"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 修复缺少PlatformCreative记录的8月9日视频，让它们进入完整工作流
清理条件: 成为项目核心工具后永久保留
"""

import sys
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.qianchuan_aw.database.database import SessionLocal
from src.qianchuan_aw.database.models import LocalCreative, PlatformCreative, AdAccount, Principal
from src.qianchuan_aw.utils.config_loader import load_settings
from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.sdk_qc.client import QianchuanClient
from src.qianchuan_aw.utils.workflow_helpers import find_or_create_local_creative


class MissingPlatformCreativeRepairer:
    """修复缺少PlatformCreative记录的素材"""
    
    def __init__(self):
        self.app_settings = load_settings()
        self.clients_by_principal = {}
        self.repair_stats = {
            'found_missing': 0,
            'created_records': 0,
            'triggered_uploads': 0,
            'fixed_status': 0,
            'errors': 0
        }
        
        logger.info("🔧 初始化PlatformCreative记录修复器")
    
    def get_client_for_principal(self, principal_id: int) -> QianchuanClient:
        """为指定主体获取API客户端"""
        if principal_id not in self.clients_by_principal:
            self.clients_by_principal[principal_id] = QianchuanClient(
                app_id=self.app_settings['api_credentials']['app_id'],
                secret=self.app_settings['api_credentials']['secret'],
                principal_id=principal_id
            )
        return self.clients_by_principal[principal_id]
    
    def find_missing_platform_creatives(self, db) -> List[Dict[str, Any]]:
        """查找缺少PlatformCreative记录的素材"""
        
        logger.info("🔍 查找缺少PlatformCreative记录的8月9日视频...")
        
        # 查询缺少PlatformCreative记录的LocalCreative
        missing_creatives = db.query(LocalCreative).outerjoin(PlatformCreative).filter(
            PlatformCreative.id.is_(None),  # 没有PlatformCreative记录
            LocalCreative.filename.like('%8.9-%') | LocalCreative.filename.like('%7.24%')  # 8月9日或7月24日视频
        ).all()
        
        result = []
        for creative in missing_creatives:
            result.append({
                'id': creative.id,
                'filename': creative.filename,
                'file_path': creative.file_path,
                'status': creative.status,
                'principal_id': creative.principal_id,
                'principal_name': creative.principal.name if creative.principal else 'Unknown'
            })
        
        self.repair_stats['found_missing'] = len(result)
        logger.info(f"📊 找到 {len(result)} 个缺少PlatformCreative记录的素材")
        
        return result
    
    def create_platform_creative_from_api(self, db, creative_info: Dict[str, Any]) -> bool:
        """通过API查询为已上传素材创建PlatformCreative记录"""
        
        try:
            creative_id = creative_info['id']
            filename = creative_info['filename']
            principal_id = creative_info['principal_id']
            
            logger.info(f"🔍 为素材 {filename} 查询API状态...")
            
            # 获取API客户端
            client = self.get_client_for_principal(principal_id)
            
            # 获取该主体下的测试账户
            test_accounts = db.query(AdAccount).filter(
                AdAccount.principal_id == principal_id,
                AdAccount.account_type == 'TEST',
                AdAccount.status == 'active'
            ).all()
            
            if not test_accounts:
                logger.warning(f"❌ 主体 {principal_id} 没有可用的测试账户")
                return False
            
            # 尝试在各个账户中查找这个素材
            for account in test_accounts:
                try:
                    # 查询账户的素材库
                    library_response = client.get_library_videos(
                        advertiser_id=account.account_id_qc,
                        filtering={"video_name": filename.replace('.mp4', '')}
                    )
                    
                    if library_response and 'list' in library_response:
                        videos = library_response['list']
                        
                        # 查找匹配的视频
                        for video in videos:
                            if video.get('title') == filename or video.get('title') == filename.replace('.mp4', ''):
                                # 找到匹配的视频，创建PlatformCreative记录
                                platform_creative = PlatformCreative(
                                    local_creative_id=creative_id,
                                    account_id=account.id,
                                    material_id_qc=str(video['material_id']),
                                    video_id=video['video_id'],
                                    video_url=video.get('url'),
                                    video_cover_id=video.get('cover_image_id'),
                                    review_status=video.get('audit_status', 'UNKNOWN')
                                )
                                
                                db.add(platform_creative)
                                db.commit()
                                
                                logger.success(f"✅ 为素材 {filename} 创建PlatformCreative记录 (material_id: {video['material_id']})")
                                self.repair_stats['created_records'] += 1
                                return True
                
                except Exception as e:
                    logger.debug(f"在账户 {account.name} 中查找素材失败: {e}")
                    continue
            
            logger.warning(f"⚠️ 未在任何账户中找到素材 {filename}")
            return False
            
        except Exception as e:
            logger.error(f"❌ 为素材 {filename} 创建PlatformCreative记录失败: {e}")
            self.repair_stats['errors'] += 1
            return False
    
    def trigger_upload_for_pending_materials(self, db, creative_info: Dict[str, Any]) -> bool:
        """为pending_upload状态的素材触发上传"""
        
        try:
            creative_id = creative_info['id']
            filename = creative_info['filename']
            file_path = creative_info['file_path']
            principal_id = creative_info['principal_id']
            
            logger.info(f"🚀 为素材 {filename} 触发上传任务...")
            
            # 获取该主体下的测试账户
            test_accounts = db.query(AdAccount).filter(
                AdAccount.principal_id == principal_id,
                AdAccount.account_type == 'TEST',
                AdAccount.status == 'active'
            ).limit(1).all()
            
            if not test_accounts:
                logger.warning(f"❌ 主体 {principal_id} 没有可用的测试账户")
                return False
            
            target_account = test_accounts[0]
            
            # 触发上传任务
            from src.qianchuan_aw.workflows.tasks import upload_single_video
            upload_single_video.delay(
                local_creative_id=creative_id,
                account_id=target_account.id,
                file_path=file_path,
                principal_name=creative_info['principal_name']
            )
            
            logger.success(f"✅ 为素材 {filename} 触发上传任务到账户 {target_account.name}")
            self.repair_stats['triggered_uploads'] += 1
            return True
            
        except Exception as e:
            logger.error(f"❌ 为素材 {filename} 触发上传失败: {e}")
            self.repair_stats['errors'] += 1
            return False
    
    def fix_status_anomalies(self, db, creative_info: Dict[str, Any]) -> bool:
        """修复状态异常的素材"""
        
        try:
            creative_id = creative_info['id']
            filename = creative_info['filename']
            file_path = creative_info['file_path']
            status = creative_info['status']
            
            # 如果是uploaded_pending_plan状态但在01_materials_to_process目录，修复状态
            if status == 'uploaded_pending_plan' and '01_materials_to_process' in file_path:
                creative = db.get(LocalCreative, creative_id)
                creative.status = 'pending_upload'
                db.commit()
                
                logger.info(f"🔧 修复素材 {filename} 状态: uploaded_pending_plan → pending_upload")
                self.repair_stats['fixed_status'] += 1
                return True
                
        except Exception as e:
            logger.error(f"❌ 修复素材 {filename} 状态失败: {e}")
            self.repair_stats['errors'] += 1
            return False
    
    def repair_missing_records(self):
        """执行修复操作"""
        
        logger.info("🚀 开始修复缺少PlatformCreative记录的素材")
        logger.info("=" * 80)
        
        with SessionLocal() as db:
            # 1. 查找缺少记录的素材
            missing_creatives = self.find_missing_platform_creatives(db)
            
            if not missing_creatives:
                logger.info("✅ 没有发现缺少PlatformCreative记录的素材")
                return
            
            # 2. 按状态分类处理
            pending_upload_creatives = [c for c in missing_creatives if c['status'] == 'pending_upload']
            uploaded_pending_plan_creatives = [c for c in missing_creatives if c['status'] == 'uploaded_pending_plan']
            
            logger.info(f"📊 素材分类统计:")
            logger.info(f"   pending_upload: {len(pending_upload_creatives)} 个")
            logger.info(f"   uploaded_pending_plan: {len(uploaded_pending_plan_creatives)} 个")
            
            # 3. 处理已上传但缺少记录的素材
            logger.info(f"\n🔧 处理已上传但缺少PlatformCreative记录的素材...")
            for creative_info in uploaded_pending_plan_creatives:
                # 先修复状态异常
                if '01_materials_to_process' in creative_info['file_path']:
                    self.fix_status_anomalies(db, creative_info)
                    continue
                
                # 为已上传素材创建记录
                self.create_platform_creative_from_api(db, creative_info)
            
            # 4. 触发未上传素材的上传
            logger.info(f"\n🚀 触发未上传素材的上传任务...")
            for creative_info in pending_upload_creatives[:10]:  # 先处理10个，避免过载
                self.trigger_upload_for_pending_materials(db, creative_info)
        
        # 5. 输出修复统计
        self.print_repair_summary()
    
    def print_repair_summary(self):
        """输出修复统计"""
        
        logger.info(f"\n📊 PlatformCreative记录修复统计")
        logger.info("=" * 80)
        logger.info(f"⏰ 修复时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"🎯 修复范围: 8月9日和7月24日视频素材")
        
        logger.info(f"\n📋 修复结果:")
        logger.info(f"   发现缺少记录: {self.repair_stats['found_missing']} 个")
        logger.info(f"   创建PlatformCreative记录: {self.repair_stats['created_records']} 个")
        logger.info(f"   触发上传任务: {self.repair_stats['triggered_uploads']} 个")
        logger.info(f"   修复状态异常: {self.repair_stats['fixed_status']} 个")
        logger.info(f"   处理错误: {self.repair_stats['errors']} 个")
        
        success_rate = ((self.repair_stats['created_records'] + self.repair_stats['triggered_uploads'] + self.repair_stats['fixed_status']) / 
                       max(self.repair_stats['found_missing'], 1)) * 100
        
        logger.info(f"\n✅ 修复成功率: {success_rate:.1f}%")
        
        if self.repair_stats['triggered_uploads'] > 0:
            logger.info(f"\n💡 后续操作建议:")
            logger.info(f"   1. 监控上传任务进度")
            logger.info(f"   2. 等待上传完成后检查PlatformCreative记录")
            logger.info(f"   3. 运行计划创建任务")
            logger.info(f"   4. 执行完整工作流验证")


def main():
    """主函数"""
    
    logger.info("🚨 修复缺少PlatformCreative记录的8月9日视频")
    logger.info("=" * 80)
    
    logger.info("🎯 修复目标:")
    logger.info("   1. 为已上传素材创建PlatformCreative记录")
    logger.info("   2. 触发未上传素材的上传任务")
    logger.info("   3. 修复状态异常的素材")
    logger.info("   4. 让所有8月9日视频进入完整工作流")
    
    try:
        repairer = MissingPlatformCreativeRepairer()
        repairer.repair_missing_records()
        
        logger.success("🎉 PlatformCreative记录修复完成！")
        logger.info("💡 现在这些视频应该可以正常进入工作流了")
        
    except Exception as e:
        logger.error(f"❌ 修复过程发生严重错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
