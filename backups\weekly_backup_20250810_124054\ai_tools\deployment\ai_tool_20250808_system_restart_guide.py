#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 千川自动化项目系统重启和部署指南
清理条件: 成为项目核心部署工具，长期保留
"""

import os
import sys
import time
from datetime import datetime, timezone

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger


class SystemRestartGuide:
    """系统重启和部署指南"""
    
    def __init__(self):
        self.restart_steps = []
        self.verification_steps = []
        self.monitoring_steps = []
    
    def generate_restart_guide(self):
        """生成系统重启指南"""
        logger.info("🚀 千川自动化项目系统重启指南 v2.0")
        logger.info("="*100)
        
        # 重启前准备
        logger.info("📋 第一步：重启前准备")
        logger.info("-" * 50)
        logger.info("1. 🛑 停止现有Celery服务")
        logger.info("   - 在运行Celery Worker的终端按 Ctrl+C")
        logger.info("   - 在运行Celery Beat的终端按 Ctrl+C")
        logger.info("   - 等待所有进程优雅关闭")
        
        logger.info("\n2. 🧹 清理残留进程")
        logger.info("   - 检查是否有残留的Celery进程")
        logger.info("   - 检查是否有残留的浏览器进程")
        logger.info("   - 必要时手动终止")
        
        logger.info("\n3. 💾 备份当前状态")
        logger.info("   - 备份数据库（可选）")
        logger.info("   - 记录当前素材状态分布")
        
        # 重启步骤
        logger.info("\n📋 第二步：系统重启")
        logger.info("-" * 50)
        logger.info("1. 🚀 启动Celery Beat（新版本）")
        logger.info("   命令: python run_celery_beat.py")
        logger.info("   预期: 看到 '💓 Beat心跳机制已启动' 消息")
        
        logger.info("\n2. 🚀 启动Celery Worker（新版本）")
        logger.info("   命令: python run_celery_worker.py")
        logger.info("   预期: 看到 '✅ Celery生命周期管理器已启动' 消息")
        
        logger.info("\n3. ⏱️ 等待系统稳定")
        logger.info("   - 等待2-3分钟让系统完全启动")
        logger.info("   - 观察日志输出是否正常")
        
        # 验证步骤
        logger.info("\n📋 第三步：系统验证")
        logger.info("-" * 50)
        logger.info("1. 📊 运行性能监控")
        logger.info("   命令: python ai_tools/monitoring/ai_tool_20250808_performance_metrics_dashboard.py")
        
        logger.info("\n2. 🔍 检查批量任务执行")
        logger.info("   - 观察是否有batch_upload_videos任务执行")
        logger.info("   - 观察是否有batch_create_plans任务执行")
        logger.info("   - 检查任务执行日志")
        
        logger.info("\n3. 📈 监控处理效果")
        logger.info("   - 观察pending_upload数量是否减少")
        logger.info("   - 观察uploaded_pending_plan数量是否增加")
        logger.info("   - 检查是否有新的processing状态积压")
        
        # 性能预期
        logger.info("\n📋 第四步：性能预期")
        logger.info("-" * 50)
        logger.info("🎯 预期改进效果:")
        logger.info("   • 上传效率: 从10个/小时 → 50-100个/小时")
        logger.info("   • 状态一致性: 从61% → 100%")
        logger.info("   • 浏览器进程: 稳定在3-5个")
        logger.info("   • 内存使用: 降低60-80%")
        logger.info("   • 系统稳定性: 99%+")
        
        # 监控指标
        logger.info("\n📋 第五步：关键监控指标")
        logger.info("-" * 50)
        logger.info("🔍 需要监控的关键指标:")
        logger.info("   • processing状态数量: 应该保持在0-5个")
        logger.info("   • 浏览器进程数量: 应该保持在3-5个")
        logger.info("   • 上传成功率: 应该达到90%+")
        logger.info("   • 任务执行延迟: 应该在正常范围内")
        
        # 问题排查
        logger.info("\n📋 第六步：常见问题排查")
        logger.info("-" * 50)
        logger.info("🔧 如果遇到问题:")
        logger.info("   • processing状态再次积压 → 检查原子状态管理器集成")
        logger.info("   • 浏览器进程过多 → 检查浏览器进程池配置")
        logger.info("   • 批量任务不执行 → 检查Celery Beat配置")
        logger.info("   • 上传效率仍然低 → 检查网络和API限制")
        
        # 回滚方案
        logger.info("\n📋 第七步：紧急回滚方案")
        logger.info("-" * 50)
        logger.info("🚨 如果系统出现严重问题:")
        logger.info("   1. 立即停止Celery服务")
        logger.info("   2. 恢复原始的tasks.py和celery_app.py")
        logger.info("   3. 重启原有系统")
        logger.info("   4. 分析问题原因")
        
        return {
            'guide_generated': True,
            'ready_for_restart': True,
            'expected_improvements': {
                'upload_efficiency': '5-10x faster',
                'state_consistency': '100%',
                'resource_usage': '60-80% reduction',
                'system_stability': '99%+'
            }
        }
    
    def show_current_system_status(self):
        """显示当前系统状态"""
        logger.info("\n📊 当前系统状态检查")
        logger.info("="*80)
        
        try:
            from qianchuan_aw.database.database import SessionLocal
            from qianchuan_aw.database.models import LocalCreative
            
            with SessionLocal() as db:
                # 状态分布
                status_counts = {}
                all_creatives = db.query(LocalCreative).all()
                
                for creative in all_creatives:
                    status = creative.status or 'NULL'
                    status_counts[status] = status_counts.get(status, 0) + 1
                
                logger.info("📈 素材状态分布:")
                for status, count in sorted(status_counts.items()):
                    if count > 0:
                        status_icon = {
                            'pending_upload': '📤',
                            'processing': '⏳',
                            'uploaded_pending_plan': '📋',
                            'testing_pending_review': '🔍',
                            'approved': '✅',
                            'rejected': '❌',
                            'upload_failed': '💀'
                        }.get(status, '📄')
                        
                        logger.info(f"   {status_icon} {status}: {count} 个")
                
                # 关键指标
                processing_count = status_counts.get('processing', 0)
                pending_count = status_counts.get('pending_upload', 0)
                uploaded_count = status_counts.get('uploaded_pending_plan', 0)
                
                logger.info(f"\n🎯 关键指标:")
                logger.info(f"   待处理积压: {processing_count} 个 {'✅' if processing_count == 0 else '⚠️'}")
                logger.info(f"   待上传素材: {pending_count} 个")
                logger.info(f"   待创建计划: {uploaded_count} 个")
                
                # 系统就绪状态
                system_ready = processing_count == 0 and (pending_count > 0 or uploaded_count > 0)
                
                if system_ready:
                    logger.success("✅ 系统状态良好，已就绪重启")
                else:
                    logger.warning("⚠️ 系统状态需要关注")
                
                return {
                    'status_distribution': status_counts,
                    'processing_backlog': processing_count,
                    'pending_upload': pending_count,
                    'uploaded_pending_plan': uploaded_count,
                    'system_ready': system_ready
                }
                
        except Exception as e:
            logger.error(f"❌ 系统状态检查失败: {e}")
            return {'system_ready': False, 'error': str(e)}
    
    def show_restart_commands(self):
        """显示重启命令"""
        logger.info("\n🚀 系统重启命令")
        logger.info("="*80)
        
        logger.info("📋 重启步骤:")
        logger.info("1. 停止现有服务（如果正在运行）")
        logger.info("   - 在Celery Worker终端按 Ctrl+C")
        logger.info("   - 在Celery Beat终端按 Ctrl+C")
        
        logger.info("\n2. 启动新版本服务")
        logger.info("   🔹 终端1 - 启动Beat:")
        logger.info("   python run_celery_beat.py")
        
        logger.info("\n   🔹 终端2 - 启动Worker:")
        logger.info("   python run_celery_worker.py")
        
        logger.info("\n3. 验证系统运行")
        logger.info("   🔹 终端3 - 监控性能:")
        logger.info("   python ai_tools/monitoring/ai_tool_20250808_performance_metrics_dashboard.py")
        
        logger.info("\n4. 观察处理效果")
        logger.info("   - 观察日志中的批量任务执行")
        logger.info("   - 监控素材状态变化")
        logger.info("   - 检查浏览器进程数量")
        
        logger.info("\n💡 预期看到的日志:")
        logger.info("   • '💓 Beat心跳机制已启动'")
        logger.info("   • '✅ Celery生命周期管理器已启动'")
        logger.info("   • '🚀 [Batch Task Start] 批量上传视频'")
        logger.info("   • '✅ 安全派发上传任务'")


def main():
    """主函数"""
    guide = SystemRestartGuide()
    
    # 显示当前系统状态
    status = guide.show_current_system_status()
    
    # 生成重启指南
    restart_info = guide.generate_restart_guide()
    
    # 显示重启命令
    guide.show_restart_commands()
    
    # 最终建议
    logger.info("\n🎊 第二阶段实施完成总结")
    logger.info("="*80)
    
    if status and status.get('system_ready'):
        logger.success("✅ 系统状态优秀，已完全就绪重启")
        logger.success("🚀 预期效果：上传效率提升5-10倍")
        logger.success("📊 建议立即重启服务，开始高效批量处理")
        return 0
    else:
        logger.warning("⚠️ 系统基本就绪，但建议先解决发现的问题")
        return 1


if __name__ == "__main__":
    exit(main())
