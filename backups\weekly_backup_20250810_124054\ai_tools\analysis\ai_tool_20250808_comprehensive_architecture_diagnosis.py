#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 千川自动化项目Celery工作流架构深度诊断
清理条件: 成为项目核心分析工具，长期保留
"""

import os
import sys
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Tuple
from dataclasses import dataclass

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.database import SessionLocal
from qianchuan_aw.database.models import LocalCreative, Campaign, AdAccount
from sqlalchemy import text


@dataclass
class TaskDependency:
    """任务依赖关系"""
    task_name: str
    depends_on: List[str]
    triggers: List[str]
    outputs: List[str]
    interval_seconds: int
    potential_conflicts: List[str]


@dataclass
class PerformanceBottleneck:
    """性能瓶颈"""
    component: str
    issue: str
    impact: str
    severity: str  # LOW, MEDIUM, HIGH, CRITICAL
    evidence: List[str]


@dataclass
class ArchitecturalFlaw:
    """架构缺陷"""
    category: str
    description: str
    root_cause: str
    consequences: List[str]
    fix_complexity: str  # LOW, MEDIUM, HIGH


class ComprehensiveArchitectureDiagnostic:
    """千川自动化项目架构综合诊断器"""
    
    def __init__(self):
        self.task_dependencies = []
        self.performance_bottlenecks = []
        self.architectural_flaws = []
        self.database_inconsistencies = []
        
    def run_comprehensive_diagnosis(self) -> Dict[str, Any]:
        """运行全面的架构诊断"""
        logger.info("🔍 开始千川自动化项目架构深度诊断")
        logger.info("="*100)
        
        diagnosis_results = {
            'timestamp': datetime.now(timezone.utc),
            'task_dependency_analysis': self._analyze_task_dependencies(),
            'performance_bottleneck_analysis': self._analyze_performance_bottlenecks(),
            'architectural_flaw_analysis': self._analyze_architectural_flaws(),
            'database_consistency_analysis': self._analyze_database_consistency(),
            'celery_lifecycle_analysis': self._analyze_celery_lifecycle_issues(),
            'upload_efficiency_analysis': self._analyze_upload_efficiency(),
            'critical_issues': [],
            'recommendations': []
        }
        
        # 汇总关键问题
        self._summarize_critical_issues(diagnosis_results)
        
        # 生成诊断报告
        self._generate_comprehensive_report(diagnosis_results)
        
        return diagnosis_results
    
    def _analyze_task_dependencies(self) -> Dict[str, Any]:
        """分析任务间依赖和冲突"""
        logger.info("📊 分析任务间依赖和冲突...")
        
        # 定义当前任务依赖关系
        task_flow = {
            'file_ingestion': TaskDependency(
                task_name='file_ingestion',
                depends_on=[],
                triggers=['new文件'],
                outputs=['pending_grouping状态素材'],
                interval_seconds=300,  # 5分钟
                potential_conflicts=['重复扫描', '文件锁定冲突']
            ),
            'group_dispatch': TaskDependency(
                task_name='group_dispatch',
                depends_on=['file_ingestion'],
                triggers=['pending_grouping状态素材'],
                outputs=['processing状态素材', 'upload_single_video任务'],
                interval_seconds=60,   # 1分钟
                potential_conflicts=['重复派发', '状态竞争']
            ),
            'upload_single_video': TaskDependency(
                task_name='upload_single_video',
                depends_on=['group_dispatch'],
                triggers=['Celery队列任务'],
                outputs=['uploaded_pending_plan状态'],
                interval_seconds=0,    # 异步执行
                potential_conflicts=['浏览器进程冲突', 'API限制冲突', '数据库锁定']
            ),
            'plan_creation': TaskDependency(
                task_name='plan_creation',
                depends_on=['upload_single_video'],
                triggers=['uploaded_pending_plan状态素材'],
                outputs=['testing_pending_review状态'],
                interval_seconds=120,  # 2分钟
                potential_conflicts=['素材状态不一致']
            ),
            'plan_appeal': TaskDependency(
                task_name='plan_appeal',
                depends_on=['plan_creation'],
                triggers=['rejected计划'],
                outputs=['appeal_pending状态'],
                interval_seconds=900,  # 15分钟
                potential_conflicts=['浏览器进程占用', '测试账户限制冲突']
            ),
            'material_monitoring': TaskDependency(
                task_name='material_monitoring',
                depends_on=['plan_creation', 'plan_appeal'],
                triggers=['testing_pending_review状态', 'appeal_pending状态'],
                outputs=['approved/rejected状态'],
                interval_seconds=300,  # 5分钟
                potential_conflicts=['浏览器进程占用', '状态查询冲突']
            )
        }
        
        # 分析依赖冲突
        dependency_conflicts = []
        
        # 检查间隔时间冲突
        for task_name, task in task_flow.items():
            if task.interval_seconds > 0:
                for dep_name in task.depends_on:
                    dep_task = task_flow.get(dep_name)
                    if dep_task and dep_task.interval_seconds > 0:
                        if task.interval_seconds < dep_task.interval_seconds:
                            dependency_conflicts.append({
                                'type': 'INTERVAL_CONFLICT',
                                'task': task_name,
                                'dependency': dep_name,
                                'issue': f'{task_name}间隔({task.interval_seconds}s) < {dep_name}间隔({dep_task.interval_seconds}s)',
                                'impact': '可能导致任务等待依赖数据'
                            })
        
        # 检查资源竞争
        browser_tasks = ['upload_single_video', 'plan_appeal', 'material_monitoring']
        for i, task1 in enumerate(browser_tasks):
            for task2 in browser_tasks[i+1:]:
                dependency_conflicts.append({
                    'type': 'RESOURCE_CONFLICT',
                    'task': task1,
                    'conflict_with': task2,
                    'issue': '浏览器进程资源竞争',
                    'impact': '可能导致进程泄漏和性能下降'
                })
        
        return {
            'task_flow': {name: {
                'depends_on': task.depends_on,
                'interval_seconds': task.interval_seconds,
                'potential_conflicts': task.potential_conflicts
            } for name, task in task_flow.items()},
            'dependency_conflicts': dependency_conflicts,
            'critical_paths': ['file_ingestion → group_dispatch → upload_single_video → plan_creation'],
            'bottleneck_tasks': ['upload_single_video', 'material_monitoring']
        }
    
    def _analyze_performance_bottlenecks(self) -> Dict[str, Any]:
        """分析性能瓶颈"""
        logger.info("⚡ 分析性能瓶颈...")
        
        bottlenecks = []
        
        # 分析数据库状态
        with SessionLocal() as db:
            # 检查processing状态积压
            processing_count = db.query(LocalCreative).filter(
                LocalCreative.status == 'processing'
            ).count()
            
            if processing_count > 100:
                bottlenecks.append(PerformanceBottleneck(
                    component='upload_pipeline',
                    issue=f'{processing_count}个素材卡在processing状态',
                    impact='上传效率严重低下，资源浪费',
                    severity='CRITICAL',
                    evidence=[
                        f'processing状态素材数量: {processing_count}',
                        '可能原因: 上传任务失败但状态未回滚',
                        '影响: 新素材无法正常处理'
                    ]
                ))
            
            # 检查upload_failed比例
            total_creatives = db.query(LocalCreative).count()
            failed_count = db.query(LocalCreative).filter(
                LocalCreative.status == 'upload_failed'
            ).count()
            
            if failed_count > 0:
                failure_rate = (failed_count / total_creatives) * 100
                bottlenecks.append(PerformanceBottleneck(
                    component='upload_reliability',
                    issue=f'上传失败率: {failure_rate:.2f}%',
                    impact='素材处理成功率低',
                    severity='HIGH' if failure_rate > 5 else 'MEDIUM',
                    evidence=[
                        f'失败素材数量: {failed_count}',
                        f'总素材数量: {total_creatives}',
                        '需要分析失败原因'
                    ]
                ))
        
        # 浏览器进程瓶颈
        bottlenecks.append(PerformanceBottleneck(
            component='browser_management',
            issue='浏览器进程管理混乱',
            impact='系统资源耗尽，任务执行缓慢',
            severity='HIGH',
            evidence=[
                '多个任务同时启动浏览器进程',
                '进程清理机制不完善',
                '内存泄漏和CPU占用过高'
            ]
        ))
        
        # API调用效率瓶颈
        bottlenecks.append(PerformanceBottleneck(
            component='api_efficiency',
            issue='千川API调用效率低',
            impact='上传速度慢，任务积压',
            severity='HIGH',
            evidence=[
                '每次上传都需要启动新的浏览器会话',
                '缺乏连接池和会话复用',
                '没有批量操作优化'
            ]
        ))
        
        return {
            'bottlenecks': [
                {
                    'component': b.component,
                    'issue': b.issue,
                    'impact': b.impact,
                    'severity': b.severity,
                    'evidence': b.evidence
                } for b in bottlenecks
            ],
            'critical_bottlenecks': [b for b in bottlenecks if b.severity == 'CRITICAL'],
            'performance_score': self._calculate_performance_score(bottlenecks)
        }
    
    def _analyze_architectural_flaws(self) -> Dict[str, Any]:
        """分析架构设计缺陷"""
        logger.info("🏗️ 分析架构设计缺陷...")
        
        flaws = []
        
        # 状态管理缺陷
        flaws.append(ArchitecturalFlaw(
            category='状态管理',
            description='素材状态转换缺乏原子性保证',
            root_cause='状态更新和任务派发分离，存在竞态条件',
            consequences=[
                'processing状态素材大量积压',
                '重复处理和资源浪费',
                '状态不一致导致工作流卡死'
            ],
            fix_complexity='HIGH'
        ))
        
        # 任务调度缺陷
        flaws.append(ArchitecturalFlaw(
            category='任务调度',
            description='Celery Beat停止后Worker仍运行1小时',
            root_cause='Worker进程没有正确监听Beat状态，缺乏优雅关闭机制',
            consequences=[
                '资源浪费',
                '任务执行不可控',
                '系统状态不一致'
            ],
            fix_complexity='MEDIUM'
        ))
        
        # 资源管理缺陷
        flaws.append(ArchitecturalFlaw(
            category='资源管理',
            description='浏览器进程生命周期管理混乱',
            root_cause='缺乏统一的浏览器进程池，每个任务独立创建进程',
            consequences=[
                '内存泄漏',
                '进程数量失控',
                '系统性能下降'
            ],
            fix_complexity='HIGH'
        ))
        
        # 错误处理缺陷
        flaws.append(ArchitecturalFlaw(
            category='错误处理',
            description='任务失败后状态回滚机制不完善',
            root_cause='异常处理逻辑分散，缺乏统一的错误恢复策略',
            consequences=[
                '失败任务状态残留',
                '资源无法释放',
                '工作流中断'
            ],
            fix_complexity='MEDIUM'
        ))
        
        # 并发控制缺陷
        flaws.append(ArchitecturalFlaw(
            category='并发控制',
            description='缺乏有效的并发限制和负载均衡',
            root_cause='任务派发没有考虑系统负载和资源限制',
            consequences=[
                '系统过载',
                '任务执行效率低',
                '资源竞争激烈'
            ],
            fix_complexity='HIGH'
        ))
        
        return {
            'flaws': [
                {
                    'category': f.category,
                    'description': f.description,
                    'root_cause': f.root_cause,
                    'consequences': f.consequences,
                    'fix_complexity': f.fix_complexity
                } for f in flaws
            ],
            'critical_flaws': [f for f in flaws if f.fix_complexity == 'HIGH'],
            'architecture_score': self._calculate_architecture_score(flaws)
        }
    
    def _analyze_database_consistency(self) -> Dict[str, Any]:
        """分析数据库状态一致性"""
        logger.info("🗄️ 分析数据库状态一致性...")
        
        inconsistencies = []
        
        with SessionLocal() as db:
            # 检查长期processing状态
            long_processing = db.execute(text("""
                SELECT COUNT(*) as count
                FROM local_creatives 
                WHERE status = 'processing' 
                AND created_at < NOW() - INTERVAL '1 hour'
            """)).fetchone()
            
            if long_processing.count > 0:
                inconsistencies.append({
                    'type': 'STALE_PROCESSING',
                    'count': long_processing.count,
                    'description': '长期处于processing状态的素材',
                    'impact': '阻塞新素材处理'
                })
            
            # 检查孤儿状态
            orphan_uploaded = db.execute(text("""
                SELECT COUNT(*) as count
                FROM local_creatives lc
                LEFT JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                WHERE lc.status = 'uploaded_pending_plan' 
                AND pc.id IS NULL
            """)).fetchone()
            
            if orphan_uploaded.count > 0:
                inconsistencies.append({
                    'type': 'ORPHAN_UPLOADED',
                    'count': orphan_uploaded.count,
                    'description': '标记为已上传但无平台记录的素材',
                    'impact': '数据不一致，影响后续处理'
                })
        
        return {
            'inconsistencies': inconsistencies,
            'consistency_score': self._calculate_consistency_score(inconsistencies)
        }
    
    def _analyze_celery_lifecycle_issues(self) -> Dict[str, Any]:
        """分析Celery生命周期问题"""
        logger.info("🔄 分析Celery生命周期问题...")
        
        lifecycle_issues = [
            {
                'component': 'celery_beat',
                'issue': 'Beat停止后Worker仍运行',
                'root_cause': 'Worker没有监听Beat状态变化',
                'solution': '实现Worker优雅关闭机制'
            },
            {
                'component': 'task_retry',
                'issue': '任务重试机制不完善',
                'root_cause': '重试逻辑没有考虑状态回滚',
                'solution': '实现原子性重试机制'
            },
            {
                'component': 'task_timeout',
                'issue': '长时间运行任务缺乏超时控制',
                'root_cause': '没有设置合理的任务超时时间',
                'solution': '为每个任务设置适当的超时时间'
            }
        ]
        
        return {
            'lifecycle_issues': lifecycle_issues,
            'critical_issues': [issue for issue in lifecycle_issues if 'Worker' in issue['issue']]
        }
    
    def _analyze_upload_efficiency(self) -> Dict[str, Any]:
        """分析上传效率问题"""
        logger.info("📈 分析上传效率问题...")
        
        efficiency_issues = [
            {
                'category': '浏览器启动开销',
                'issue': '每次上传都启动新的浏览器进程',
                'impact': '启动时间占用大量资源',
                'optimization': '实现浏览器进程池'
            },
            {
                'category': 'API调用效率',
                'issue': '缺乏批量上传机制',
                'impact': '单个文件上传效率低',
                'optimization': '实现批量上传API'
            },
            {
                'category': '并发控制',
                'issue': '并发上传数量不受控制',
                'impact': '系统过载，反而降低效率',
                'optimization': '实现智能并发控制'
            },
            {
                'category': '错误恢复',
                'issue': '上传失败后需要重新开始',
                'impact': '浪费已完成的工作',
                'optimization': '实现断点续传机制'
            }
        ]
        
        return {
            'efficiency_issues': efficiency_issues,
            'estimated_improvement': '优化后上传效率可提升5-10倍'
        }
    
    def _calculate_performance_score(self, bottlenecks: List[PerformanceBottleneck]) -> int:
        """计算性能评分"""
        if not bottlenecks:
            return 100
        
        penalty = 0
        for bottleneck in bottlenecks:
            if bottleneck.severity == 'CRITICAL':
                penalty += 40
            elif bottleneck.severity == 'HIGH':
                penalty += 25
            elif bottleneck.severity == 'MEDIUM':
                penalty += 15
            else:
                penalty += 5
        
        return max(0, 100 - penalty)
    
    def _calculate_architecture_score(self, flaws: List[ArchitecturalFlaw]) -> int:
        """计算架构评分"""
        if not flaws:
            return 100
        
        penalty = 0
        for flaw in flaws:
            if flaw.fix_complexity == 'HIGH':
                penalty += 30
            elif flaw.fix_complexity == 'MEDIUM':
                penalty += 20
            else:
                penalty += 10
        
        return max(0, 100 - penalty)
    
    def _calculate_consistency_score(self, inconsistencies: List[Dict]) -> int:
        """计算一致性评分"""
        if not inconsistencies:
            return 100
        
        penalty = len(inconsistencies) * 20
        return max(0, 100 - penalty)
    
    def _summarize_critical_issues(self, results: Dict[str, Any]):
        """汇总关键问题"""
        critical_issues = []
        
        # 性能关键问题
        perf_analysis = results['performance_bottleneck_analysis']
        for bottleneck in perf_analysis['critical_bottlenecks']:
            critical_issues.append({
                'category': 'PERFORMANCE',
                'issue': bottleneck['issue'],
                'impact': bottleneck['impact'],
                'priority': 'URGENT'
            })
        
        # 架构关键问题
        arch_analysis = results['architectural_flaw_analysis']
        for flaw in arch_analysis['critical_flaws']:
            critical_issues.append({
                'category': 'ARCHITECTURE',
                'issue': flaw['description'],
                'impact': ', '.join(flaw['consequences']),
                'priority': 'HIGH'
            })
        
        results['critical_issues'] = critical_issues
    
    def _generate_comprehensive_report(self, results: Dict[str, Any]):
        """生成综合诊断报告"""
        logger.info("\n" + "="*100)
        logger.info("📋 千川自动化项目架构综合诊断报告")
        logger.info("="*100)
        
        # 总体评分
        perf_score = results['performance_bottleneck_analysis']['performance_score']
        arch_score = results['architectural_flaw_analysis']['architecture_score']
        consistency_score = results['database_consistency_analysis']['consistency_score']
        overall_score = (perf_score + arch_score + consistency_score) / 3
        
        logger.info(f"🎯 总体健康评分: {overall_score:.1f}/100")
        logger.info(f"   性能评分: {perf_score}/100")
        logger.info(f"   架构评分: {arch_score}/100")
        logger.info(f"   一致性评分: {consistency_score}/100")
        
        # 关键问题
        critical_issues = results['critical_issues']
        if critical_issues:
            logger.error(f"\n🚨 发现 {len(critical_issues)} 个关键问题:")
            for i, issue in enumerate(critical_issues, 1):
                logger.error(f"   {i}. [{issue['category']}] {issue['issue']}")
                logger.error(f"      影响: {issue['impact']}")
        
        # 性能瓶颈
        bottlenecks = results['performance_bottleneck_analysis']['bottlenecks']
        logger.info(f"\n⚡ 性能瓶颈分析 ({len(bottlenecks)} 个):")
        for bottleneck in bottlenecks:
            severity_icon = {"CRITICAL": "🔴", "HIGH": "🟠", "MEDIUM": "🟡", "LOW": "🟢"}
            icon = severity_icon.get(bottleneck['severity'], "⚪")
            logger.info(f"   {icon} {bottleneck['component']}: {bottleneck['issue']}")
        
        # 架构缺陷
        flaws = results['architectural_flaw_analysis']['flaws']
        logger.info(f"\n🏗️ 架构缺陷分析 ({len(flaws)} 个):")
        for flaw in flaws:
            complexity_icon = {"HIGH": "🔴", "MEDIUM": "🟡", "LOW": "🟢"}
            icon = complexity_icon.get(flaw['fix_complexity'], "⚪")
            logger.info(f"   {icon} {flaw['category']}: {flaw['description']}")
        
        # 数据库一致性
        inconsistencies = results['database_consistency_analysis']['inconsistencies']
        if inconsistencies:
            logger.info(f"\n🗄️ 数据库一致性问题 ({len(inconsistencies)} 个):")
            for inconsistency in inconsistencies:
                logger.info(f"   ❌ {inconsistency['description']}: {inconsistency['count']} 个")


def main():
    """主函数"""
    diagnostic = ComprehensiveArchitectureDiagnostic()
    results = diagnostic.run_comprehensive_diagnosis()
    
    overall_score = (
        results['performance_bottleneck_analysis']['performance_score'] +
        results['architectural_flaw_analysis']['architecture_score'] +
        results['database_consistency_analysis']['consistency_score']
    ) / 3
    
    if overall_score >= 80:
        logger.success(f"✅ 架构诊断完成，总体评分: {overall_score:.1f}/100")
        return 0
    elif overall_score >= 60:
        logger.warning(f"⚠️ 架构诊断完成，总体评分: {overall_score:.1f}/100，需要优化")
        return 1
    else:
        logger.error(f"❌ 架构诊断完成，总体评分: {overall_score:.1f}/100，需要重大改进")
        return 2


if __name__ == "__main__":
    exit(main())
