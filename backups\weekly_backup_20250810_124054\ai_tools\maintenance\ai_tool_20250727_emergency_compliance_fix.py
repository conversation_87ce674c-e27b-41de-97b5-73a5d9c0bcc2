#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 紧急修复素材重复使用违规问题
清理条件: 系统完全合规后可删除

千川自动化紧急合规性修复工具
========================

发现严重问题：
- 155个素材违反"素材唯一性测试铁律"
- 445个重复计划被创建
- 最严重案例：单个素材创建6个计划

立即修复措施：
1. 识别所有违规素材和计划
2. 保留最早的计划，删除重复计划
3. 更新素材状态为已测试
4. 加强重复检查机制
5. 生成合规性报告
"""

import sys
import os
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import LocalCreative, Campaign, PlatformCreative, AdAccount
from sqlalchemy import text


class EmergencyComplianceFixer:
    """紧急合规性修复器"""
    
    def __init__(self):
        self.violations_found = []
        self.plans_to_delete = []
        self.materials_to_update = []
        self.fix_summary = {
            'total_violations': 0,
            'plans_deleted': 0,
            'materials_updated': 0,
            'errors': []
        }
    
    def analyze_violations(self) -> Dict[str, Any]:
        """分析违规情况"""
        logger.info("🔍 分析素材重复使用违规情况...")
        
        with database_session() as db:
            # 查找所有违规素材
            violation_query = text("""
                SELECT 
                    lc.id as material_id,
                    lc.filename,
                    lc.file_hash,
                    lc.status,
                    COUNT(DISTINCT c.id) as plan_count,
                    STRING_AGG(DISTINCT c.campaign_id_qc, ', ') as plan_ids,
                    STRING_AGG(DISTINCT c.id::text, ', ') as internal_plan_ids,
                    MIN(c.created_at) as earliest_plan_date
                FROM local_creatives lc
                JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                JOIN campaigns c ON cpca.campaign_id = c.id
                JOIN ad_accounts aa ON c.account_id = aa.id
                WHERE aa.account_type = 'TEST'
                GROUP BY lc.id, lc.filename, lc.file_hash, lc.status
                HAVING COUNT(DISTINCT c.id) > 1
                ORDER BY plan_count DESC
            """)
            
            result = db.execute(violation_query)
            violations = result.fetchall()
            
            for violation in violations:
                violation_info = {
                    'material_id': violation.material_id,
                    'filename': violation.filename,
                    'file_hash': violation.file_hash,
                    'current_status': violation.status,
                    'plan_count': violation.plan_count,
                    'plan_ids': violation.plan_ids.split(', '),
                    'internal_plan_ids': [int(x) for x in violation.internal_plan_ids.split(', ')],
                    'earliest_plan_date': violation.earliest_plan_date
                }
                self.violations_found.append(violation_info)
            
            self.fix_summary['total_violations'] = len(violations)
            
            logger.warning(f"🚨 发现 {len(violations)} 个素材违反铁律")
            logger.warning(f"🚨 总计 {sum(v.plan_count for v in violations)} 个重复计划")
            
            return {
                'violations_count': len(violations),
                'total_duplicate_plans': sum(v.plan_count for v in violations),
                'violations': self.violations_found
            }
    
    def prepare_fix_plan(self) -> Dict[str, Any]:
        """准备修复计划"""
        logger.info("📋 准备修复计划...")
        
        for violation in self.violations_found:
            # 对于每个违规素材，保留最早的计划，删除其他计划
            internal_plan_ids = violation['internal_plan_ids']
            
            # 找到最早的计划（保留）
            with database_session() as db:
                earliest_plan = db.query(Campaign).filter(
                    Campaign.id.in_(internal_plan_ids)
                ).order_by(Campaign.created_at.asc()).first()
                
                if earliest_plan:
                    # 其他计划标记为删除
                    plans_to_delete = [pid for pid in internal_plan_ids if pid != earliest_plan.id]
                    
                    self.plans_to_delete.extend(plans_to_delete)
                    self.materials_to_update.append({
                        'material_id': violation['material_id'],
                        'keep_plan_id': earliest_plan.id,
                        'keep_plan_qc_id': earliest_plan.campaign_id_qc,
                        'delete_plan_ids': plans_to_delete
                    })
        
        logger.info(f"📋 修复计划：保留 {len(self.materials_to_update)} 个最早计划")
        logger.info(f"📋 修复计划：删除 {len(self.plans_to_delete)} 个重复计划")
        
        return {
            'plans_to_keep': len(self.materials_to_update),
            'plans_to_delete': len(self.plans_to_delete)
        }
    
    def execute_fix(self, dry_run: bool = True) -> Dict[str, Any]:
        """执行修复"""
        if dry_run:
            logger.info("🧪 执行修复（预览模式）...")
        else:
            logger.warning("⚠️ 执行修复（实际操作）...")
        
        try:
            with database_session() as db:
                for plan_id in self.plans_to_delete:
                    if dry_run:
                        logger.info(f"[预览] 将删除计划 ID: {plan_id}")
                    else:
                        # 实际删除计划
                        plan = db.query(Campaign).filter(Campaign.id == plan_id).first()
                        if plan:
                            logger.warning(f"删除重复计划: {plan.campaign_id_qc}")
                            db.delete(plan)
                            self.fix_summary['plans_deleted'] += 1
                
                # 更新素材状态
                for material_update in self.materials_to_update:
                    material_id = material_update['material_id']
                    
                    if dry_run:
                        logger.info(f"[预览] 将更新素材 {material_id} 状态为 already_tested")
                    else:
                        # 实际更新素材状态
                        material = db.query(LocalCreative).filter(LocalCreative.id == material_id).first()
                        if material:
                            material.status = 'already_tested'
                            self.fix_summary['materials_updated'] += 1
                            logger.info(f"更新素材 {material.filename} 状态为已测试")
                
                if not dry_run:
                    db.commit()
                    logger.success("✅ 合规性修复完成")
                else:
                    logger.info("✅ 预览完成，使用 dry_run=False 执行实际修复")
        
        except Exception as e:
            logger.error(f"❌ 修复执行失败: {e}")
            self.fix_summary['errors'].append(str(e))
            if not dry_run:
                db.rollback()
        
        return self.fix_summary
    
    def generate_compliance_report(self) -> str:
        """生成合规性报告"""
        report_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        report = f"""
千川自动化系统合规性紧急修复报告
================================
报告时间: {report_time}

🚨 发现的违规情况:
- 违规素材数量: {self.fix_summary['total_violations']} 个
- 重复计划总数: {len(self.plans_to_delete)} 个
- 最严重案例: 单个素材创建6个计划

🛠️ 修复措施:
- 删除重复计划: {self.fix_summary['plans_deleted']} 个
- 更新素材状态: {self.fix_summary['materials_updated']} 个
- 保留最早计划: {len(self.materials_to_update)} 个

📊 违规素材详情:
"""
        
        for i, violation in enumerate(self.violations_found[:10], 1):
            report += f"""
{i}. 素材: {violation['filename']}
   - 素材ID: {violation['material_id']}
   - 重复计划数: {violation['plan_count']}
   - 计划IDs: {', '.join(violation['plan_ids'])}
"""
        
        if len(self.violations_found) > 10:
            report += f"\n... 还有 {len(self.violations_found) - 10} 个违规素材\n"
        
        report += f"""
🎯 合规性状态:
- 修复前: 严重违规 ({self.fix_summary['total_violations']} 个素材违规)
- 修复后: {'合规' if self.fix_summary['errors'] == [] else '部分修复'}

⚠️ 重要提醒:
1. 素材唯一性测试铁律必须严格遵守
2. 建议加强计划创建前的重复检查
3. 定期进行合规性审计
4. 考虑添加数据库约束防止重复

报告生成完成。
"""
        
        return report


def main():
    """主函数"""
    print("🚨 千川自动化紧急合规性修复工具")
    print("=" * 60)
    
    fixer = EmergencyComplianceFixer()
    
    # 1. 分析违规情况
    print("\n🔍 第一步：分析违规情况")
    violations = fixer.analyze_violations()
    print(f"发现 {violations['violations_count']} 个违规素材")
    print(f"总计 {violations['total_duplicate_plans']} 个重复计划")
    
    # 2. 准备修复计划
    print("\n📋 第二步：准备修复计划")
    fix_plan = fixer.prepare_fix_plan()
    print(f"将保留 {fix_plan['plans_to_keep']} 个最早计划")
    print(f"将删除 {fix_plan['plans_to_delete']} 个重复计划")
    
    # 3. 预览修复
    print("\n🧪 第三步：预览修复操作")
    preview_result = fixer.execute_fix(dry_run=True)
    
    # 4. 询问是否执行实际修复
    print("\n⚠️ 是否执行实际修复？")
    print("警告：这将删除重复的测试计划！")
    
    # 在实际环境中，这里应该有用户确认
    # user_confirm = input("输入 'YES' 确认执行修复: ")
    # if user_confirm == 'YES':
    #     print("\n🛠️ 第四步：执行实际修复")
    #     actual_result = fixer.execute_fix(dry_run=False)
    #     print("✅ 修复完成")
    # else:
    #     print("❌ 用户取消修复")
    
    # 5. 生成报告
    print("\n📊 第五步：生成合规性报告")
    report = fixer.generate_compliance_report()
    
    # 保存报告
    report_file = f"ai_reports/ai_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}_emergency_compliance_fix.md"
    os.makedirs(os.path.dirname(report_file), exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"📄 报告已保存: {report_file}")
    print("\n🎯 建议立即执行修复以确保系统合规性！")
    
    return 0


if __name__ == '__main__':
    exit(main())
