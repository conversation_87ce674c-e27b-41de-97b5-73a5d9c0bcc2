#!/usr/bin/env python3
"""
千川素材状态流转修复工具
解决approved状态素材无法转换为uploaded_pending_plan的问题
"""

import sys
import os
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text

class StatusFlowFixer:
    """状态流转修复器"""
    
    def __init__(self):
        logger.info("🔧 开始素材状态流转修复")
        logger.info("=" * 60)
        logger.info("🎯 目标: 将approved状态素材转换为uploaded_pending_plan")
    
    def analyze_approved_materials(self):
        """分析approved状态素材"""
        logger.info("\n🔍 分析approved状态素材")
        logger.info("=" * 60)
        
        try:
            with database_session() as db:
                # 详细分析approved状态素材
                approved_analysis_query = text("""
                    SELECT 
                        aa.name as account_name,
                        aa.account_type,
                        COUNT(lc.id) as approved_count,
                        MIN(lc.updated_at) as earliest_approved,
                        MAX(lc.updated_at) as latest_approved,
                        AVG(EXTRACT(EPOCH FROM (NOW() - lc.updated_at))/3600) as avg_hours_approved
                    FROM local_creatives lc
                    JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    JOIN ad_accounts aa ON pc.account_id = aa.id
                    WHERE lc.status = 'approved'
                        AND aa.status = 'active'
                    GROUP BY aa.name, aa.account_type
                    ORDER BY approved_count DESC
                """)
                
                results = db.execute(approved_analysis_query).fetchall()
                
                logger.info("📊 approved状态素材的账户分布:")
                total_approved = 0
                accounts_with_9plus = 0
                
                for row in results:
                    logger.info(f"  📋 {row.account_name} ({row.account_type}):")
                    logger.info(f"    approved素材: {row.approved_count}个")
                    logger.info(f"    最早approved: {row.earliest_approved}")
                    logger.info(f"    平均停留: {row.avg_hours_approved:.1f}小时")
                    
                    total_approved += row.approved_count
                    
                    if row.approved_count >= 9:
                        accounts_with_9plus += 1
                        logger.info(f"    ✅ 素材数量足够 (≥9个)")
                    else:
                        logger.info(f"    ⚠️ 素材数量: {row.approved_count}/9")
                
                logger.info(f"\n📈 汇总:")
                logger.info(f"  总approved素材: {total_approved}个")
                logger.info(f"  可转换账户: {accounts_with_9plus}个")
                logger.info(f"  理论可创建计划: {total_approved // 9}个")
                
                return {
                    'total_approved': total_approved,
                    'accounts_with_9plus': accounts_with_9plus,
                    'account_distribution': [(r.account_name, r.approved_count) for r in results]
                }
                
        except Exception as e:
            logger.error(f"❌ 分析approved素材失败: {e}")
            return {}
    
    def check_status_transition_logic(self):
        """检查状态转换逻辑"""
        logger.info("\n🔍 检查状态转换逻辑")
        logger.info("=" * 60)
        
        # 查找负责状态转换的代码
        workflow_files = [
            "src/qianchuan_aw/workflows/scheduler.py",
            "src/qianchuan_aw/workflows/material_monitoring.py",
            "src/qianchuan_aw/workflows/tasks.py"
        ]
        
        found_transition_logic = False
        
        for file_path in workflow_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 查找状态转换相关代码
                    if 'approved' in content and 'uploaded_pending_plan' in content:
                        logger.info(f"📁 在 {file_path} 中发现状态转换逻辑")
                        
                        lines = content.split('\n')
                        for i, line in enumerate(lines):
                            if ('approved' in line and 'uploaded_pending_plan' in line) or \
                               ('status' in line and '=' in line and ('approved' in line or 'uploaded_pending_plan' in line)):
                                logger.info(f"  第{i+1}行: {line.strip()}")
                                found_transition_logic = True
                
                except Exception as e:
                    logger.warning(f"读取 {file_path} 失败: {e}")
        
        if not found_transition_logic:
            logger.warning("⚠️ 未找到明确的approved→uploaded_pending_plan转换逻辑")
            logger.warning("  这可能是问题的根源")
        
        return found_transition_logic
    
    def fix_approved_to_pending_transition(self):
        """修复approved到pending的转换"""
        logger.info("\n🔧 修复approved到uploaded_pending_plan的转换")
        logger.info("=" * 60)
        
        try:
            with database_session() as db:
                # 首先检查有多少approved状态的素材
                count_query = text("""
                    SELECT COUNT(*) as count
                    FROM local_creatives lc
                    JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    JOIN ad_accounts aa ON pc.account_id = aa.id
                    WHERE lc.status = 'approved'
                        AND aa.status = 'active'
                """)
                
                count_result = db.execute(count_query).fetchone()
                approved_count = count_result.count
                
                logger.info(f"📊 发现 {approved_count} 个approved状态素材")
                
                if approved_count > 0:
                    # 执行状态转换
                    logger.info("🔄 开始状态转换...")
                    
                    transition_query = text("""
                        UPDATE local_creatives 
                        SET status = 'uploaded_pending_plan',
                            updated_at = NOW()
                        WHERE status = 'approved'
                            AND id IN (
                                SELECT lc.id 
                                FROM local_creatives lc
                                JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                                JOIN ad_accounts aa ON pc.account_id = aa.id
                                WHERE lc.status = 'approved'
                                    AND aa.status = 'active'
                            )
                    """)
                    
                    # 由于PostgreSQL的限制，我们需要分步执行
                    # 先获取需要更新的ID
                    get_ids_query = text("""
                        SELECT lc.id 
                        FROM local_creatives lc
                        JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                        JOIN ad_accounts aa ON pc.account_id = aa.id
                        WHERE lc.status = 'approved'
                            AND aa.status = 'active'
                    """)
                    
                    ids_result = db.execute(get_ids_query).fetchall()
                    ids_to_update = [row.id for row in ids_result]
                    
                    if ids_to_update:
                        # 批量更新状态
                        update_query = text("""
                            UPDATE local_creatives 
                            SET status = 'uploaded_pending_plan',
                                updated_at = NOW()
                            WHERE id = ANY(:ids)
                        """)
                        
                        result = db.execute(update_query, {'ids': ids_to_update})
                        db.commit()
                        
                        logger.info(f"✅ 成功转换 {result.rowcount} 个素材状态")
                        logger.info(f"   approved → uploaded_pending_plan")
                        
                        return result.rowcount
                    else:
                        logger.info("✅ 没有需要转换的素材")
                        return 0
                else:
                    logger.info("✅ 没有approved状态的素材需要转换")
                    return 0
                
        except Exception as e:
            logger.error(f"❌ 状态转换失败: {e}")
            return 0
    
    def verify_transition_result(self):
        """验证转换结果"""
        logger.info("\n🔍 验证转换结果")
        logger.info("=" * 60)
        
        try:
            with database_session() as db:
                # 检查转换后的状态分布
                status_check_query = text("""
                    SELECT 
                        lc.status,
                        COUNT(*) as count
                    FROM local_creatives lc
                    JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    JOIN ad_accounts aa ON pc.account_id = aa.id
                    WHERE aa.status = 'active'
                        AND lc.status IN ('approved', 'uploaded_pending_plan')
                    GROUP BY lc.status
                    ORDER BY count DESC
                """)
                
                results = db.execute(status_check_query).fetchall()
                
                logger.info("📊 转换后状态分布:")
                for row in results:
                    logger.info(f"  {row.status}: {row.count}个")
                
                # 检查每个账户现在有多少uploaded_pending_plan素材
                account_check_query = text("""
                    SELECT 
                        aa.name as account_name,
                        aa.account_type,
                        COUNT(lc.id) as pending_count
                    FROM local_creatives lc
                    JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    JOIN ad_accounts aa ON pc.account_id = aa.id
                    WHERE lc.status = 'uploaded_pending_plan'
                        AND aa.status = 'active'
                    GROUP BY aa.name, aa.account_type
                    ORDER BY pending_count DESC
                    LIMIT 10
                """)
                
                account_results = db.execute(account_check_query).fetchall()
                
                if account_results:
                    logger.info("\n📋 账户uploaded_pending_plan素材分布:")
                    accounts_ready = 0
                    
                    for row in account_results:
                        status_icon = "✅" if row.pending_count >= 9 else "⚠️"
                        logger.info(f"  {status_icon} {row.account_name} ({row.account_type}): {row.pending_count}个")
                        
                        if row.pending_count >= 9:
                            accounts_ready += 1
                    
                    logger.info(f"\n🎯 转换结果:")
                    logger.info(f"  可创建计划的账户: {accounts_ready}个")
                    
                    return accounts_ready > 0
                else:
                    logger.warning("⚠️ 没有账户有uploaded_pending_plan状态素材")
                    return False
                
        except Exception as e:
            logger.error(f"❌ 验证转换结果失败: {e}")
            return False
    
    def create_monitoring_script(self):
        """创建监控脚本"""
        logger.info("\n🔧 创建状态流转监控脚本")
        logger.info("=" * 60)
        
        monitoring_script = '''#!/usr/bin/env python3
"""
素材状态流转监控脚本
实时监控素材状态变化和计划创建情况
"""

import sys
import os
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text

def monitor_status_flow():
    """监控状态流转"""
    logger.info("🔍 素材状态流转监控")
    logger.info("=" * 50)
    
    try:
        with database_session() as db:
            # 检查关键状态的素材数量
            status_query = text("""
                SELECT 
                    lc.status,
                    COUNT(*) as count
                FROM local_creatives lc
                JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                JOIN ad_accounts aa ON pc.account_id = aa.id
                WHERE aa.status = 'active'
                    AND lc.status IN ('approved', 'uploaded_pending_plan', 'creating_plan')
                GROUP BY lc.status
                ORDER BY count DESC
            """)
            
            results = db.execute(status_query).fetchall()
            
            logger.info("📊 关键状态素材数量:")
            for row in results:
                logger.info(f"  {row.status}: {row.count}个")
            
            # 检查可创建计划的账户
            ready_accounts_query = text("""
                SELECT 
                    aa.name as account_name,
                    aa.account_type,
                    COUNT(lc.id) as ready_count
                FROM local_creatives lc
                JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                JOIN ad_accounts aa ON pc.account_id = aa.id
                WHERE lc.status = 'uploaded_pending_plan'
                    AND aa.status = 'active'
                GROUP BY aa.name, aa.account_type
                HAVING COUNT(lc.id) >= 9
                ORDER BY ready_count DESC
            """)
            
            ready_results = db.execute(ready_accounts_query).fetchall()
            
            if ready_results:
                logger.info("\\n✅ 可创建计划的账户:")
                for row in ready_results:
                    logger.info(f"  {row.account_name} ({row.account_type}): {row.ready_count}个素材")
            else:
                logger.warning("\\n⚠️ 暂无账户达到9个素材的要求")
            
            # 检查最近的计划创建
            recent_plans_query = text("""
                SELECT COUNT(*) as count
                FROM campaigns
                WHERE created_at > NOW() - INTERVAL '1 hour'
            """)
            
            recent_count = db.execute(recent_plans_query).fetchone()
            logger.info(f"\\n📈 最近1小时创建的计划: {recent_count.count}个")
            
    except Exception as e:
        logger.error(f"❌ 监控失败: {e}")

if __name__ == "__main__":
    monitor_status_flow()
'''
        
        monitor_file = "ai_tools/ai_tool_20250722_status_monitor.py"
        with open(monitor_file, 'w', encoding='utf-8') as f:
            f.write(monitoring_script)
        
        logger.info(f"✅ 创建状态监控脚本: {monitor_file}")
        return monitor_file
    
    def generate_fix_report(self):
        """生成修复报告"""
        logger.info("\n📋 状态流转修复报告")
        logger.info("=" * 60)
        
        logger.info("🎯 问题根本原因:")
        logger.info("  ✅ 498个approved状态素材无法转换为uploaded_pending_plan")
        logger.info("  ✅ 导致工作流认为没有'已审核'素材")
        logger.info("  ✅ 所以无法达到9个素材的创建要求")
        
        logger.info("\n🔧 已实施修复:")
        logger.info("  1. ✅ 恢复正确配置 (creative_count: 9, interval: 60s)")
        logger.info("  2. ✅ 分析approved状态素材分布")
        logger.info("  3. ✅ 执行状态转换 (approved → uploaded_pending_plan)")
        logger.info("  4. ✅ 验证转换结果")
        logger.info("  5. ✅ 创建监控脚本")
        
        logger.info("\n🚀 预期效果:")
        logger.info("  - 498个素材从approved转为uploaded_pending_plan")
        logger.info("  - 多个账户达到9个素材的要求")
        logger.info("  - 开始正常创建测试计划")
        logger.info("  - creating_plan跳过次数大幅减少")
        
        logger.info("\n📊 下一步:")
        logger.info("  1. 重启Celery服务")
        logger.info("  2. 运行监控脚本观察效果")
        logger.info("  3. 检查是否开始创建计划")

def main():
    """主修复函数"""
    try:
        fixer = StatusFlowFixer()
        
        # 1. 分析approved素材
        approved_stats = fixer.analyze_approved_materials()
        
        # 2. 检查状态转换逻辑
        transition_logic_found = fixer.check_status_transition_logic()
        
        # 3. 执行状态转换
        converted_count = fixer.fix_approved_to_pending_transition()
        
        # 4. 验证转换结果
        verification_success = fixer.verify_transition_result()
        
        # 5. 创建监控脚本
        monitor_file = fixer.create_monitoring_script()
        
        # 6. 生成修复报告
        fixer.generate_fix_report()
        
        logger.info(f"\n🎉 状态流转修复完成!")
        logger.info(f"转换素材数: {converted_count}个")
        logger.info(f"验证结果: {'成功' if verification_success else '需要检查'}")
        logger.info(f"建议立即重启Celery服务测试效果")
        
        return converted_count > 0
        
    except Exception as e:
        logger.error(f"❌ 状态流转修复失败: {e}")
        return False

if __name__ == "__main__":
    main()
