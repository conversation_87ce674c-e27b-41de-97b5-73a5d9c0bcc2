#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 前端素材搜索页面功能增强
清理条件: 功能完全集成到主系统后删除

千川自动化前端素材搜索页面增强工具
==============================

改进目标：
1. 上传账户显示千川广告户ID
2. 添加测试计划ID显示
3. 状态使用中文名展示
4. 增强搜索结果的全面性
"""

import os
import sys
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.database import SessionLocal
from qianchuan_aw.database.models import LocalCreative, Campaign, PlatformCreative, AdAccount, Principal
from contextlib import contextmanager

@contextmanager
def database_session():
    """数据库会话上下文管理器"""
    session = SessionLocal()
    try:
        yield session
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()


class MaterialSearchEnhancer:
    """素材搜索页面增强器"""
    
    def __init__(self):
        self.status_mapping = {
            # 英文状态 -> 中文状态
            'new': '新素材',
            'pending_grouping': '待分组',
            'pending_upload': '待上传',
            'upload_failed': '上传失败',
            'uploaded_pending_plan': '已上传待建计划',
            'testing_pending_review': '测试中待审核',
            'approved': '审核通过',
            'rejected': '审核拒绝',
            'harvested': '已收割',
            'already_tested': '已测试过',
            'not_harvested': '未收割'
        }
    
    def get_enhanced_material_info(self, db, material_id: int) -> Dict[str, Any]:
        """获取增强的素材信息"""
        try:
            # 查询素材基本信息
            material = db.query(LocalCreative).filter(LocalCreative.id == material_id).first()
            if not material:
                return {}
            
            # 查询关联的平台素材
            platform_creative = db.query(PlatformCreative).filter(
                PlatformCreative.local_creative_id == material.id
            ).first()
            
            # 查询关联的测试计划
            campaigns = []
            ad_account_info = None

            if platform_creative:
                from sqlalchemy.orm import joinedload
                campaigns = db.query(Campaign).join(
                    Campaign.platform_creatives
                ).filter(
                    PlatformCreative.id == platform_creative.id
                ).options(
                    joinedload(Campaign.account).joinedload(AdAccount.principal)
                ).all()

                # 获取广告账户信息
                if campaigns:
                    ad_account_info = campaigns[0].account
            
            # 构建增强信息
            enhanced_info = {
                'basic_info': {
                    'id': material.id,
                    'filename': os.path.basename(material.file_path) if material.file_path else '',
                    'file_hash': material.file_hash,
                    'status_en': material.status,
                    'status_cn': self.status_mapping.get(material.status, material.status),
                    'created_at': material.created_at,
                    'updated_at': material.updated_at
                },
                'account_info': {
                    'principal_name': ad_account_info.principal.name if ad_account_info and ad_account_info.principal else '未知主体',
                    'ad_account_id': ad_account_info.account_id_qc if ad_account_info else '未分配',
                    'account_name': ad_account_info.name if ad_account_info else '未分配'
                },
                'campaign_info': {
                    'campaign_count': len(campaigns),
                    'campaigns': [
                        {
                            'campaign_id_qc': campaign.campaign_id_qc,
                            'campaign_name': f"计划_{campaign.campaign_id_qc}",  # 使用计划ID作为名称
                            'status': campaign.status,
                            'appeal_status': campaign.appeal_status,
                            'created_at': campaign.created_at
                        }
                        for campaign in campaigns
                    ]
                },
                'platform_info': {
                    'platform_creative_id': platform_creative.material_id_qc if platform_creative else None,
                    'upload_status': platform_creative.review_status if platform_creative else '未上传'
                }
            }
            
            return enhanced_info
            
        except Exception as e:
            logger.error(f"获取增强素材信息失败: {e}")
            return {}
    
    def search_materials_enhanced(self, db, search_params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """增强版素材搜索"""
        try:
            # 简化查询，直接查询素材
            query = db.query(LocalCreative)

            # 应用搜索条件
            conditions = []

            # 文件名搜索
            if search_params.get('filename'):
                conditions.append(LocalCreative.file_path.contains(search_params['filename']))

            # 状态筛选
            if search_params.get('status') and search_params['status'] != '全部':
                # 支持中文状态搜索
                status_en = None
                for en_status, cn_status in self.status_mapping.items():
                    if cn_status == search_params['status']:
                        status_en = en_status
                        break

                if status_en:
                    conditions.append(LocalCreative.status == status_en)
                else:
                    conditions.append(LocalCreative.status == search_params['status'])

            # 时间范围筛选
            if search_params.get('start_date'):
                conditions.append(LocalCreative.created_at >= search_params['start_date'])

            if search_params.get('end_date'):
                conditions.append(LocalCreative.created_at <= search_params['end_date'])

            # 应用所有条件
            if conditions:
                from sqlalchemy import and_
                query = query.filter(and_(*conditions))

            # 执行查询
            materials = query.limit(search_params.get('limit', 10)).all()

            # 构建增强结果
            enhanced_results = []
            for material in materials:
                enhanced_info = self.get_enhanced_material_info(db, material.id)
                if enhanced_info:
                    enhanced_results.append(enhanced_info)

            return enhanced_results

        except Exception as e:
            logger.error(f"增强版素材搜索失败: {e}")
            return []
    
    def generate_search_result_html(self, enhanced_results: List[Dict[str, Any]]) -> str:
        """生成增强版搜索结果HTML"""
        if not enhanced_results:
            return "<div class='no-results'>未找到匹配的素材</div>"
        
        html_parts = []
        
        for result in enhanced_results:
            basic = result['basic_info']
            account = result['account_info']
            campaign = result['campaign_info']
            platform = result['platform_info']
            
            # 构建测试计划信息
            campaign_info_html = ""
            if campaign['campaigns']:
                campaign_info_html = "<br>".join([
                    f"计划ID: {c['campaign_id_qc']} | 状态: {c['status']}"
                    for c in campaign['campaigns'][:3]  # 最多显示3个计划
                ])
                if len(campaign['campaigns']) > 3:
                    campaign_info_html += f"<br>... 还有{len(campaign['campaigns']) - 3}个计划"
            else:
                campaign_info_html = "暂无测试计划"
            
            # 构建单个结果HTML
            result_html = f"""
            <div class="material-result-enhanced">
                <div class="material-header">
                    <h4>📹 {basic['filename']}</h4>
                    <span class="status-badge status-{basic['status_en']}">{basic['status_cn']}</span>
                </div>
                
                <div class="material-details">
                    <div class="detail-row">
                        <span class="label">素材ID:</span>
                        <span class="value">{basic['id']}</span>
                    </div>
                    
                    <div class="detail-row">
                        <span class="label">上传账户:</span>
                        <span class="value">{account['ad_account_id']} ({account['principal_name']})</span>
                    </div>
                    
                    <div class="detail-row">
                        <span class="label">测试计划:</span>
                        <div class="campaign-info">{campaign_info_html}</div>
                    </div>
                    
                    <div class="detail-row">
                        <span class="label">文件哈希:</span>
                        <span class="value hash-value">{basic['file_hash'][:16]}...</span>
                    </div>
                    
                    <div class="detail-row">
                        <span class="label">创建时间:</span>
                        <span class="value">{basic['created_at'].strftime('%Y-%m-%d %H:%M:%S') if basic['created_at'] else '未知'}</span>
                    </div>
                </div>
                
                <div class="material-actions">
                    <button class="btn-view" onclick="viewMaterialDetail({basic['id']})">查看详情</button>
                    <button class="btn-history" onclick="viewMaterialHistory({basic['id']})">查看历史</button>
                </div>
            </div>
            """
            
            html_parts.append(result_html)
        
        return "\n".join(html_parts)
    
    def generate_enhanced_css(self) -> str:
        """生成增强版CSS样式"""
        return """
        <style>
        .material-result-enhanced {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .material-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 8px;
        }
        
        .material-header h4 {
            margin: 0;
            color: #333;
            font-size: 16px;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }
        
        .status-approved { background-color: #4CAF50; }
        .status-rejected { background-color: #f44336; }
        .status-harvested { background-color: #2196F3; }
        .status-pending_upload { background-color: #FF9800; }
        .status-new { background-color: #9C27B0; }
        .status-already_tested { background-color: #607D8B; }
        
        .material-details {
            margin-bottom: 12px;
        }
        
        .detail-row {
            display: flex;
            margin-bottom: 8px;
            align-items: flex-start;
        }
        
        .detail-row .label {
            font-weight: bold;
            color: #666;
            min-width: 80px;
            margin-right: 8px;
        }
        
        .detail-row .value {
            color: #333;
            flex: 1;
        }
        
        .campaign-info {
            font-size: 14px;
            line-height: 1.4;
            color: #555;
        }
        
        .hash-value {
            font-family: monospace;
            background: #f5f5f5;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .material-actions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
            padding-top: 8px;
            border-top: 1px solid #f0f0f0;
        }
        
        .material-actions button {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .btn-view {
            background-color: #2196F3;
            color: white;
        }
        
        .btn-history {
            background-color: #4CAF50;
            color: white;
        }
        
        .material-actions button:hover {
            opacity: 0.8;
        }
        
        .no-results {
            text-align: center;
            padding: 40px;
            color: #666;
            font-size: 16px;
        }
        </style>
        """
    
    def create_enhanced_search_demo(self, db):
        """创建增强版搜索演示"""
        logger.info("🎨 创建增强版素材搜索演示...")
        
        # 搜索一些示例素材
        search_params = {
            'limit': 5,
            'status': '审核通过'  # 使用中文状态
        }
        
        enhanced_results = self.search_materials_enhanced(db, search_params)
        
        if enhanced_results:
            html_content = self.generate_search_result_html(enhanced_results)
            css_content = self.generate_enhanced_css()
            
            # 生成完整的HTML演示页面
            demo_html = f"""
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>千川自动化 - 增强版素材搜索演示</title>
                {css_content}
            </head>
            <body>
                <div style="max-width: 1200px; margin: 0 auto; padding: 20px;">
                    <h1>🔍 千川自动化 - 增强版素材搜索演示</h1>
                    <p>展示改进后的素材搜索结果，包含千川广告户ID、测试计划ID和中文状态显示</p>
                    
                    <div class="search-results">
                        {html_content}
                    </div>
                </div>
                
                <script>
                function viewMaterialDetail(materialId) {{
                    alert('查看素材详情: ID ' + materialId);
                    // 这里可以集成到实际的详情页面
                }}
                
                function viewMaterialHistory(materialId) {{
                    alert('查看素材历史: ID ' + materialId);
                    // 这里可以集成到实际的历史页面
                }}
                </script>
            </body>
            </html>
            """
            
            # 保存演示页面
            demo_path = os.path.join(project_root, 'ai_temp', 'enhanced_material_search_demo.html')
            os.makedirs(os.path.dirname(demo_path), exist_ok=True)
            
            with open(demo_path, 'w', encoding='utf-8') as f:
                f.write(demo_html)
            
            logger.success(f"✅ 增强版搜索演示已生成: {demo_path}")
            return demo_path
        else:
            logger.warning("⚠️ 未找到合适的演示数据")
            return None


def main():
    """主函数"""
    enhancer = MaterialSearchEnhancer()
    
    print("🎨 千川自动化前端素材搜索页面增强工具")
    print("=" * 50)
    print("改进内容:")
    print("1. 上传账户显示千川广告户ID")
    print("2. 添加测试计划ID显示")
    print("3. 状态使用中文名展示")
    print("4. 增强搜索结果全面性")
    print("=" * 50)
    
    with database_session() as db:
        # 创建增强版搜索演示
        demo_path = enhancer.create_enhanced_search_demo(db)
        
        if demo_path:
            print(f"\n✅ 增强版搜索演示已生成")
            print(f"📁 演示文件: {demo_path}")
            print(f"🌐 请在浏览器中打开查看效果")
        else:
            print("\n⚠️ 演示生成失败，请检查数据库中是否有合适的素材数据")
    
    return 0


if __name__ == '__main__':
    exit(main())
