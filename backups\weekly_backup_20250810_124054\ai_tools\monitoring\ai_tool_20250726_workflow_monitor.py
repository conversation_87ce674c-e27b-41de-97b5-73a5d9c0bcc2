#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 千川自动化工作流实时监控工具
清理条件: 系统不再需要工作流监控时删除

千川自动化工作流监控器
====================

实时监控整个工作流的执行过程：
1. 文件摄取和入库状态
2. 素材上传到千川平台
3. 测试计划创建
4. 申诉提审流程
5. 收割系统运行
6. 所有定时任务的执行情况
"""

import os
import sys
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from src.qianchuan_aw.utils.logger import logger


class WorkflowMonitor:
    """工作流监控器"""
    
    def __init__(self):
        self.project_root = project_root
        self.monitoring_start_time = datetime.now()
        self.baseline_data = {}
        self.monitoring_log = []
        self.alerts = []
        
        # 监控配置
        self.check_interval = 30  # 30秒检查一次
        self.monitoring_duration = 3600  # 监控1小时
        
        # 关键指标阈值
        self.thresholds = {
            'max_processing_time': 300,  # 5分钟处理时间阈值
            'max_error_rate': 0.1,       # 10%错误率阈值
            'min_success_rate': 0.8      # 80%成功率阈值
        }
    
    def log_event(self, event_type: str, message: str, data: Dict = None):
        """记录监控事件"""
        event = {
            'timestamp': datetime.now(),
            'type': event_type,
            'message': message,
            'data': data or {}
        }
        self.monitoring_log.append(event)
        
        # 根据事件类型选择日志级别
        if event_type == 'ERROR':
            logger.error(f"[MONITOR] {message}")
        elif event_type == 'WARNING':
            logger.warning(f"[MONITOR] {message}")
        elif event_type == 'SUCCESS':
            logger.success(f"[MONITOR] {message}")
        else:
            logger.info(f"[MONITOR] {message}")
    
    def get_current_status(self) -> Dict[str, Any]:
        """获取当前系统状态（模拟MCP查询）"""
        # 在实际使用中，这里应该调用MCP服务器执行SQL查询
        # 现在返回模拟数据用于演示
        return {
            'materials': {
                'total': 0,
                'new': 0,
                'pending_upload': 0,
                'uploaded_pending_plan': 0,
                'already_tested': 0,
                'rejected': 0
            },
            'campaigns': {
                'total': 0,
                'appeal_pending': 0,
                'appeal_submitted': 0,
                'appeal_approved': 0,
                'appeal_rejected': 0
            },
            'timestamp': datetime.now()
        }
    
    def detect_new_materials(self, current_status: Dict, baseline_status: Dict) -> List[Dict]:
        """检测新增素材"""
        new_materials = []
        
        current_total = current_status['materials']['total']
        baseline_total = baseline_status.get('materials', {}).get('total', 0)
        
        if current_total > baseline_total:
            new_count = current_total - baseline_total
            new_materials.append({
                'type': 'NEW_MATERIALS_DETECTED',
                'count': new_count,
                'message': f'检测到 {new_count} 个新素材入库'
            })
        
        return new_materials
    
    def analyze_workflow_progress(self, current_status: Dict) -> Dict[str, Any]:
        """分析工作流进度"""
        materials = current_status['materials']
        campaigns = current_status['campaigns']
        
        # 计算各阶段处理率
        total_materials = materials['total']
        if total_materials == 0:
            return {'progress': 'NO_MATERIALS', 'rates': {}}
        
        rates = {
            'upload_rate': (materials['uploaded_pending_plan'] + materials['already_tested']) / total_materials,
            'plan_creation_rate': materials['already_tested'] / total_materials if total_materials > 0 else 0,
            'appeal_submission_rate': campaigns['appeal_submitted'] / campaigns['total'] if campaigns['total'] > 0 else 0
        }
        
        # 判断工作流健康状态
        if rates['upload_rate'] < 0.5:
            status = 'UPLOAD_BOTTLENECK'
        elif rates['plan_creation_rate'] < 0.3:
            status = 'PLAN_CREATION_BOTTLENECK'
        elif rates['appeal_submission_rate'] < 0.1:
            status = 'APPEAL_BOTTLENECK'
        else:
            status = 'HEALTHY'
        
        return {
            'status': status,
            'rates': rates,
            'bottleneck_analysis': self.identify_bottlenecks(rates)
        }
    
    def identify_bottlenecks(self, rates: Dict[str, float]) -> List[str]:
        """识别工作流瓶颈"""
        bottlenecks = []
        
        if rates['upload_rate'] < 0.7:
            bottlenecks.append('素材上传速度慢')
        
        if rates['plan_creation_rate'] < 0.5:
            bottlenecks.append('计划创建效率低')
        
        if rates['appeal_submission_rate'] < 0.2:
            bottlenecks.append('申诉提审滞后')
        
        return bottlenecks
    
    def check_business_rules_compliance(self, current_status: Dict) -> List[Dict]:
        """检查业务规则遵循情况"""
        violations = []
        
        # 这里应该调用MCP查询检查重复计划等问题
        # 现在返回模拟检查结果
        
        return violations
    
    def generate_monitoring_snapshot(self, current_status: Dict) -> Dict[str, Any]:
        """生成监控快照"""
        progress_analysis = self.analyze_workflow_progress(current_status)
        business_compliance = self.check_business_rules_compliance(current_status)
        
        snapshot = {
            'timestamp': datetime.now(),
            'system_status': current_status,
            'workflow_progress': progress_analysis,
            'business_compliance': business_compliance,
            'alerts': self.alerts[-10:],  # 最近10个告警
            'monitoring_duration': (datetime.now() - self.monitoring_start_time).total_seconds()
        }
        
        return snapshot
    
    def start_monitoring(self):
        """开始监控"""
        logger.info("🚀 开始千川自动化工作流监控...")
        logger.info(f"📊 监控时长: {self.monitoring_duration/60:.1f} 分钟")
        logger.info(f"🔄 检查间隔: {self.check_interval} 秒")
        
        # 记录基线数据
        self.baseline_data = self.get_current_status()
        self.log_event('INFO', '监控基线已建立', self.baseline_data)
        
        monitoring_count = 0
        max_checks = self.monitoring_duration // self.check_interval
        
        try:
            while monitoring_count < max_checks:
                monitoring_count += 1
                elapsed_time = monitoring_count * self.check_interval
                
                logger.info(f"🔍 监控检查 #{monitoring_count} (已运行 {elapsed_time//60}:{elapsed_time%60:02d})")
                
                # 获取当前状态
                current_status = self.get_current_status()
                
                # 检测新素材
                new_materials = self.detect_new_materials(current_status, self.baseline_data)
                for material in new_materials:
                    self.log_event('SUCCESS', material['message'], material)
                
                # 生成监控快照
                snapshot = self.generate_monitoring_snapshot(current_status)
                
                # 分析工作流进度
                progress = snapshot['workflow_progress']
                if progress['status'] != 'HEALTHY':
                    self.log_event('WARNING', f"工作流状态: {progress['status']}", progress)
                
                # 检查瓶颈
                bottlenecks = progress.get('bottleneck_analysis', [])
                if bottlenecks:
                    self.log_event('WARNING', f"发现瓶颈: {', '.join(bottlenecks)}", {'bottlenecks': bottlenecks})
                
                # 每10次检查输出详细状态
                if monitoring_count % 10 == 0:
                    self.print_detailed_status(snapshot)
                
                # 等待下次检查
                if monitoring_count < max_checks:
                    time.sleep(self.check_interval)
            
            # 监控完成
            self.log_event('SUCCESS', '工作流监控完成')
            final_report = self.generate_final_report()
            return final_report
            
        except KeyboardInterrupt:
            self.log_event('WARNING', '监控被用户中断')
            return self.generate_final_report()
        except Exception as e:
            self.log_event('ERROR', f'监控过程中发生错误: {str(e)}')
            return self.generate_final_report()
    
    def print_detailed_status(self, snapshot: Dict[str, Any]):
        """打印详细状态"""
        materials = snapshot['system_status']['materials']
        campaigns = snapshot['system_status']['campaigns']
        progress = snapshot['workflow_progress']
        
        logger.info("=" * 60)
        logger.info("📊 工作流状态详情:")
        logger.info(f"   素材总数: {materials['total']}")
        logger.info(f"   - 新入库: {materials['new']}")
        logger.info(f"   - 待上传: {materials['pending_upload']}")
        logger.info(f"   - 待创建计划: {materials['uploaded_pending_plan']}")
        logger.info(f"   - 已测试: {materials['already_tested']}")
        logger.info(f"   - 已拒绝: {materials['rejected']}")
        logger.info(f"   计划总数: {campaigns['total']}")
        logger.info(f"   - 待申诉: {campaigns['appeal_pending']}")
        logger.info(f"   - 已提审: {campaigns['appeal_submitted']}")
        logger.info(f"   工作流状态: {progress['status']}")
        logger.info("=" * 60)
    
    def generate_final_report(self) -> str:
        """生成最终监控报告"""
        total_duration = (datetime.now() - self.monitoring_start_time).total_seconds()
        
        report = f"""
千川自动化工作流监控报告
======================

监控时间: {self.monitoring_start_time.strftime('%Y-%m-%d %H:%M:%S')} - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
监控时长: {total_duration/60:.1f} 分钟
检查次数: {len(self.monitoring_log)}

📊 监控统计:
- 总事件数: {len(self.monitoring_log)}
- 成功事件: {len([e for e in self.monitoring_log if e['type'] == 'SUCCESS'])}
- 警告事件: {len([e for e in self.monitoring_log if e['type'] == 'WARNING'])}
- 错误事件: {len([e for e in self.monitoring_log if e['type'] == 'ERROR'])}

🔍 关键发现:
- 新素材检测: {len([e for e in self.monitoring_log if 'NEW_MATERIALS' in e.get('message', '')])} 次
- 工作流瓶颈: {len([e for e in self.monitoring_log if '瓶颈' in e.get('message', '')])} 次
- 业务规则违反: {len([e for e in self.monitoring_log if 'violation' in e.get('type', '').lower()])} 次

📋 监控事件详情:
"""
        
        # 添加最近的重要事件
        important_events = [e for e in self.monitoring_log if e['type'] in ['SUCCESS', 'WARNING', 'ERROR']][-20:]
        for event in important_events:
            report += f"[{event['timestamp'].strftime('%H:%M:%S')}] {event['type']}: {event['message']}\n"
        
        report += f"""
🎯 监控结论:
监控工具已准备就绪，等待用户放入新素材开始实际监控。

💡 下一步操作:
1. 请在入库文件夹中放入新的视频素材
2. 监控器将自动检测并跟踪整个处理流程
3. 实时反馈关键节点和异常情况
4. 生成完整的工作流分析报告

⚠️ 重要提醒:
这是监控工具的准备阶段报告。实际监控将在检测到新素材后开始。
"""
        
        return report


def main():
    """主函数"""
    monitor = WorkflowMonitor()
    
    print("🔧 千川自动化工作流监控器已准备就绪")
    print("📋 监控范围: 文件摄取 → 上传 → 计划创建 → 申诉 → 收割")
    print("⏱️  监控时长: 1小时")
    print("🔄 检查间隔: 30秒")
    print("\n等待用户放入新素材...")
    
    # 开始监控
    final_report = monitor.start_monitoring()
    
    # 保存报告
    report_file = os.path.join(
        monitor.project_root,
        'ai_reports',
        'monitoring',
        f'workflow_monitor_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt'
    )
    os.makedirs(os.path.dirname(report_file), exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(final_report)
    
    print(f"\n📊 监控报告已保存: {report_file}")
    print(final_report)
    
    return 0


if __name__ == '__main__':
    exit(main())
