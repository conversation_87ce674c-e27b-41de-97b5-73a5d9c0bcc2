#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 简化版工作流状态监控脚本
清理条件: 项目重构时可考虑删除
"""

import psycopg2
import yaml
from pathlib import Path
from datetime import datetime

def load_database_config():
    """加载数据库配置"""
    config_path = Path(__file__).parent.parent.parent / 'config' / 'settings.yml'
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config['database']['postgresql']

def get_database_connection():
    """获取数据库连接"""
    db_config = load_database_config()
    return psycopg2.connect(
        host=db_config['host'],
        port=db_config['port'],
        database=db_config['dbname'],
        user=db_config['user'],
        password=db_config['password']
    )

def check_workflow_health():
    """检查工作流健康状态"""
    try:
        conn = get_database_connection()
        cursor = conn.cursor()
        
        print("📊 千川工作流状态监控报告")
        print("=" * 60)
        print(f"📅 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 检查local_creatives状态分布
        cursor.execute("""
            SELECT status, COUNT(*) as count 
            FROM local_creatives 
            GROUP BY status 
            ORDER BY count DESC
        """)
        
        print("🎯 本地素材状态分布:")
        print("-" * 40)
        total_materials = 0
        critical_issues = []
        
        for status, count in cursor.fetchall():
            total_materials += count
            alert = ""
            
            # 检查关键问题
            if status == 'pending_upload' and count > 50:
                alert = "🚨 CRITICAL"
                critical_issues.append(f"pending_upload状态过多: {count}")
            elif status == 'upload_failed' and count > 20:
                alert = "⚠️ WARNING"
                critical_issues.append(f"上传失败过多: {count}")
            elif status == 'new' and count > 100:
                alert = "📈 HIGH"
            else:
                alert = "✅"
            
            print(f"   {alert} {status:20} : {count:6}")
        
        print(f"\n📊 总计素材数量: {total_materials}")
        
        # 检查platform_creatives状态
        cursor.execute("""
            SELECT review_status, COUNT(*) as count 
            FROM platform_creatives 
            GROUP BY review_status 
            ORDER BY count DESC
        """)
        
        print("\n🌐 平台素材状态分布:")
        print("-" * 40)
        for status, count in cursor.fetchall():
            print(f"   📋 {status:15} : {count:6}")
        
        # 检查campaigns状态
        cursor.execute("""
            SELECT status, COUNT(*) as count 
            FROM campaigns 
            GROUP BY status 
            ORDER BY count DESC
        """)
        
        print("\n🎪 广告计划状态分布:")
        print("-" * 40)
        for status, count in cursor.fetchall():
            print(f"   🎯 {status:15} : {count:6}")
        
        # 检查最近活动
        cursor.execute("""
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as new_materials
            FROM local_creatives 
            WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
            GROUP BY DATE(created_at)
            ORDER BY date DESC
        """)
        
        print("\n📈 最近7天新增素材:")
        print("-" * 40)
        recent_activity = cursor.fetchall()
        if recent_activity:
            for date, count in recent_activity:
                print(f"   📅 {date} : {count:6} 个")
        else:
            print("   ⚠️ 最近7天无新增素材")
            critical_issues.append("最近7天无新增素材")
        
        # 关键问题汇总
        if critical_issues:
            print("\n🚨 发现的关键问题:")
            print("-" * 40)
            for issue in critical_issues:
                print(f"   ❌ {issue}")
        else:
            print("\n✅ 工作流状态正常，未发现关键问题")
        
        # 建议操作
        print("\n💡 建议操作:")
        print("-" * 40)
        
        # 检查是否有new状态的素材
        cursor.execute("SELECT COUNT(*) FROM local_creatives WHERE status = 'new'")
        new_count = cursor.fetchone()[0]
        
        if new_count > 0:
            print(f"   🔄 有 {new_count} 个新素材待处理，确保Celery Worker正在运行")
        
        # 检查是否有upload_failed的素材
        cursor.execute("SELECT COUNT(*) FROM local_creatives WHERE status = 'upload_failed'")
        failed_count = cursor.fetchone()[0]
        
        if failed_count > 0:
            print(f"   🔧 有 {failed_count} 个上传失败的素材，建议检查API配置")
        
        # 检查是否有approved的素材
        cursor.execute("SELECT COUNT(*) FROM local_creatives WHERE status = 'approved'")
        approved_count = cursor.fetchone()[0]
        
        if approved_count > 0:
            print(f"   🎯 有 {approved_count} 个已审核通过的素材，可以进行收割")
        
        print("\n📋 监控命令:")
        print("-" * 40)
        print("   python ai_tools/monitoring/ai_tool_20250728_monitoring_workflow_status_simple.py")
        print("   # 建议每小时运行一次进行监控")
        
        cursor.close()
        conn.close()
        
        return len(critical_issues) == 0
        
    except Exception as e:
        print(f"❌ 监控检查失败: {e}")
        return False

def main():
    """主函数"""
    try:
        success = check_workflow_health()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n\n⏹️ 监控检查已取消")
        return 1
    except Exception as e:
        print(f"\n❌ 发生未预期的错误: {e}")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
