#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 深度诊断计划创建问题
清理条件: 成为计划创建诊断工具，长期保留
"""

import os
import sys

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger


def deep_diagnosis_plan_creation():
    """深度诊断计划创建问题"""
    logger.info("🔬 深度诊断计划创建问题")
    logger.info("="*100)
    
    try:
        from qianchuan_aw.database.database import SessionLocal
        from qianchuan_aw.database.models import LocalCreative, Platform<PERSON>reative, Principal, AdAccount
        from qianchuan_aw.utils.config_manager import get_config_manager
        
        # 1. 检查配置
        logger.info("📋 检查配置...")
        config_manager = get_config_manager()
        app_settings = config_manager.get_config()
        
        plan_creation_config = app_settings.get('workflow', {}).get('plan_creation', {})
        logger.info(f"   计划创建配置: {plan_creation_config}")
        
        # 2. 检查数据库状态
        logger.info("📊 检查数据库状态...")
        with SessionLocal() as db:
            # 检查缇萃百货的详细状态
            ticui_principal = db.query(Principal).filter(Principal.name == '缇萃百货').first()
            
            if not ticui_principal:
                logger.error("❌ 找不到缇萃百货主体")
                return False
            
            # 检查测试账户
            test_accounts = db.query(AdAccount).filter(
                AdAccount.principal_id == ticui_principal.id,
                AdAccount.account_type == 'TEST',
                AdAccount.status == 'active'
            ).all()
            
            logger.info(f"   缇萃百货活跃测试账户: {len(test_accounts)} 个")
            for account in test_accounts:
                logger.info(f"     - {account.name} ({account.account_id_qc})")
            
            # 检查uploaded_pending_plan素材
            uploaded_creatives = db.query(LocalCreative).filter(
                LocalCreative.principal_id == ticui_principal.id,
                LocalCreative.status == 'uploaded_pending_plan'
            ).all()
            
            logger.info(f"   uploaded_pending_plan素材: {len(uploaded_creatives)} 个")
            
            # 检查有PlatformCreative记录的素材
            creatives_with_pc = []
            creatives_without_pc = []
            
            for creative in uploaded_creatives:
                pc = db.query(PlatformCreative).filter(
                    PlatformCreative.local_creative_id == creative.id
                ).first()
                
                if pc:
                    creatives_with_pc.append((creative, pc))
                else:
                    creatives_without_pc.append(creative)
            
            logger.info(f"   有PlatformCreative记录: {len(creatives_with_pc)} 个")
            logger.info(f"   缺少PlatformCreative记录: {len(creatives_without_pc)} 个")
            
            # 显示前3个有PlatformCreative记录的素材详情
            logger.info("📋 前3个有PlatformCreative记录的素材:")
            for i, (creative, pc) in enumerate(creatives_with_pc[:3]):
                logger.info(f"   {i+1}. LocalCreative ID: {creative.id}")
                logger.info(f"      文件: {os.path.basename(creative.file_path)}")
                logger.info(f"      状态: {creative.status}")
                logger.info(f"      PlatformCreative ID: {pc.id}")
                logger.info(f"      账户ID: {pc.account_id}")
                
                # 检查账户信息
                account = db.query(AdAccount).filter(AdAccount.id == pc.account_id).first()
                if account:
                    logger.info(f"      账户名: {account.name}")
                    logger.info(f"      账户状态: {account.status}")
                    logger.info(f"      账户类型: {account.account_type}")
                else:
                    logger.warning(f"      ⚠️ 找不到账户ID {pc.account_id}")
        
        # 3. 直接调用handle_plan_creation函数进行测试
        logger.info("🧪 直接调用handle_plan_creation函数...")
        
        try:
            from qianchuan_aw.workflows.scheduler import handle_plan_creation
            from qianchuan_aw.utils.db_utils import database_session
            
            with database_session() as db:
                logger.info("   开始执行handle_plan_creation...")
                handle_plan_creation(db, app_settings)
                logger.info("   handle_plan_creation执行完成")
                
        except Exception as e:
            logger.error(f"❌ handle_plan_creation执行失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False
        
        # 4. 检查执行后的状态变化
        logger.info("📊 检查执行后状态...")
        with SessionLocal() as db:
            # 重新检查uploaded_pending_plan数量
            uploaded_after = db.query(LocalCreative).filter(
                LocalCreative.principal_id == ticui_principal.id,
                LocalCreative.status == 'uploaded_pending_plan'
            ).count()
            
            # 检查testing_pending_review数量
            testing_after = db.query(LocalCreative).filter(
                LocalCreative.principal_id == ticui_principal.id,
                LocalCreative.status == 'testing_pending_review'
            ).count()
            
            logger.info(f"   执行后 uploaded_pending_plan: {uploaded_after} 个")
            logger.info(f"   执行后 testing_pending_review: {testing_after} 个")
            
            change = len(uploaded_creatives) - uploaded_after
            if change > 0:
                logger.success(f"✅ 状态转换成功！{change} 个素材状态已改变")
                return True
            else:
                logger.warning("⚠️ 没有检测到状态变化")
                return False
        
    except Exception as e:
        logger.error(f"❌ 深度诊断失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


def main():
    """主函数"""
    logger.info("🚀 启动计划创建深度诊断")
    logger.info("🎯 目标：找出计划创建不工作的根本原因")
    
    success = deep_diagnosis_plan_creation()
    
    if success:
        logger.success("🎊 深度诊断发现计划创建可以工作")
        logger.success("💡 建议：检查任务调度是否正常")
        return 0
    else:
        logger.error("❌ 深度诊断发现计划创建存在问题")
        logger.error("🔧 需要进一步分析错误原因")
        return 1


if __name__ == "__main__":
    exit(main())
