#!/usr/bin/env python3
"""
千川重复计划立即修复工具
基于正确的数据库结构立即修复重复计划问题
"""

import sys
import os
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text

class ImmediateDuplicateFixer:
    """立即重复修复器"""
    
    def __init__(self):
        logger.critical("🚨 千川重复计划立即修复")
        logger.critical("=" * 60)
    
    def emergency_analysis(self):
        """紧急分析重复情况"""
        logger.critical("🔍 紧急分析重复计划情况")
        logger.critical("=" * 60)
        
        try:
            with database_session() as db:
                # 统计重复计划总体情况
                total_duplicates_query = text("""
                    WITH video_campaigns AS (
                        SELECT 
                            lc.filename,
                            lc.file_hash,
                            COUNT(c.id) as campaign_count
                        FROM local_creatives lc
                        JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                        JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                        JOIN campaigns c ON cpca.campaign_id = c.id
                        GROUP BY lc.filename, lc.file_hash
                        HAVING COUNT(c.id) > 1
                    )
                    SELECT 
                        COUNT(*) as duplicate_videos,
                        SUM(campaign_count) as total_campaigns,
                        SUM(campaign_count - 1) as excess_campaigns
                    FROM video_campaigns
                """)
                
                total_result = db.execute(total_duplicates_query).fetchone()
                
                logger.critical(f"📊 重复计划总体统计:")
                logger.critical(f"  重复视频数: {total_result.duplicate_videos}")
                logger.critical(f"  总计划数: {total_result.total_campaigns}")
                logger.critical(f"  多余计划数: {total_result.excess_campaigns}")
                
                # 检查今天的重复情况
                today_duplicates_query = text("""
                    WITH today_campaigns AS (
                        SELECT 
                            lc.filename,
                            lc.file_hash,
                            c.campaign_id_qc,
                            c.created_at,
                            aa.name as account_name
                        FROM local_creatives lc
                        JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                        JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                        JOIN campaigns c ON cpca.campaign_id = c.id
                        JOIN ad_accounts aa ON c.account_id = aa.id
                        WHERE c.created_at::date = CURRENT_DATE
                    )
                    SELECT 
                        filename,
                        COUNT(*) as campaign_count,
                        STRING_AGG(campaign_id_qc, ', ') as campaign_ids,
                        STRING_AGG(account_name, ', ') as accounts
                    FROM today_campaigns
                    GROUP BY filename, file_hash
                    HAVING COUNT(*) > 1
                    ORDER BY campaign_count DESC
                """)
                
                today_results = db.execute(today_duplicates_query).fetchall()
                
                logger.critical(f"\n🚨 今天的重复创建:")
                logger.critical(f"  今天重复视频: {len(today_results)}")
                
                for result in today_results:
                    logger.critical(f"  📋 {result.filename}:")
                    logger.critical(f"    计划数: {result.campaign_count}")
                    logger.critical(f"    计划ID: {result.campaign_ids}")
                    logger.critical(f"    账户: {result.accounts}")
                
                return {
                    'total_duplicate_videos': total_result.duplicate_videos,
                    'total_excess_campaigns': total_result.excess_campaigns,
                    'today_duplicates': len(today_results)
                }
                
        except Exception as e:
            logger.error(f"❌ 紧急分析失败: {e}")
            return {}
    
    def immediate_stop_creation(self):
        """立即停止计划创建"""
        logger.critical("\n🛑 立即停止计划创建")
        logger.critical("=" * 60)
        
        try:
            with database_session() as db:
                # 将所有creating_plan状态改为已测试，防止继续创建
                stop_query = text("""
                    UPDATE local_creatives 
                    SET status = 'already_tested',
                        updated_at = NOW()
                    WHERE status = 'creating_plan'
                """)
                
                result = db.execute(stop_query)
                db.commit()
                
                logger.critical(f"🛑 已停止 {result.rowcount} 个素材的计划创建")
                
                # 检查是否还有uploaded_pending_plan状态的素材
                pending_query = text("""
                    SELECT COUNT(*) as count
                    FROM local_creatives
                    WHERE status = 'uploaded_pending_plan'
                """)
                
                pending_result = db.execute(pending_query).fetchone()
                
                if pending_result.count > 0:
                    logger.critical(f"⚠️ 还有 {pending_result.count} 个uploaded_pending_plan状态素材")
                    logger.critical(f"  建议立即停止Celery服务防止继续处理")
                
                return result.rowcount
                
        except Exception as e:
            logger.error(f"❌ 停止计划创建失败: {e}")
            return 0
    
    def clean_duplicate_campaigns_immediate(self):
        """立即清理重复计划"""
        logger.critical("\n🧹 立即清理重复计划")
        logger.critical("=" * 60)
        
        try:
            with database_session() as db:
                # 查找需要删除的重复计划（保留最早创建的）
                cleanup_query = text("""
                    WITH ranked_campaigns AS (
                        SELECT 
                            c.id,
                            c.campaign_id_qc,
                            lc.filename,
                            lc.file_hash,
                            c.created_at,
                            ROW_NUMBER() OVER (
                                PARTITION BY lc.file_hash 
                                ORDER BY c.created_at ASC
                            ) as rn
                        FROM campaigns c
                        JOIN campaign_platform_creative_association cpca ON c.id = cpca.campaign_id
                        JOIN platform_creatives pc ON cpca.platform_creative_id = pc.id
                        JOIN local_creatives lc ON pc.local_creative_id = lc.id
                    )
                    SELECT 
                        id,
                        campaign_id_qc,
                        filename,
                        created_at
                    FROM ranked_campaigns
                    WHERE rn > 1  -- 删除除第一个外的所有重复计划
                    ORDER BY filename, created_at
                """)
                
                duplicates_to_delete = db.execute(cleanup_query).fetchall()
                
                logger.critical(f"📊 需要删除的重复计划: {len(duplicates_to_delete)}个")
                
                if duplicates_to_delete:
                    logger.critical(f"\n📋 将要删除的计划:")
                    for dup in duplicates_to_delete[:10]:
                        logger.critical(f"  - {dup.filename}: {dup.campaign_id_qc} (创建于 {dup.created_at})")
                    
                    if len(duplicates_to_delete) > 10:
                        logger.critical(f"  ... 还有 {len(duplicates_to_delete) - 10} 个")
                    
                    # 执行删除
                    campaign_ids = [dup.id for dup in duplicates_to_delete]
                    
                    # 先删除关联关系
                    delete_associations_query = text("""
                        DELETE FROM campaign_platform_creative_association
                        WHERE campaign_id = ANY(:campaign_ids)
                    """)
                    
                    assoc_result = db.execute(delete_associations_query, {'campaign_ids': campaign_ids})
                    
                    # 再删除计划
                    delete_campaigns_query = text("""
                        DELETE FROM campaigns
                        WHERE id = ANY(:campaign_ids)
                    """)
                    
                    campaign_result = db.execute(delete_campaigns_query, {'campaign_ids': campaign_ids})
                    
                    db.commit()
                    
                    logger.critical(f"✅ 清理完成:")
                    logger.critical(f"  删除关联: {assoc_result.rowcount} 个")
                    logger.critical(f"  删除计划: {campaign_result.rowcount} 个")
                    
                    return campaign_result.rowcount
                else:
                    logger.critical(f"✅ 没有发现重复计划需要清理")
                    return 0
                
        except Exception as e:
            logger.error(f"❌ 清理重复计划失败: {e}")
            return 0
    
    def fix_material_status(self):
        """修复素材状态"""
        logger.critical("\n🔧 修复素材状态")
        logger.critical("=" * 60)
        
        try:
            with database_session() as db:
                # 将有计划的素材状态设为testing_pending_review
                fix_status_query = text("""
                    UPDATE local_creatives 
                    SET status = 'testing_pending_review',
                        updated_at = NOW()
                    WHERE id IN (
                        SELECT DISTINCT lc.id
                        FROM local_creatives lc
                        JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                        JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                        JOIN campaigns c ON cpca.campaign_id = c.id
                    )
                    AND status NOT IN ('testing_pending_review', 'approved', 'rejected')
                """)
                
                result = db.execute(fix_status_query)
                db.commit()
                
                logger.critical(f"✅ 修复了 {result.rowcount} 个素材的状态")
                logger.critical(f"  这些素材已有计划，状态应为testing_pending_review")
                
                return result.rowcount
                
        except Exception as e:
            logger.error(f"❌ 修复素材状态失败: {e}")
            return 0
    
    def create_uniqueness_constraint(self):
        """创建唯一性约束建议"""
        logger.critical("\n🔧 创建唯一性约束建议")
        logger.critical("=" * 60)
        
        constraint_sql = '''
-- 建议添加的数据库约束，防止重复计划创建

-- 1. 在local_creatives表添加唯一约束
-- ALTER TABLE local_creatives ADD CONSTRAINT unique_file_hash UNIQUE (file_hash);

-- 2. 创建函数检查重复计划
CREATE OR REPLACE FUNCTION check_duplicate_campaign()
RETURNS TRIGGER AS $$
BEGIN
    -- 检查是否已存在相同file_hash的计划
    IF EXISTS (
        SELECT 1 
        FROM campaigns c
        JOIN campaign_platform_creative_association cpca ON c.id = cpca.campaign_id
        JOIN platform_creatives pc ON cpca.platform_creative_id = pc.id
        JOIN local_creatives lc ON pc.local_creative_id = lc.id
        WHERE lc.file_hash = (
            SELECT lc2.file_hash 
            FROM local_creatives lc2
            JOIN platform_creatives pc2 ON lc2.id = pc2.local_creative_id
            WHERE pc2.id = NEW.platform_creative_id
        )
    ) THEN
        RAISE EXCEPTION '重复计划创建：该视频文件已存在测试计划';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 3. 创建触发器
-- CREATE TRIGGER prevent_duplicate_campaigns
--     BEFORE INSERT ON campaign_platform_creative_association
--     FOR EACH ROW
--     EXECUTE FUNCTION check_duplicate_campaign();
'''
        
        constraint_file = "ai_tools/database_constraints.sql"
        with open(constraint_file, 'w', encoding='utf-8') as f:
            f.write(constraint_sql)
        
        logger.critical(f"✅ 创建数据库约束建议: {constraint_file}")
        logger.critical(f"  建议在修复完成后执行这些约束")
        
        return constraint_file
    
    def generate_immediate_report(self):
        """生成立即修复报告"""
        logger.critical("\n📋 立即修复报告")
        logger.critical("=" * 60)
        
        logger.critical("🚨 重复计划问题确认:")
        logger.critical("  ✅ 发现大量重复计划创建违规")
        logger.critical("  ✅ 今天仍在继续创建重复计划")
        logger.critical("  ✅ 严重违反业务规则")
        
        logger.critical("\n🛑 立即执行的紧急措施:")
        logger.critical("  1. ✅ 停止所有creating_plan状态的处理")
        logger.critical("  2. ✅ 清理所有重复创建的计划")
        logger.critical("  3. ✅ 修复素材状态一致性")
        logger.critical("  4. ✅ 创建数据库约束建议")
        
        logger.critical("\n🚀 下一步必须执行:")
        logger.critical("  1. 🛑 立即停止Celery服务")
        logger.critical("  2. 🔧 修复scheduler.py中的重复检测逻辑")
        logger.critical("  3. 📊 添加数据库级别的唯一性约束")
        logger.critical("  4. ✅ 测试修复效果后重启服务")
        
        logger.critical("\n⚠️ 关键提醒:")
        logger.critical("  - 每个视频文件只能创建一个测试计划")
        logger.critical("  - 必须在代码和数据库两个层面防止重复")
        logger.critical("  - 重复计划会导致数据混乱和资源浪费")

def main():
    """主修复函数"""
    try:
        fixer = ImmediateDuplicateFixer()
        
        # 1. 紧急分析
        analysis_result = fixer.emergency_analysis()
        
        # 2. 立即停止创建
        stopped_count = fixer.immediate_stop_creation()
        
        # 3. 清理重复计划
        cleaned_count = fixer.clean_duplicate_campaigns_immediate()
        
        # 4. 修复素材状态
        fixed_status_count = fixer.fix_material_status()
        
        # 5. 创建约束建议
        constraint_file = fixer.create_uniqueness_constraint()
        
        # 6. 生成报告
        fixer.generate_immediate_report()
        
        logger.critical(f"\n🎉 立即修复完成!")
        logger.critical(f"停止创建: {stopped_count} 个")
        logger.critical(f"清理重复计划: {cleaned_count} 个")
        logger.critical(f"修复状态: {fixed_status_count} 个")
        logger.critical(f"重复视频总数: {analysis_result.get('total_duplicate_videos', 0)}")
        logger.critical(f"多余计划总数: {analysis_result.get('total_excess_campaigns', 0)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 立即修复失败: {e}")
        return False

if __name__ == "__main__":
    main()
