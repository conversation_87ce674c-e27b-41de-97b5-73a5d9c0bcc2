#!/usr/bin/env python3
"""
千川视频素材工作流文件管理问题修复工具
提供具体的修复方案和代码实现
"""

import sys
import os
import time
import shutil
from pathlib import Path
from datetime import datetime
import threading
from contextlib import contextmanager

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.utils.logger import logger
from sqlalchemy import text

class FileAccessManager:
    """文件访问管理器 - 解决文件锁冲突问题"""
    
    def __init__(self):
        self._file_locks = {}
        self._lock = threading.Lock()
    
    @contextmanager
    def safe_file_access(self, file_path: str, max_retries: int = 5, base_delay: float = 1.0):
        """
        安全文件访问上下文管理器
        
        Args:
            file_path: 文件路径
            max_retries: 最大重试次数
            base_delay: 基础延迟时间（秒）
        """
        normalized_path = os.path.normpath(file_path)
        
        # 获取文件级锁
        with self._lock:
            if normalized_path not in self._file_locks:
                self._file_locks[normalized_path] = threading.Lock()
            file_lock = self._file_locks[normalized_path]
        
        # 尝试获取文件锁
        acquired = False
        try:
            for attempt in range(max_retries):
                try:
                    # 检查文件是否被其他进程锁定
                    if self._check_file_accessible(normalized_path):
                        acquired = file_lock.acquire(timeout=base_delay * (2 ** attempt))
                        if acquired:
                            logger.debug(f"获取文件锁成功: {os.path.basename(normalized_path)}")
                            yield normalized_path
                            return
                    
                    if attempt < max_retries - 1:
                        delay = base_delay * (2 ** attempt)
                        logger.warning(f"文件被锁定，等待 {delay:.1f}s 后重试 (尝试 {attempt + 1}/{max_retries}): {os.path.basename(normalized_path)}")
                        time.sleep(delay)
                
                except Exception as e:
                    logger.error(f"文件访问检查失败: {e}")
                    if attempt == max_retries - 1:
                        raise
            
            raise PermissionError(f"文件锁定冲突，已重试 {max_retries} 次仍无法访问: {normalized_path}")
            
        finally:
            if acquired:
                file_lock.release()
                logger.debug(f"释放文件锁: {os.path.basename(normalized_path)}")
    
    def _check_file_accessible(self, file_path: str) -> bool:
        """检查文件是否可访问"""
        try:
            with open(file_path, 'rb') as f:
                f.read(1024)  # 尝试读取1KB
            return True
        except (PermissionError, OSError):
            return False

class QuarantineManager:
    """隔离管理器 - 优化隔离机制"""
    
    def __init__(self, quarantine_dir: str = "quarantine/invalid_videos"):
        self.quarantine_dir = Path(quarantine_dir)
        self.quarantine_dir.mkdir(parents=True, exist_ok=True)
        self._quarantined_hashes = set()
        self._load_quarantined_hashes()
    
    def _load_quarantined_hashes(self):
        """加载已隔离文件的哈希值"""
        try:
            from src.qianchuan_aw.utils.hash_utils import calculate_file_hash
            
            for video_file in self.quarantine_dir.glob("*.mp4"):
                try:
                    file_hash = calculate_file_hash(str(video_file))
                    if file_hash:
                        self._quarantined_hashes.add(file_hash)
                except Exception:
                    continue
        except Exception as e:
            logger.error(f"加载隔离文件哈希失败: {e}")
    
    def should_quarantine(self, file_path: str) -> bool:
        """检查文件是否应该被隔离（避免重复隔离）"""
        try:
            from src.qianchuan_aw.utils.hash_utils import calculate_file_hash
            
            file_hash = calculate_file_hash(file_path)
            if file_hash in self._quarantined_hashes:
                logger.info(f"文件已被隔离过，跳过: {os.path.basename(file_path)}")
                return False
            return True
        except Exception as e:
            logger.error(f"检查隔离状态失败: {e}")
            return True  # 出错时默认允许隔离
    
    def quarantine_file(self, file_path: str, reason: str) -> bool:
        """隔离文件（改进版）"""
        if not self.should_quarantine(file_path):
            return False
        
        try:
            from src.qianchuan_aw.utils.hash_utils import calculate_file_hash
            
            filename = os.path.basename(file_path)
            
            # 使用文件哈希作为唯一标识，避免重复隔离
            file_hash = calculate_file_hash(file_path)
            quarantine_name = f"{file_hash[:8]}_{filename}"
            quarantine_path = self.quarantine_dir / quarantine_name
            
            # 移动文件
            shutil.move(file_path, quarantine_path)
            
            # 记录隔离信息
            reason_file = quarantine_path.with_suffix(quarantine_path.suffix + ".reason.txt")
            with open(reason_file, 'w', encoding='utf-8') as f:
                f.write(f"隔离原因: {reason}\n")
                f.write(f"原路径: {file_path}\n")
                f.write(f"文件哈希: {file_hash}\n")
                f.write(f"隔离时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            
            # 更新已隔离哈希集合
            self._quarantined_hashes.add(file_hash)
            
            logger.info(f"文件已隔离: {filename} -> {quarantine_name}")
            return True
            
        except Exception as e:
            logger.error(f"隔离文件失败: {e}")
            return False
    
    def cleanup_quarantine(self, days_old: int = 30):
        """清理旧的隔离文件"""
        try:
            cutoff_time = datetime.now().timestamp() - (days_old * 24 * 3600)
            cleaned_count = 0
            
            for file_path in self.quarantine_dir.glob("*"):
                if file_path.stat().st_mtime < cutoff_time:
                    if file_path.is_file():
                        file_path.unlink()
                        cleaned_count += 1
            
            logger.info(f"清理了 {cleaned_count} 个超过 {days_old} 天的隔离文件")
            
        except Exception as e:
            logger.error(f"清理隔离文件失败: {e}")

class FilePathManager:
    """文件路径管理器 - 修复文件路径问题"""
    
    def __init__(self):
        self.search_dirs = [
            "G:/workflow_assets/01_materials_to_process",
            "G:/workflow_assets/02_materials_in_testing",
            "G:/workflow_assets/00_materials_archived",
            "quarantine/invalid_videos"
        ]
    
    def fix_missing_file_paths(self, dry_run: bool = True):
        """修复缺失的文件路径"""
        logger.info(f"开始修复缺失的文件路径 (dry_run={dry_run})")
        
        with database_session() as db:
            # 查找物理文件不存在的记录
            missing_query = text("""
                SELECT 
                    lc.id,
                    lc.filename,
                    lc.file_path,
                    lc.file_hash,
                    lc.status
                FROM local_creatives lc
                JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
                WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                    AND lc.file_path IS NOT NULL AND lc.file_path != ''
                    AND lc.status IN ('processing', 'upload_failed')
                ORDER BY lc.updated_at DESC
                LIMIT 100
            """)
            
            missing_results = db.execute(missing_query).fetchall()
            fixed_count = 0
            
            for row in missing_results:
                if not os.path.exists(row.file_path):
                    # 尝试找到文件的新位置
                    new_path = self._find_file_by_hash_or_name(row.file_hash, row.filename)
                    
                    if new_path:
                        logger.info(f"找到文件新位置: {row.filename}")
                        logger.info(f"  原路径: {row.file_path}")
                        logger.info(f"  新路径: {new_path}")
                        
                        if not dry_run:
                            # 更新数据库中的文件路径
                            update_query = text("""
                                UPDATE local_creatives 
                                SET file_path = :new_path, updated_at = NOW()
                                WHERE id = :creative_id
                            """)
                            db.execute(update_query, {
                                'new_path': new_path,
                                'creative_id': row.id
                            })
                            db.commit()
                        
                        fixed_count += 1
                    else:
                        logger.warning(f"未找到文件: {row.filename}")
            
            logger.info(f"修复完成，共处理 {fixed_count} 个文件路径")
    
    def _find_file_by_hash_or_name(self, file_hash: str, filename: str) -> str:
        """通过哈希值或文件名查找文件"""
        from src.qianchuan_aw.utils.hash_utils import calculate_file_hash
        
        # 首先尝试通过哈希值精确匹配
        if file_hash:
            for search_dir in self.search_dirs:
                if not os.path.exists(search_dir):
                    continue
                
                for root, dirs, files in os.walk(search_dir):
                    for file in files:
                        if file.endswith('.mp4'):
                            file_path = os.path.join(root, file)
                            try:
                                current_hash = calculate_file_hash(file_path)
                                if current_hash == file_hash:
                                    return file_path
                            except Exception:
                                continue
        
        # 如果哈希匹配失败，尝试文件名匹配
        if filename:
            for search_dir in self.search_dirs:
                if not os.path.exists(search_dir):
                    continue
                
                for root, dirs, files in os.walk(search_dir):
                    if filename in files:
                        return os.path.join(root, filename)
        
        return None

def apply_file_management_fixes():
    """应用文件管理修复"""
    logger.info("🚀 开始应用文件管理修复")
    
    # 1. 清理隔离目录
    logger.info("📁 清理隔离目录...")
    quarantine_manager = QuarantineManager()
    quarantine_manager.cleanup_quarantine(days_old=7)  # 清理7天前的隔离文件
    
    # 2. 修复文件路径
    logger.info("🔧 修复文件路径...")
    path_manager = FilePathManager()
    path_manager.fix_missing_file_paths(dry_run=False)
    
    # 3. 重置失败状态的素材
    logger.info("🔄 重置失败状态的素材...")
    with database_session() as db:
        reset_query = text("""
            UPDATE local_creatives 
            SET status = 'pending_grouping', updated_at = NOW()
            WHERE id IN (
                SELECT lc.id FROM local_creatives lc
                JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
                WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                    AND lc.status = 'upload_failed'
                    AND lc.file_path IS NOT NULL 
                    AND lc.updated_at < NOW() - INTERVAL '1 hour'
            )
        """)
        result = db.execute(reset_query)
        db.commit()
        logger.info(f"重置了 {result.rowcount} 个失败状态的素材")
    
    logger.info("✅ 文件管理修复完成")

if __name__ == "__main__":
    # 演示文件访问管理器的使用
    file_manager = FileAccessManager()
    
    # 示例：安全访问文件
    test_file = "G:/workflow_assets/01_materials_to_process/缇萃百货/test.mp4"
    if os.path.exists(test_file):
        try:
            with file_manager.safe_file_access(test_file) as safe_path:
                logger.info(f"安全访问文件: {safe_path}")
                # 在这里进行文件操作
        except PermissionError as e:
            logger.error(f"文件访问失败: {e}")
    
    # 应用所有修复
    apply_file_management_fixes()
