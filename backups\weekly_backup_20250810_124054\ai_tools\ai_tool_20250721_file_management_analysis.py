#!/usr/bin/env python3
"""
千川视频素材工作流文件管理和上传问题深度分析工具
分析隔离目录、文件访问冲突、上传效率等问题
"""

import sys
import os
import time
import shutil
from pathlib import Path
from collections import defaultdict, Counter
from datetime import datetime, timedelta
import psutil

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.utils.config_loader import load_settings
from sqlalchemy import text
import pandas as pd

def analyze_quarantine_directory():
    """分析隔离目录"""
    print("🔍 隔离目录分析")
    print("=" * 60)
    
    quarantine_dir = Path("quarantine/invalid_videos")
    
    if not quarantine_dir.exists():
        print("❌ 隔离目录不存在")
        return
    
    # 统计隔离文件
    video_files = list(quarantine_dir.glob("*.mp4"))
    reason_files = list(quarantine_dir.glob("*.reason.txt"))
    
    print(f"📊 隔离目录统计:")
    print(f"  隔离视频文件: {len(video_files)} 个")
    print(f"  说明文件: {len(reason_files)} 个")
    print(f"  目录大小: {get_directory_size(quarantine_dir):.2f} MB")
    
    # 分析隔离原因
    print(f"\n📋 隔离原因分析:")
    reason_stats = defaultdict(int)
    
    for reason_file in reason_files:
        try:
            with open(reason_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if '隔离原因:' in content:
                    reason = content.split('隔离原因:')[1].split('\n')[0].strip()
                    reason_stats[reason] += 1
        except Exception as e:
            print(f"  ⚠️ 读取说明文件失败: {reason_file.name}")
    
    for reason, count in sorted(reason_stats.items(), key=lambda x: x[1], reverse=True):
        print(f"  {reason}: {count} 次")
    
    # 分析重复隔离
    print(f"\n🔄 重复隔离分析:")
    filename_counts = Counter()
    for video_file in video_files:
        # 提取基础文件名（去除时间戳）
        base_name = video_file.name
        if '_' in base_name and base_name.split('_')[-1].replace('.mp4', '').isdigit():
            base_name = '_'.join(base_name.split('_')[:-1]) + '.mp4'
        filename_counts[base_name] += 1
    
    duplicates = {name: count for name, count in filename_counts.items() if count > 1}
    if duplicates:
        print(f"  发现 {len(duplicates)} 个文件被重复隔离:")
        for filename, count in sorted(duplicates.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"    {filename}: {count} 次")
    else:
        print("  ✅ 未发现重复隔离")

def analyze_file_access_conflicts():
    """分析文件访问冲突"""
    print(f"\n🔒 文件访问冲突分析")
    print("=" * 60)
    
    # 检查主要工作目录
    work_dirs = [
        "G:/workflow_assets/01_materials_to_process/缇萃百货",
        "G:/workflow_assets/02_materials_in_testing",
        "G:/workflow_assets/00_materials_archived"
    ]
    
    locked_files = []
    total_files = 0
    
    for work_dir in work_dirs:
        if not os.path.exists(work_dir):
            print(f"  ⚠️ 目录不存在: {work_dir}")
            continue
        
        print(f"\n📁 检查目录: {work_dir}")
        dir_files = list(Path(work_dir).glob("*.mp4"))
        total_files += len(dir_files)
        
        dir_locked = 0
        for file_path in dir_files:
            if is_file_locked(file_path):
                locked_files.append(str(file_path))
                dir_locked += 1
        
        print(f"  总文件: {len(dir_files)}")
        print(f"  被锁定: {dir_locked}")
        print(f"  锁定率: {(dir_locked/len(dir_files)*100):.1f}%" if dir_files else "0%")
    
    print(f"\n📊 总体文件锁定情况:")
    print(f"  总文件数: {total_files}")
    print(f"  被锁定文件: {len(locked_files)}")
    print(f"  总锁定率: {(len(locked_files)/total_files*100):.1f}%" if total_files else "0%")
    
    if locked_files:
        print(f"\n🔒 被锁定的文件 (前10个):")
        for file_path in locked_files[:10]:
            print(f"    {Path(file_path).name}")
            # 尝试找到占用进程
            processes = find_processes_using_file(file_path)
            if processes:
                for proc in processes[:3]:
                    print(f"      占用进程: {proc['name']} (PID: {proc['pid']})")

def analyze_upload_efficiency():
    """分析上传效率"""
    print(f"\n⚡ 上传效率分析")
    print("=" * 60)
    
    with database_session() as db:
        # 获取配置信息
        config = load_settings()
        max_workers = config.get('workflow', {}).get('max_upload_workers', 15)
        retry_delay = config.get('robustness', {}).get('upload_retry_delay', 60)
        
        print(f"📋 当前配置:")
        print(f"  最大并发上传: {max_workers}")
        print(f"  重试延迟: {retry_delay} 秒")
        
        # 分析待处理素材
        pending_query = text("""
            SELECT 
                lc.status,
                COUNT(*) as count,
                AVG(EXTRACT(EPOCH FROM (NOW() - lc.created_at))/3600) as avg_age_hours
            FROM local_creatives lc
            JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
            WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                AND lc.status IN ('pending_grouping', 'processing', 'uploaded_pending_plan', 'creating_plan')
            GROUP BY lc.status
            ORDER BY count DESC
        """)
        
        pending_results = db.execute(pending_query).fetchall()
        
        print(f"\n📊 待处理素材状态:")
        total_pending = 0
        for row in pending_results:
            total_pending += row.count
            print(f"  {row.status}: {row.count} 个 (平均等待: {row.avg_age_hours:.1f}小时)")
        
        # 计算理论处理时间
        if total_pending > 0:
            # 假设每个视频上传需要30秒，包括验证、MD5计算、API调用
            avg_upload_time = 30  # 秒
            theoretical_time = (total_pending * avg_upload_time) / max_workers / 60  # 分钟
            
            print(f"\n⏱️ 理论处理时间估算:")
            print(f"  待处理素材: {total_pending} 个")
            print(f"  平均上传时间: {avg_upload_time} 秒/个")
            print(f"  并发处理: {max_workers} 个")
            print(f"  理论完成时间: {theoretical_time:.1f} 分钟")
        
        # 分析失败重试情况
        failed_query = text("""
            SELECT 
                COUNT(*) as failed_count,
                COUNT(CASE WHEN lc.updated_at > NOW() - INTERVAL '1 hour' THEN 1 END) as recent_failed
            FROM local_creatives lc
            JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
            WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                AND lc.status = 'upload_failed'
        """)
        
        failed_result = db.execute(failed_query).fetchone()
        
        print(f"\n❌ 失败重试分析:")
        print(f"  总失败数: {failed_result.failed_count}")
        print(f"  近1小时失败: {failed_result.recent_failed}")
        
        if failed_result.failed_count > 0:
            failure_rate = (failed_result.failed_count / (total_pending + failed_result.failed_count)) * 100
            print(f"  失败率: {failure_rate:.1f}%")

def analyze_missing_files():
    """分析文件不存在错误"""
    print(f"\n📂 文件不存在错误分析")
    print("=" * 60)
    
    with database_session() as db:
        # 查找文件路径为空或文件不存在的记录
        missing_query = text("""
            SELECT 
                lc.id,
                lc.filename,
                lc.file_path,
                lc.status,
                lc.created_at,
                lc.updated_at
            FROM local_creatives lc
            JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
            WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                AND (lc.file_path IS NULL OR lc.file_path = '')
            ORDER BY lc.updated_at DESC
            LIMIT 20
        """)
        
        missing_results = db.execute(missing_query).fetchall()
        
        print(f"📊 文件路径缺失记录: {len(missing_results)} 个")
        
        if missing_results:
            print(f"\n📋 缺失文件路径的记录 (前10个):")
            for row in missing_results[:10]:
                print(f"  ID: {row.id}")
                print(f"    文件名: {row.filename}")
                print(f"    状态: {row.status}")
                print(f"    创建时间: {row.created_at}")
                print(f"    更新时间: {row.updated_at}")
                print()
        
        # 检查文件路径存在但文件实际不存在的情况
        path_check_query = text("""
            SELECT 
                lc.id,
                lc.filename,
                lc.file_path,
                lc.status
            FROM local_creatives lc
            JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
            WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                AND lc.file_path IS NOT NULL AND lc.file_path != ''
                AND lc.status IN ('processing', 'upload_failed')
            ORDER BY lc.updated_at DESC
            LIMIT 50
        """)
        
        path_results = db.execute(path_check_query).fetchall()
        
        missing_physical_files = []
        for row in path_results:
            if row.file_path and not os.path.exists(row.file_path):
                missing_physical_files.append(row)
        
        print(f"📊 物理文件缺失记录: {len(missing_physical_files)} 个")
        
        if missing_physical_files:
            print(f"\n📋 物理文件不存在的记录 (前5个):")
            for row in missing_physical_files[:5]:
                print(f"  ID: {row.id}")
                print(f"    文件名: {row.filename}")
                print(f"    路径: {row.file_path}")
                print(f"    状态: {row.status}")
                
                # 尝试在其他目录找到文件
                if row.filename:
                    found_path = find_file_in_workflow_dirs(row.filename)
                    if found_path:
                        print(f"    ✅ 在其他位置找到: {found_path}")
                    else:
                        print(f"    ❌ 未在工作目录中找到")
                print()

def get_directory_size(directory):
    """获取目录大小（MB）"""
    total_size = 0
    try:
        for dirpath, dirnames, filenames in os.walk(directory):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total_size += os.path.getsize(filepath)
    except Exception:
        pass
    return total_size / (1024 * 1024)

def is_file_locked(file_path):
    """检查文件是否被锁定"""
    try:
        with open(file_path, 'rb') as f:
            f.read(1024)
        return False
    except (PermissionError, OSError):
        return True

def find_processes_using_file(file_path):
    """查找占用文件的进程"""
    processes = []
    try:
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                for item in proc.open_files():
                    if item.path == file_path:
                        processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name']
                        })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
    except Exception:
        pass
    return processes

def find_file_in_workflow_dirs(filename):
    """在工作流目录中查找文件"""
    search_dirs = [
        "G:/workflow_assets/01_materials_to_process",
        "G:/workflow_assets/02_materials_in_testing", 
        "G:/workflow_assets/00_materials_archived",
        "quarantine/invalid_videos"
    ]
    
    for search_dir in search_dirs:
        if os.path.exists(search_dir):
            for root, dirs, files in os.walk(search_dir):
                if filename in files:
                    return os.path.join(root, filename)
    return None

def generate_recommendations():
    """生成修复建议"""
    print(f"\n🛠️ 修复建议和解决方案")
    print("=" * 60)
    
    print(f"🔴 P0 - 文件访问冲突修复:")
    print(f"  1. 实现文件锁检测和重试机制")
    print(f"     - 在文件操作前检查文件锁状态")
    print(f"     - 使用指数退避重试策略")
    print(f"     - 最大重试次数: 5次，总等待时间: 30秒")
    
    print(f"\n  2. 优化并发文件访问")
    print(f"     - 实现分布式文件锁")
    print(f"     - 限制同一文件的并发访问")
    print(f"     - 使用文件访问队列")
    
    print(f"\n🟡 P1 - 隔离机制优化:")
    print(f"  1. 改进隔离逻辑")
    print(f"     - 避免重复隔离同一文件")
    print(f"     - 实现隔离文件清理机制")
    print(f"     - 添加隔离统计和监控")
    
    print(f"\n  2. 视频验证优化")
    print(f"     - 改进视频时长检测算法")
    print(f"     - 添加视频格式验证")
    print(f"     - 实现批量验证")
    
    print(f"\n🟢 P2 - 上传效率优化:")
    print(f"  1. 调整并发参数")
    print(f"     - 根据系统负载动态调整并发数")
    print(f"     - 优化重试延迟时间")
    print(f"     - 实现智能调度")
    
    print(f"\n  2. 文件路径管理")
    print(f"     - 实现文件路径自动修复")
    print(f"     - 添加文件移动跟踪")
    print(f"     - 完善文件状态同步")

if __name__ == "__main__":
    print("🚀 千川视频素材工作流文件管理深度分析")
    print("=" * 80)
    
    analyze_quarantine_directory()
    analyze_file_access_conflicts()
    analyze_upload_efficiency()
    analyze_missing_files()
    generate_recommendations()
    
    print(f"\n✅ 分析完成!")
    print(f"📊 建议优先处理文件访问冲突问题，这是影响上传效率的主要原因")
