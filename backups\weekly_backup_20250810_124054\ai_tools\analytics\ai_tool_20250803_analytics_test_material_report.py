#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 测试账户素材审核报表 - 完整版数据分析
清理条件: 系统升级时可考虑重构
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime, timedelta, timezone
import pytz
from typing import Dict, List, Any, Optional
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import (
    LocalCreative, Campaign, PlatformCreative, AdAccount, Principal
)
from sqlalchemy import func, and_, or_, desc, asc, text
from sqlalchemy.orm import joinedload

# 中国时区
CHINA_TZ = pytz.timezone('Asia/Shanghai')

def convert_to_china_time(dt):
    """将数据库时间转换为中国时区"""
    if dt is None:
        return None
    if dt.tzinfo is None:
        # 假设数据库存储的是UTC时间
        dt = dt.replace(tzinfo=timezone.utc)
    return dt.astimezone(CHINA_TZ)

def render_test_material_analytics_page():
    """测试账户素材审核报表页面"""
    st.header("📊 测试账户素材审核报表 (完整版)")
    st.markdown("**专注测试账户数据** - 全面分析视频素材工作流，包括上传、创建计划、审核等各环节数据")
    
    # 重要提示
    st.info("🎯 **数据范围**: 本报表仅显示TEST账户的数据，不包含DELIVERY账户（正式投放户）的任何信息")
    
    # 侧边栏配置
    with st.sidebar:
        st.markdown("### 📋 报表配置")
        
        # 时间范围选择
        time_range_type = st.selectbox(
            "时间范围",
            ["最近7天", "最近30天", "自定义范围"],
            help="选择数据统计的时间范围"
        )
        
        if time_range_type == "自定义范围":
            col1, col2 = st.columns(2)
            with col1:
                start_date = st.date_input(
                    "开始日期",
                    value=datetime.now().date() - timedelta(days=7)
                )
            with col2:
                end_date = st.date_input(
                    "结束日期", 
                    value=datetime.now().date()
                )
        else:
            days = 7 if time_range_type == "最近7天" else 30
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=days)
        
        # 分析维度选择
        analysis_mode = st.selectbox(
            "分析维度",
            ["📊 综合仪表板", "🎬 视频素材分析", "📋 计划创建分析", "🔍 审核流程分析", "📈 时间趋势分析"],
            help="选择要分析的数据维度"
        )
        
        # 刷新控制
        st.markdown("---")
        if st.button("🔄 刷新数据", use_container_width=True):
            st.rerun()
    
    # 获取数据
    with st.spinner("正在加载数据..."):
        data = load_test_account_data(start_date, end_date)
    
    if not data:
        st.warning("📭 选定时间范围内没有找到测试账户数据")
        return
    
    # 根据分析模式显示不同内容
    if analysis_mode == "📊 综合仪表板":
        render_comprehensive_dashboard(data)
    elif analysis_mode == "🎬 视频素材分析":
        render_material_analysis(data)
    elif analysis_mode == "📋 计划创建分析":
        render_campaign_analysis(data)
    elif analysis_mode == "🔍 审核流程分析":
        render_review_analysis(data)
    elif analysis_mode == "📈 时间趋势分析":
        render_trend_analysis(data, start_date, end_date)

def load_test_account_data(start_date, end_date) -> Dict[str, Any]:
    """加载测试账户数据 - 修复时区和审核通过率计算问题"""
    try:
        with database_session() as db:
            # 修复时区处理：使用本地时区查询
            start_datetime = datetime.combine(start_date, datetime.min.time())
            end_datetime = datetime.combine(end_date, datetime.max.time())

            # 使用AT TIME ZONE进行正确的时区转换
            start_datetime_utc = start_datetime.replace(tzinfo=CHINA_TZ).astimezone(timezone.utc)
            end_datetime_utc = end_datetime.replace(tzinfo=CHINA_TZ).astimezone(timezone.utc)

            # 获取测试账户列表
            test_accounts = db.query(AdAccount).options(
                joinedload(AdAccount.principal)
            ).filter(
                AdAccount.account_type == 'TEST',
                AdAccount.status == 'active'
            ).all()

            if not test_accounts:
                return {}

            test_account_ids = [acc.id for acc in test_accounts]

            # 获取本地素材数据 - 使用正确的时区查询
            local_creatives = db.query(LocalCreative).options(
                joinedload(LocalCreative.principal),
                joinedload(LocalCreative.uploaded_to_account)
            ).filter(
                LocalCreative.uploaded_to_account_id.in_(test_account_ids),
                LocalCreative.created_at >= start_datetime_utc,
                LocalCreative.created_at <= end_datetime_utc
            ).all()

            # 获取平台素材数据 - 使用正确的时区查询
            platform_creatives = db.query(PlatformCreative).options(
                joinedload(PlatformCreative.account),
                joinedload(PlatformCreative.local_creative)
            ).filter(
                PlatformCreative.account_id.in_(test_account_ids),
                PlatformCreative.created_at >= start_datetime_utc,
                PlatformCreative.created_at <= end_datetime_utc
            ).all()

            # 获取计划数据 - 使用正确的时区查询
            campaigns = db.query(Campaign).options(
                joinedload(Campaign.account)
            ).filter(
                Campaign.account_id.in_(test_account_ids),
                Campaign.created_at >= start_datetime_utc,
                Campaign.created_at <= end_datetime_utc
            ).all()

            # 在session内预处理数据，避免lazy loading问题
            processed_data = {
                'test_accounts': [
                    {
                        'id': acc.id,
                        'name': acc.name,
                        'account_type': acc.account_type,
                        'principal_name': acc.principal.name if acc.principal else '未知'
                    } for acc in test_accounts
                ],
                'local_creatives_raw': [
                    {
                        'id': lc.id,
                        'filename': lc.filename,
                        'status': lc.status,
                        'created_at': lc.created_at,
                        'updated_at': lc.updated_at,
                        'uploaded_to_account_id': lc.uploaded_to_account_id,
                        'principal_name': lc.principal.name if lc.principal else '未知',
                        'account_name': lc.uploaded_to_account.name if lc.uploaded_to_account else '未知',
                        'harvest_status': lc.harvest_status,
                        'material_id_qc': lc.material_id_qc
                    } for lc in local_creatives
                ],
                'platform_creatives_raw': [
                    {
                        'id': pc.id,
                        'material_id_qc': pc.material_id_qc,
                        'review_status': pc.review_status,
                        'created_at': pc.created_at,
                        'local_creative_id': pc.local_creative_id,
                        'account_id': pc.account_id,
                        'account_name': pc.account.name if pc.account else '未知'
                    } for pc in platform_creatives
                ],
                'campaigns_raw': [
                    {
                        'id': c.id,
                        'campaign_id_qc': c.campaign_id_qc,
                        'status': c.status,
                        'created_at': c.created_at,
                        'last_updated': c.last_updated,
                        'first_appeal_at': c.first_appeal_at,
                        'last_appeal_at': c.last_appeal_at,
                        'account_id': c.account_id,
                        'account_name': c.account.name if c.account else '未知'
                    } for c in campaigns
                ],
                'date_range': (start_date, end_date)
            }

            return processed_data

    except Exception as e:
        st.error(f"❌ 数据加载失败: {e}")
        return {}

def render_comprehensive_dashboard(data: Dict[str, Any]):
    """渲染综合仪表板 - 修正版本"""
    st.markdown("## 📊 综合数据仪表板")
    st.markdown("**专注测试账户素材审核报表** - 全面分析视频素材工作流，包括上传、创建计划、审核等各环节数据")

    # 重要说明
    st.info("🎯 **审核通过率定义**: 最终收割的审核通过素材数量 ÷ 上传的视频素材总数量")

    # 核心指标卡片
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric(
            "测试账户数量",
            len(data['test_accounts']),
            help="活跃的测试账户总数"
        )

    with col2:
        st.metric(
            "视频素材总数",
            len(data['local_creatives_raw']),
            help="上传的视频素材总数"
        )

    with col3:
        st.metric(
            "创建计划数",
            len(data['campaigns_raw']),
            help="创建的广告计划总数"
        )

    with col4:
        # 修正审核通过率计算：最终收割的审核通过素材/上传的视频素材数量
        harvested_materials = [lc for lc in data['local_creatives_raw'] if lc['harvest_status'] == 'harvested']
        total_materials = len(data['local_creatives_raw'])
        harvest_rate = len(harvested_materials) / total_materials * 100 if total_materials > 0 else 0
        st.metric(
            "审核通过率",
            f"{harvest_rate:.1f}%",
            help="最终收割的审核通过素材占上传视频素材总数的比例"
        )
    
    # 详细统计表格
    st.markdown("### 📋 账户详细统计")
    
    account_stats = []
    for account_data in data['test_accounts']:
        account_id = account_data['id']
        account_materials = [lc for lc in data['local_creatives_raw'] if lc['uploaded_to_account_id'] == account_id]
        account_campaigns = [c for c in data['campaigns_raw'] if c['account_id'] == account_id]

        # 修正统计逻辑：基于素材收割状态计算通过率
        harvested_materials = [lc for lc in account_materials if lc['harvest_status'] == 'harvested']
        approved_materials = [lc for lc in account_materials if lc['status'] == 'approved']
        rejected_materials = [lc for lc in account_materials if lc['status'] == 'rejected']

        # 计算真实的审核通过率（收割素材/总素材）
        harvest_rate = len(harvested_materials) / len(account_materials) * 100 if account_materials else 0

        account_stats.append({
            '账户名称': account_data['name'],
            '主体': account_data['principal_name'],
            '视频素材数': len(account_materials),
            '创建计划数': len(account_campaigns),
            '审核通过': len(approved_materials),
            '已收割': len(harvested_materials),
            '被拒绝': len(rejected_materials),
            '通过率': f"{harvest_rate:.1f}%"
        })
    
    if account_stats:
        df_stats = pd.DataFrame(account_stats)
        st.dataframe(
            df_stats,
            use_container_width=True,
            hide_index=True
        )

        # 添加总计行
        total_materials = sum([stat['视频素材数'] for stat in account_stats])
        total_campaigns = sum([stat['创建计划数'] for stat in account_stats])
        total_approved = sum([stat['审核通过'] for stat in account_stats])
        total_harvested = sum([stat['已收割'] for stat in account_stats])
        total_rejected = sum([stat['被拒绝'] for stat in account_stats])
        overall_harvest_rate = total_harvested / total_materials * 100 if total_materials > 0 else 0

        st.markdown("### 📈 总体统计")
        col_total1, col_total2, col_total3, col_total4 = st.columns(4)

        with col_total1:
            st.metric("总视频素材", total_materials)
        with col_total2:
            st.metric("总创建计划", total_campaigns)
        with col_total3:
            st.metric("总收割素材", total_harvested)
        with col_total4:
            st.metric("整体通过率", f"{overall_harvest_rate:.1f}%")

    # 添加素材流程状态分析
    st.markdown("### 🔄 素材流程状态分析")

    # 统计各状态素材数量
    status_stats = {}
    harvest_stats = {}

    for lc in data['local_creatives_raw']:
        status = lc['status']
        harvest_status = lc.get('harvest_status', 'unknown')

        status_stats[status] = status_stats.get(status, 0) + 1
        harvest_stats[harvest_status] = harvest_stats.get(harvest_status, 0) + 1

    col_status1, col_status2 = st.columns(2)

    with col_status1:
        st.markdown("#### 📊 素材状态分布")
        status_df = pd.DataFrame([
            {'状态': status, '数量': count, '占比': f"{count/total_materials*100:.1f}%"}
            for status, count in status_stats.items()
        ])
        st.dataframe(status_df, use_container_width=True, hide_index=True)

    with col_status2:
        st.markdown("#### 🎯 收割状态分布")
        harvest_df = pd.DataFrame([
            {'收割状态': status, '数量': count, '占比': f"{count/total_materials*100:.1f}%"}
            for status, count in harvest_stats.items()
        ])
        st.dataframe(harvest_df, use_container_width=True, hide_index=True)

def render_material_analysis(data: Dict[str, Any]):
    """渲染视频素材分析"""
    st.markdown("## 🎬 视频素材全流程分析")
    
    # 素材状态分布
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### 📊 素材状态分布")
        status_counts = {}
        for lc in data['local_creatives_raw']:
            status_counts[lc['status']] = status_counts.get(lc['status'], 0) + 1
        
        if status_counts:
            fig = px.pie(
                values=list(status_counts.values()),
                names=list(status_counts.keys()),
                title="本地素材状态分布"
            )
            st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        st.markdown("### 📈 平台素材审核状态")
        review_counts = {}
        for pc in data['platform_creatives_raw']:
            review_counts[pc['review_status']] = review_counts.get(pc['review_status'], 0) + 1
        
        if review_counts:
            fig = px.bar(
                x=list(review_counts.keys()),
                y=list(review_counts.values()),
                title="平台素材审核状态分布"
            )
            st.plotly_chart(fig, use_container_width=True)
    
    # 素材详细列表
    st.markdown("### 📋 素材详细信息")
    
    material_details = []
    for lc in data['local_creatives_raw']:
        # 查找对应的平台素材
        platform_creative = next((pc for pc in data['platform_creatives_raw'] if pc['local_creative_id'] == lc['id']), None)
        
        material_details.append({
            'ID': lc['id'],
            '文件名': lc['filename'] or '未知',
            '主体': lc['principal_name'],
            '上传账户': lc['account_name'],
            '本地状态': lc['status'],
            '平台审核状态': platform_creative['review_status'] if platform_creative else '未上传',
            '创建时间': convert_to_china_time(lc['created_at']).strftime('%Y-%m-%d %H:%M:%S') if lc['created_at'] else '未知',
            '更新时间': convert_to_china_time(lc['updated_at']).strftime('%Y-%m-%d %H:%M:%S') if lc['updated_at'] else '未知'
        })
    
    if material_details:
        df_materials = pd.DataFrame(material_details)
        st.dataframe(
            df_materials,
            use_container_width=True,
            hide_index=True
        )

def render_campaign_analysis(data: Dict[str, Any]):
    """渲染计划创建分析"""
    st.markdown("## 📋 广告计划创建分析")
    
    # 计划状态统计
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### 📊 计划状态分布")
        status_counts = {}
        for campaign in data['campaigns_raw']:
            status_counts[campaign['status']] = status_counts.get(campaign['status'], 0) + 1
        
        if status_counts:
            fig = px.pie(
                values=list(status_counts.values()),
                names=list(status_counts.keys()),
                title="计划状态分布"
            )
            st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        st.markdown("### 📈 提审状态分析")
        appeal_stats = {
            '已提审': len([c for c in data['campaigns_raw'] if c['first_appeal_at'] is not None]),
            '未提审': len([c for c in data['campaigns_raw'] if c['first_appeal_at'] is None]),
            '提审成功': len([c for c in data['campaigns_raw'] if c['status'] in ['MONITORING', 'APPROVED']]),
            '提审失败': len([c for c in data['campaigns_raw'] if c['status'] in ['REJECTED', 'APPEAL_TIMEOUT']])
        }
        
        fig = px.bar(
            x=list(appeal_stats.keys()),
            y=list(appeal_stats.values()),
            title="提审状态统计"
        )
        st.plotly_chart(fig, use_container_width=True)

def render_review_analysis(data: Dict[str, Any]):
    """渲染审核流程分析"""
    st.markdown("## 🔍 审核流程深度分析")
    
    # 审核时效分析
    st.markdown("### ⏱️ 审核时效分析")
    
    review_times = []
    for campaign in data['campaigns_raw']:
        if campaign['first_appeal_at'] and campaign['status'] in ['MONITORING', 'APPROVED', 'REJECTED']:
            # 计算从提审到有结果的时间
            if campaign['last_updated']:
                duration = (campaign['last_updated'] - campaign['first_appeal_at']).total_seconds() / 3600  # 小时
                review_times.append({
                    '计划ID': campaign['campaign_id_qc'],
                    '账户': campaign['account_name'],
                    '提审时间': convert_to_china_time(campaign['first_appeal_at']).strftime('%Y-%m-%d %H:%M:%S'),
                    '审核时长(小时)': round(duration, 2),
                    '最终状态': campaign['status']
                })
    
    if review_times:
        df_times = pd.DataFrame(review_times)
        
        # 显示统计信息
        col1, col2, col3 = st.columns(3)
        with col1:
            avg_time = df_times['审核时长(小时)'].mean()
            st.metric("平均审核时长", f"{avg_time:.1f}小时")
        with col2:
            max_time = df_times['审核时长(小时)'].max()
            st.metric("最长审核时长", f"{max_time:.1f}小时")
        with col3:
            min_time = df_times['审核时长(小时)'].min()
            st.metric("最短审核时长", f"{min_time:.1f}小时")
        
        # 显示详细表格
        st.dataframe(df_times, use_container_width=True, hide_index=True)
    else:
        st.info("📭 暂无完整的审核时效数据")

def render_trend_analysis(data: Dict[str, Any], start_date, end_date):
    """渲染时间趋势分析"""
    st.markdown("## 📈 时间趋势分析")
    
    # 按日期统计数据
    daily_stats = {}
    
    # 统计每日素材上传数量
    for lc in data['local_creatives_raw']:
        if lc['created_at']:
            date_key = convert_to_china_time(lc['created_at']).date()
            if date_key not in daily_stats:
                daily_stats[date_key] = {'materials': 0, 'campaigns': 0, 'approved': 0}
            daily_stats[date_key]['materials'] += 1

    # 统计每日计划创建数量
    for campaign in data['campaigns_raw']:
        if campaign['created_at']:
            date_key = convert_to_china_time(campaign['created_at']).date()
            if date_key not in daily_stats:
                daily_stats[date_key] = {'materials': 0, 'campaigns': 0, 'approved': 0}
            daily_stats[date_key]['campaigns'] += 1
            
            if campaign['status'] in ['MONITORING', 'APPROVED']:
                daily_stats[date_key]['approved'] += 1
    
    if daily_stats:
        # 创建趋势图
        dates = sorted(daily_stats.keys())
        materials_count = [daily_stats[date]['materials'] for date in dates]
        campaigns_count = [daily_stats[date]['campaigns'] for date in dates]
        approved_count = [daily_stats[date]['approved'] for date in dates]
        
        fig = make_subplots(
            rows=2, cols=1,
            subplot_titles=('每日素材上传趋势', '每日计划创建与通过趋势'),
            vertical_spacing=0.1
        )
        
        # 素材上传趋势
        fig.add_trace(
            go.Scatter(x=dates, y=materials_count, name='素材上传', line=dict(color='blue')),
            row=1, col=1
        )
        
        # 计划创建趋势
        fig.add_trace(
            go.Scatter(x=dates, y=campaigns_count, name='计划创建', line=dict(color='green')),
            row=2, col=1
        )
        
        # 计划通过趋势
        fig.add_trace(
            go.Scatter(x=dates, y=approved_count, name='计划通过', line=dict(color='orange')),
            row=2, col=1
        )
        
        fig.update_layout(height=600, title_text="测试账户工作流时间趋势")
        st.plotly_chart(fig, use_container_width=True)
    else:
        st.info("📭 暂无趋势数据")

if __name__ == "__main__":
    render_test_material_analytics_page()
