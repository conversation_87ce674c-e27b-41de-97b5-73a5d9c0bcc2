# -*- coding: utf-8 -*-
"""
[V50.0] Celery 任务定义模块
"""
import os
from datetime import datetime, timedelta, timezone
from qianchuan_aw.celery_app import app
from qianchuan_aw.utils.config_manager import get_config_manager
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import LocalCreative, AdAccount
from qianchuan_aw.workflows import scheduler, fast_monitor
from qianchuan_aw.services import event_detector
from qianchuan_aw.utils.logger import logger

# 加载一次配置，供所有任务共享
config_manager = get_config_manager()
app_settings = config_manager.get_config()

def _run_task(task_func, *args, **kwargs):
    """[V52.3] 辅助函数，用于在独立的数据库会话中运行同步任务。"""
    with database_session() as db:
        task_func(db, app_settings, *args, **kwargs)


def _cleanup_excess_browsers():
    """清理多余的浏览器进程"""
    try:
        import psutil

        browser_count = 0
        browsers_to_kill = []

        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'memory_info']):
            try:
                proc_info = proc.info
                name = proc_info['name'].lower()

                if any(browser in name for browser in ['chrome', 'chromium']):
                    cmdline = ' '.join(proc_info.get('cmdline', []))
                    if any(flag in cmdline for flag in ['--no-sandbox', '--disable-dev-shm-usage']):
                        browser_count += 1
                        memory_mb = proc_info['memory_info'].rss / 1024 / 1024
                        browsers_to_kill.append({
                            'pid': proc_info['pid'],
                            'memory_mb': memory_mb
                        })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        # 如果浏览器进程超过3个，清理内存使用最高的
        if browser_count > 3:
            browsers_to_kill.sort(key=lambda x: x['memory_mb'], reverse=True)
            killed_count = 0

            for browser in browsers_to_kill[3:]:  # 保留前3个
                try:
                    proc = psutil.Process(browser['pid'])
                    proc.terminate()
                    proc.wait(timeout=3)
                    killed_count += 1
                    logger.info(f"🧹 清理浏览器进程 PID:{browser['pid']} ({browser['memory_mb']:.1f}MB)")
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired):
                    continue

            if killed_count > 0:
                logger.success(f"✅ 清理了 {killed_count} 个多余的浏览器进程")
        else:
            logger.debug(f"浏览器进程数量正常: {browser_count}")

    except Exception as e:
        logger.error(f"浏览器进程清理失败: {e}")

# --- [Refactor] Producer-Consumer 任务定义 ---

@app.task(name="tasks.ingest_and_upload", ignore_result=True)
def task_ingest_and_upload():
    """[Producer V2] 扫描文件并将其信息录入数据库。"""
    if not app_settings.get('workflow', {}).get('file_ingestion', {}).get('enabled', True):
        logger.info("[Task Skip] File Ingestion is disabled.")
        return
    logger.info("[Task Start] Ingest Files")

    # [V-Fix-1] 在文件摄取前先恢复失败的素材
    _run_task(scheduler.handle_failed_upload_recovery)

    # 然后执行正常的文件摄取
    _run_task(scheduler.handle_file_ingestion)
    logger.info("[Task End] Ingest Files")

@app.task(name="tasks.group_and_dispatch", ignore_result=True)
def task_group_and_dispatch():
    """[Dispatcher] 聚合素材并按组派发上传任务。"""
    if not app_settings.get('workflow', {}).get('group_dispatch', {}).get('enabled', True):
        logger.info("[Task Skip] Group Dispatch is disabled.")
        return
    logger.info("[Task Start] Group & Dispatch")
    _run_task(scheduler.group_and_dispatch_uploads)
    logger.info("[Task End] Group & Dispatch")

@app.task(name="tasks.upload_single_video", bind=True, max_retries=5)
def upload_single_video(self, local_creative_id: int, account_id: int, file_path: str, principal_name: str):
    """[V2.0 Optimized] 处理单个视频的上传 - 集成原子状态管理器"""
    import sys
    from pathlib import Path
    from qianchuan_aw.utils.atomic_state_manager import AtomicStateManager

    attempt = self.request.retries + 1
    filename = os.path.basename(file_path)
    logger.info(f"🚀 [Task Start] Uploading video: {filename} (attempt {attempt}/6)")

    try:
        with database_session() as db:
            # 🔧 使用原子状态管理器确保状态一致性
            manager = AtomicStateManager(db)

            # 验证素材状态并原子性转换为processing
            creative = db.query(LocalCreative).filter(
                LocalCreative.id == local_creative_id
            ).first()

            if not creative:
                raise ValueError(f"素材 {local_creative_id} 不存在")

            if creative.status not in ['pending_upload', 'processing']:
                logger.warning(f"⚠️ 素材 {local_creative_id} 状态异常: {creative.status}，跳过上传")
                return {'skipped': True, 'reason': f'状态异常: {creative.status}'}

            # 🔒 原子状态转换：pending_upload → processing → uploaded_pending_plan
            try:
                with manager.atomic_state_transition(local_creative_id, creative.status, 'processing') as processing_creative:
                    logger.info(f"🔒 状态锁定成功: {creative.status} → processing")

                    # 执行实际上传逻辑
                    result = scheduler.process_single_video_upload(
                        db, app_settings, local_creative_id, account_id, file_path, principal_name
                    )

                    # 上传成功，转换为最终状态
                    processing_creative.status = 'uploaded_pending_plan'
                    processing_creative.updated_at = datetime.now()

                    logger.success(f"✅ [Task Success] Video uploaded: {filename}")
                    logger.info(f"   Material ID: {result.get('material_id')}")
                    logger.info(f"   Video ID: {result.get('video_id')}")
                    logger.info(f"   状态转换: processing → uploaded_pending_plan")

                    return result

            except Exception as upload_exc:
                # 原子状态管理器会自动回滚状态
                logger.error(f"❌ 上传失败，状态已自动回滚: {upload_exc}")
                raise upload_exc

    except Exception as exc:
        logger.error(f"❌ [Task Failed] Error uploading video {filename}: {exc}", exc_info=True)

        # 🔄 智能重试逻辑
        if attempt < 6:  # 最多重试6次
            retry_delay = min(60 * attempt, 300)  # 递增延迟，最大5分钟
            logger.warning(f"🔄 [Task Retry] Will retry in {retry_delay}s (attempt {attempt}/6)")
            raise self.retry(exc=exc, countdown=retry_delay)
        else:
            logger.error(f"💀 [Task Failed] Max retries exceeded for {filename}")

            # 最终失败，标记为失败状态
            try:
                with database_session() as db:
                    manager = AtomicStateManager(db)
                    with manager.atomic_state_transition(local_creative_id, 'processing', 'upload_failed') as failed_creative:
                        failed_creative.error_message = str(exc)[:500]
                        logger.info(f"🏷️ 标记为失败状态: processing → upload_failed")
            except Exception as mark_failed_exc:
                logger.error(f"标记失败状态时出错: {mark_failed_exc}")

            raise exc

@app.task(name="tasks.batch_upload_videos")
def batch_upload_videos(batch_size: int = 5):
    """[V2.0 Batch] 批量上传视频任务 - 提升处理效率"""
    from qianchuan_aw.utils.atomic_state_manager import AtomicStateManager

    logger.info(f"🚀 [Batch Task Start] 批量上传视频，批次大小: {batch_size}")

    try:
        with database_session() as db:
            manager = AtomicStateManager(db)

            # 🔧 [紧急修复] 首先将pending_grouping状态转换为pending_upload
            grouping_creatives = db.query(LocalCreative).filter(
                LocalCreative.status == 'pending_grouping'
            ).all()

            if grouping_creatives:
                logger.info(f"🔄 发现 {len(grouping_creatives)} 个pending_grouping状态素材，转换为pending_upload")
                for creative in grouping_creatives:
                    creative.status = 'pending_upload'
                    creative.updated_at = datetime.now()
                db.commit()
                logger.success(f"✅ 成功转换 {len(grouping_creatives)} 个素材状态")

            # 查找待上传的素材
            pending_creatives = db.query(LocalCreative).filter(
                LocalCreative.status == 'pending_upload'
            ).limit(batch_size * 3).all()  # 获取更多素材以便分批

            if not pending_creatives:
                logger.info("📭 没有待上传的素材")
                return {'processed': 0, 'batches': 0}

            logger.info(f"📋 找到 {len(pending_creatives)} 个待上传素材")

            # 按主体分组
            from collections import defaultdict
            grouped_by_principal = defaultdict(list)

            for creative in pending_creatives:
                principal_id = creative.principal_id
                grouped_by_principal[principal_id].append(creative)

            total_processed = 0
            total_batches = 0

            # 为每个主体创建批次
            for principal_id, creatives in grouped_by_principal.items():
                logger.info(f"🏢 处理主体 {principal_id} 的 {len(creatives)} 个素材")

                # 分批处理
                for i in range(0, len(creatives), batch_size):
                    batch = creatives[i:i + batch_size]
                    batch_num = i // batch_size + 1

                    logger.info(f"📦 处理批次 {batch_num}，包含 {len(batch)} 个素材")

                    # 并行派发批次中的所有上传任务
                    upload_tasks = []
                    for creative in batch:
                        try:
                            # 🔧 [负载均衡修复] 实现真正的轮询分配，避免集中分配事故
                            target_account_id = creative.uploaded_to_account_id
                            if not target_account_id:
                                # 获取所有健康的TEST账户
                                from qianchuan_aw.workflows.scheduler import is_account_healthy, last_used_account_index
                                from qianchuan_aw.sdk_qc.client import QianchuanClient
                                from qianchuan_aw.utils.config_manager import get_config_manager

                                config_manager = get_config_manager()
                                app_settings = config_manager.get_config()
                                client = QianchuanClient(
                                    app_id=app_settings['api_credentials']['app_id'],
                                    secret=app_settings['api_credentials']['secret'],
                                    principal_id=creative.principal_id
                                )

                                # 获取健康的TEST账户列表
                                test_accounts = db.query(AdAccount).filter(
                                    AdAccount.account_type == 'TEST',
                                    AdAccount.status == 'active',
                                    AdAccount.principal_id == creative.principal_id
                                ).all()

                                # 过滤健康账户
                                healthy_accounts = []
                                for acc in test_accounts:
                                    try:
                                        if is_account_healthy(db, client, acc):
                                            healthy_accounts.append(acc)
                                    except Exception as e:
                                        logger.warning(f"账户 {acc.name} 健康检查失败: {e}")

                                if healthy_accounts:
                                    # 🔧 [负载均衡] 使用轮询机制选择账户
                                    current_index = last_used_account_index[creative.principal_id]
                                    selected_account = healthy_accounts[current_index % len(healthy_accounts)]
                                    last_used_account_index[creative.principal_id] = (current_index + 1) % len(healthy_accounts)

                                    target_account_id = selected_account.id
                                    logger.info(f"🔄 [负载均衡] 为素材 {creative.id} 轮询选择TEST账户: {selected_account.name} (索引: {current_index % len(healthy_accounts)})")
                                else:
                                    logger.error(f"❌ 找不到健康的TEST账户，跳过素材 {creative.id}")
                                    continue

                            # 使用原子状态管理器安全派发任务
                            manager.safe_dispatch_upload_task(
                                creative_id=creative.id,
                                account_id=target_account_id,
                                file_path=creative.file_path,
                                principal_name=creative.principal.name if creative.principal else "unknown"
                            )
                            upload_tasks.append(creative.id)
                            total_processed += 1

                        except Exception as e:
                            logger.error(f"❌ 派发上传任务失败 (Creative {creative.id}): {e}")

                    total_batches += 1
                    logger.success(f"✅ 批次 {batch_num} 派发完成，{len(upload_tasks)} 个任务")

                    # 批次间短暂延迟，避免系统过载
                    import time
                    time.sleep(1)

            logger.success(f"🎯 批量上传任务完成: {total_processed} 个素材, {total_batches} 个批次")

            return {
                'processed': total_processed,
                'batches': total_batches,
                'success': True
            }

    except Exception as exc:
        logger.error(f"❌ [Batch Task Failed] 批量上传任务失败: {exc}", exc_info=True)
        return {
            'processed': 0,
            'batches': 0,
            'success': False,
            'error': str(exc)
        }

@app.task(name="tasks.batch_create_plans")
def batch_create_plans(batch_size: int = 10):
    """[V2.0 Batch] 批量创建计划任务 - 修复版本"""
    logger.info(f"🚀 [Batch Task Start] 批量创建计划，批次大小: {batch_size}")

    try:
        with database_session() as db:
            # 查找已上传待创建计划的素材
            ready_creatives = db.query(LocalCreative).filter(
                LocalCreative.status == 'uploaded_pending_plan'
            ).limit(batch_size).all()  # 限制处理数量

            if not ready_creatives:
                logger.info("📭 没有待创建计划的素材")
                return {'processed': 0, 'batches': 0}

            logger.info(f"📋 找到 {len(ready_creatives)} 个待创建计划的素材")

            # 🔧 [修复] 直接调用现有的计划创建逻辑，让它处理所有就绪的素材
            try:
                logger.info(f"📦 开始批量创建计划...")

                # 调用现有的计划创建逻辑
                _run_task(scheduler.handle_plan_creation)

                # 检查处理结果
                remaining_creatives = db.query(LocalCreative).filter(
                    LocalCreative.status == 'uploaded_pending_plan'
                ).count()

                processed_count = len(ready_creatives) - remaining_creatives
                if processed_count < 0:
                    processed_count = 0  # 防止负数

                logger.success(f"✅ 批量创建计划完成，处理了约 {processed_count} 个素材")

                return {
                    'processed': processed_count,
                    'batches': 1,
                    'success': True,
                    'remaining': remaining_creatives
                }

            except Exception as e:
                logger.error(f"❌ 批量创建计划失败: {e}")
                return {
                    'processed': 0,
                    'batches': 0,
                    'success': False,
                    'error': str(e)
                }

    except Exception as exc:
        logger.error(f"❌ [Batch Task Failed] 批量创建计划任务失败: {exc}", exc_info=True)
        return {
            'processed': 0,
            'batches': 0,
            'success': False,
            'error': str(exc)
        }

@app.task(name="tasks.create_plans")
def task_create_plans():
    if not app_settings.get('workflow', {}).get('plan_creation', {}).get('enabled', True):
        logger.info("[Task Skip] Plan Creation is disabled.")
        return
    logger.info("[Task Start] Create Plans")
    _run_task(scheduler.handle_plan_creation)
    logger.info("[Task End] Create Plans")

@app.task(name="tasks.submit_plans")
def task_submit_plans():
    if not app_settings.get('workflow', {}).get('plan_submission', {}).get('enabled', True):
        logger.info("[Task Skip] Plan Submission is disabled.")
        return
    logger.info("[Task Start] Submit Plans")
    _run_task(scheduler.handle_plan_submission)
    logger.info("[Task End] Submit Plans")

@app.task(name="tasks.appeal_plans")
def task_appeal_plans():
    if not app_settings.get('workflow', {}).get('plan_appeal', {}).get('enabled', True):
        logger.info("[Task Skip] Plan Appeal is disabled.")
        return

    logger.info("[Task Start] Appeal Plans")

    # 🛡️ 强制测试账户检查
    try:
        from ai_tools.workflow.ai_tool_20250803_workflow_test_account_limiter import test_account_limiter
        test_account_ids = test_account_limiter.get_test_account_ids()
        if not test_account_ids:
            logger.warning("⚠️ [Task Skip] 没有找到测试账户，跳过提审操作")
            return
        logger.info(f"🛡️ 提审限制在 {len(test_account_ids)} 个测试账户")
    except Exception as e:
        logger.error(f"❌ 测试账户检查失败，跳过提审: {e}")
        return

    # 🧹 浏览器进程预清理
    _cleanup_excess_browsers()

    # 先执行全面超时检查
    _run_task(scheduler.handle_timeout_campaigns_comprehensive)
    # 再执行正常的提审流程
    _run_task(scheduler.handle_plans_awaiting_appeal)

    # 🧹 浏览器进程后清理
    _cleanup_excess_browsers()

    logger.info("[Task End] Appeal Plans")

@app.task(name="tasks.harvest_materials")
def task_harvest_materials():
    if not app_settings.get('workflow', {}).get('independent_harvest', {}).get('enabled', True):
        logger.info("[Task Skip] Independent Material Harvest is disabled.")
        return
    logger.info("[Task Start] Independent Material Harvest")
    _run_task(scheduler.handle_independent_material_harvest)
    logger.info("[Task End] Independent Material Harvest")

@app.task(name="tasks.monitor_materials")
def task_monitor_materials():
    if not app_settings.get('workflow', {}).get('material_monitoring', {}).get('enabled', True):
        logger.info("[Task Skip] Material Monitoring is disabled.")
        return

    logger.info("[Task Start] Monitor Materials")

    # 🛡️ 强制测试账户检查
    try:
        from ai_tools.workflow.ai_tool_20250803_workflow_test_account_limiter import test_account_limiter
        test_account_ids = test_account_limiter.get_test_account_ids()
        if not test_account_ids:
            logger.warning("⚠️ [Task Skip] 没有找到测试账户，跳过素材监控")
            return
        logger.info(f"🛡️ 素材监控限制在 {len(test_account_ids)} 个测试账户")
    except Exception as e:
        logger.error(f"❌ 测试账户检查失败，跳过素材监控: {e}")
        return

    # 🧹 浏览器进程预清理
    _cleanup_excess_browsers()

    _run_task(scheduler.handle_monitoring_of_materials)

    # 🧹 浏览器进程后清理
    _cleanup_excess_browsers()

    logger.info("[Task End] Monitor Materials")

@app.task(name="tasks.manage_comments")
def task_manage_comments():
    # 检查是否启用评论管理
    if not app_settings.get('workflow', {}).get('comment_management', {}).get('enabled', True):
        logger.info("[Task Skip] Comment Management is disabled.")
        return
    logger.info("[Task Start] Manage Comments")
    _run_task(scheduler.handle_comment_management)
    logger.info("[Task End] Manage Comments")

@app.task(name="tasks.check_violations")
def task_check_violations():
    """独立的违规检测任务"""
    # 检查是否启用违规检测
    if not app_settings.get('workflow', {}).get('violation_detection', {}).get('enabled', True):
        logger.info("[Task Skip] Violation Detection is disabled.")
        return
    logger.info("[Task Start] Check Violations")
    _run_task(scheduler.handle_violation_detection)
    logger.info("[Task End] Check Violations")

@app.task(name="tasks.collect_materials")
def task_collect_materials():
    """素材收集任务 - 从局域网收集视频素材"""
    # 检查是否启用素材收集
    if not app_settings.get('workflow', {}).get('material_collection', {}).get('enabled', True):
        logger.info("[Task Skip] Material Collection is disabled.")
        return

    # 检查时间范围
    from datetime import datetime
    current_time = datetime.now()
    start_hour = app_settings.get('workflow', {}).get('material_collection', {}).get('start_hour', 8)
    start_minute = app_settings.get('workflow', {}).get('material_collection', {}).get('start_minute', 30)
    end_hour = app_settings.get('workflow', {}).get('material_collection', {}).get('end_hour', 20)
    end_minute = app_settings.get('workflow', {}).get('material_collection', {}).get('end_minute', 30)

    start_time = current_time.replace(hour=start_hour, minute=start_minute, second=0, microsecond=0)
    end_time = current_time.replace(hour=end_hour, minute=end_minute, second=0, microsecond=0)

    if not (start_time <= current_time <= end_time):
        logger.info(f"[Task Skip] Material Collection outside working hours ({start_hour:02d}:{start_minute:02d}-{end_hour:02d}:{end_minute:02d})")
        return

    logger.info("[Task Start] Collect Materials")
    _run_task(scheduler.handle_material_collection)
    logger.info("[Task End] Collect Materials")

@app.task(name="tasks.run_fast_monitor")
def task_run_fast_monitor():
    if not app_settings.get('lighthouse_plan', {}).get('fast_monitor_enabled', False):
        logger.info("[Task Skip] Fast Monitor is disabled.")
        return
    logger.info("[Task Start] Fast Monitor")
    _run_task(fast_monitor.fast_monitor_workflow)
    logger.info("[Task End] Fast Monitor")

@app.task(name="tasks.detect_events")
def task_detect_events():
    if not app_settings.get('lighthouse_plan', {}).get('fast_monitor_enabled', False):
        logger.info("[Task Skip] Event Detection is disabled.")
        return
    logger.info("[Task Start] Event Detection")
    with database_session() as db:
        event_detector.check_for_new_stars(db, app_settings)
        event_detector.check_for_rejected_creatives(db, app_settings)
    logger.info("[Task End] Event Detection")

@app.task(name="tasks.reset_stale_processing_status")
def task_reset_stale_processing_status():
    """
    [Watchdog Task] 定期巡检并重置卡死在 'processing' 状态的僵尸任务。
    """
    logger.info("[Watchdog] Starting stale task patrol...")
    from qianchuan_aw.utils.db_utils import database_session
    from qianchuan_aw.utils.config_loader import load_settings

    with database_session() as db:
        # 从配置文件读取超时时间
        settings = load_settings()
        timeout_minutes = settings.get('workflow', {}).get('zombie_cleanup', {}).get('timeout_minutes', 10)
        timeout_threshold = datetime.utcnow() - timedelta(minutes=timeout_minutes)

        # 查找超时的僵尸素材
        stale_creatives = db.query(LocalCreative).filter(
            LocalCreative.status == 'processing',
            LocalCreative.updated_at < timeout_threshold
        ).all()

        if not stale_creatives:
            logger.info("[Watchdog] Patrol finished. No stale tasks found.")
            return

        logger.warning(f"[Watchdog] Found {len(stale_creatives)} stale creatives. Resetting their status...")

        for creative in stale_creatives:
            logger.warning(f"  - Resetting creative ID {creative.id} (hash: {creative.file_hash}), last updated at {creative.updated_at}")
            creative.status = 'pending_grouping' # 重置为待分组状态，让它重新进入处理队列

        db.commit()
        logger.success(f"[Watchdog] Successfully reset {len(stale_creatives)} stale creatives.")
