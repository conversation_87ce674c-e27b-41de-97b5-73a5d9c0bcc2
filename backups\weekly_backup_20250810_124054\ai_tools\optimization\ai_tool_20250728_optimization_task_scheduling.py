#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 优化千川自动化项目任务调度间隔配置
清理条件: 项目重构时可考虑删除
"""

import os
import sys
import yaml
import shutil
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def load_config():
    """加载配置文件"""
    config_path = project_root / 'config' / 'settings.yml'
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def save_config(config):
    """保存配置文件"""
    config_path = project_root / 'config' / 'settings.yml'
    
    # 备份原配置
    backup_path = config_path.with_suffix(f'.yml.backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
    shutil.copy2(config_path, backup_path)
    logger.info(f"📋 配置文件已备份到: {backup_path}")
    
    # 保存新配置
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
    logger.success(f"✅ 配置文件已更新: {config_path}")

def optimize_task_scheduling():
    """优化任务调度配置"""
    logger.info("⚙️ 开始优化任务调度配置...")
    
    config = load_config()
    workflow = config.get('workflow', {})
    
    # 当前配置
    current_config = {
        'plan_creation': workflow.get('plan_creation', {}).get('interval_seconds', 120),
        'material_monitoring': workflow.get('material_monitoring', {}).get('interval_seconds', 180),
        'independent_harvest': workflow.get('independent_harvest', {}).get('interval_seconds', 300),
        'group_dispatch': workflow.get('group_dispatch', {}).get('interval_seconds', 60),
        'file_ingestion': workflow.get('file_ingestion', {}).get('interval_seconds', 180)
    }
    
    logger.info("📊 当前任务调度间隔:")
    logger.info("-" * 40)
    for task, interval in current_config.items():
        logger.info(f"   🔹 {task}: {interval}秒")
    
    # 优化后的配置
    optimized_config = {
        'plan_creation': {
            'enabled': True,
            'interval_seconds': 60  # 从120秒调整为60秒，加快计划创建
        },
        'material_monitoring': {
            'enabled': True,
            'interval_seconds': 120,  # 从180秒调整为120秒，及时发现审核通过
            'monitor_duration_hours': 10
        },
        'independent_harvest': {
            'enabled': True,
            'interval_seconds': 180,  # 从300秒调整为180秒，快速收割
            'description': '独立素材收割机制，不依赖申诉结果定期扫描素材状态',
            'scope': {
                'test_accounts_only': True,
                'include_delivery_accounts': False,
                'account_type_filter': ['TEST']
            }
        },
        'group_dispatch': {
            'enabled': True,
            'interval_seconds': 45  # 从60秒调整为45秒，提高分组效率
        },
        'file_ingestion': {
            'enabled': True,
            'interval_seconds': 150,  # 从180秒调整为150秒，提高文件摄取速度
            'supported_formats': ['.mp4', '.mov', '.avi', '.mkv']
        },
        'plan_appeal': {
            'enabled': True,
            'interval_seconds': 600  # 从900秒调整为600秒，加快申诉处理
        }
    }
    
    logger.info("\n🎯 优化后的任务调度间隔:")
    logger.info("-" * 40)
    for task, config_dict in optimized_config.items():
        interval = config_dict.get('interval_seconds', 'N/A')
        logger.info(f"   ✨ {task}: {interval}秒")
    
    # 应用优化配置
    for task_name, task_config in optimized_config.items():
        workflow[task_name] = {**workflow.get(task_name, {}), **task_config}
        logger.success(f"✅ 已优化任务: {task_name}")
    
    config['workflow'] = workflow
    save_config(config)
    
    # 计算优化效果
    logger.info("\n📈 优化效果分析:")
    logger.info("-" * 40)
    
    improvements = []
    
    if current_config['plan_creation'] > 60:
        old_val = current_config['plan_creation']
        new_val = 60
        improvement = ((old_val - new_val) / old_val) * 100
        improvements.append(f"计划创建速度提升 {improvement:.1f}%")
    
    if current_config['material_monitoring'] > 120:
        old_val = current_config['material_monitoring']
        new_val = 120
        improvement = ((old_val - new_val) / old_val) * 100
        improvements.append(f"素材监控响应速度提升 {improvement:.1f}%")
    
    if current_config['independent_harvest'] > 180:
        old_val = current_config['independent_harvest']
        new_val = 180
        improvement = ((old_val - new_val) / old_val) * 100
        improvements.append(f"收割速度提升 {improvement:.1f}%")
    
    if improvements:
        for improvement in improvements:
            logger.success(f"📈 {improvement}")
    else:
        logger.info("📊 配置已经比较优化")
    
    return True

def create_monitoring_script():
    """创建任务调度监控脚本"""
    logger.info("📊 创建任务调度监控脚本...")
    
    monitoring_script = '''#!/usr/bin/env python3
"""任务调度效果监控脚本"""

import sys
import time
from pathlib import Path
from datetime import datetime, timedelta

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.database.connection import database_session
from qianchuan_aw.database.models import LocalCreative

def monitor_task_effectiveness(duration_minutes=10):
    """监控任务调度效果"""
    print(f"📊 开始监控任务调度效果 ({duration_minutes} 分钟)")
    print("=" * 60)
    
    start_time = datetime.now()
    end_time = start_time + timedelta(minutes=duration_minutes)
    
    # 记录初始状态
    with database_session() as db:
        initial_stats = {}
        for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:
            count = db.query(LocalCreative).filter(LocalCreative.status == status).count()
            initial_stats[status] = count
    
    print(f"⏰ 开始时间: {start_time.strftime('%H:%M:%S')}")
    print("📈 初始状态:")
    for status, count in initial_stats.items():
        print(f"   {status}: {count}")
    
    # 定期检查状态变化
    check_count = 0
    while datetime.now() < end_time:
        time.sleep(60)  # 每分钟检查一次
        check_count += 1
        
        with database_session() as db:
            current_stats = {}
            for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:
                count = db.query(LocalCreative).filter(LocalCreative.status == status).count()
                current_stats[status] = count
        
        print(f"\\n📊 检查 {check_count} ({datetime.now().strftime('%H:%M:%S')}):")
        for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:
            initial = initial_stats[status]
            current = current_stats[status]
            change = current - initial
            
            if change != 0:
                direction = "📈" if change > 0 else "📉"
                print(f"   {direction} {status}: {initial} → {current} ({change:+d})")
            else:
                print(f"   ➡️ {status}: {current}")
    
    # 最终统计
    with database_session() as db:
        final_stats = {}
        for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:
            count = db.query(LocalCreative).filter(LocalCreative.status == status).count()
            final_stats[status] = count
    
    print(f"\\n🎯 监控完成 ({datetime.now().strftime('%H:%M:%S')})")
    print("=" * 60)
    print("📊 总体变化:")
    
    total_activity = 0
    for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:
        initial = initial_stats[status]
        final = final_stats[status]
        change = final - initial
        total_activity += abs(change)
        
        if change < 0:
            print(f"✅ {status}: {initial} → {final} (减少 {abs(change)})")
        elif change > 0:
            print(f"📈 {status}: {initial} → {final} (增加 {change})")
        else:
            print(f"➡️ {status}: {final} (无变化)")
    
    print(f"\\n📈 总活动量: {total_activity} (状态变化总数)")
    
    if total_activity > 10:
        print("✅ 任务调度效果良好，工作流活跃")
    elif total_activity > 5:
        print("⚠️ 任务调度效果一般，可能需要进一步优化")
    else:
        print("❌ 任务调度效果较差，建议检查配置")

if __name__ == "__main__":
    try:
        monitor_task_effectiveness(10)
    except KeyboardInterrupt:
        print("\\n⏹️ 监控已取消")
    except Exception as e:
        print(f"❌ 监控失败: {e}")
'''
    
    monitor_path = project_root / 'ai_tools' / 'monitoring' / 'ai_tool_20250728_monitoring_task_scheduling_effectiveness.py'
    monitor_path.parent.mkdir(exist_ok=True)
    
    with open(monitor_path, 'w', encoding='utf-8') as f:
        f.write(monitoring_script)
    
    logger.success(f"✅ 监控脚本已创建: {monitor_path}")
    return monitor_path

def main():
    """主函数"""
    logger.info("🚀 开始任务调度优化...")
    
    try:
        # 1. 优化任务调度配置
        optimize_success = optimize_task_scheduling()
        
        # 2. 创建监控脚本
        monitor_path = create_monitoring_script()
        
        # 3. 提供后续操作指南
        logger.info("\n" + "="*60)
        logger.info("🎯 优化完成指南")
        logger.info("="*60)
        
        if optimize_success:
            logger.success("✅ 任务调度配置已优化")
            
            logger.info("\n📋 后续必要操作:")
            logger.info("1. 重启Celery Beat进程以应用新配置")
            logger.info("   - 停止当前Beat进程")
            logger.info("   - 删除 logs/celerybeat-schedule.db 文件")
            logger.info("   - 重新启动Beat进程")
            
            logger.info("\n2. 监控优化效果:")
            logger.info(f"   python {monitor_path}")
            
            logger.info("\n3. 预期改进效果:")
            logger.info("   - 计划创建瓶颈缓解（60秒间隔）")
            logger.info("   - 素材监控更及时（120秒间隔）")
            logger.info("   - 收割速度提升（180秒间隔）")
            
            logger.info("\n⚠️ 重要提醒:")
            logger.info("   必须重启Celery Beat进程才能使新配置生效！")
            
            return True
        else:
            logger.error("❌ 任务调度配置优化失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 优化过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
