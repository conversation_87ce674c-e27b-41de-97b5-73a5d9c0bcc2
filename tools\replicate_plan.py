# -*- coding: utf-8 -*-
"""
[V38.0] 广告计划复制工具

本工具可以根据筛选条件找到一个或多个"母版"计划，
然后将其复制为新的计划，并在此过程中允许对关键参数（如抖音号、出价等）进行修改。
"""
import sys
import os
import json
import time
import argparse
from typing import List, Dict, Any

# --- 标准的 sys.path 设置 ---
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, project_root)
sys.path.insert(0, src_path)
# ---

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.sdk_qc.client import QianchuanClient, QianchuanAPIException
from qianchuan_aw.utils.config_manager import get_config_manager
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import AdAccount, Campaign, Principal

# 导入智能名称清理工具
ai_tools_path = os.path.join(project_root, 'ai_tools')
sys.path.insert(0, ai_tools_path)
from ai_tool_20250720_plan_name_cleaner import PlanNameCleaner

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="千川广告计划高级复制工具")

    # --- 必填参数 ---
    parser.add_argument("-a", "--advertiser-id", type=int, required=True, help="要操作的千川广告账户ID (advertiser_id)")
    parser.add_argument("-p", "--principal-id", type=int, required=True, help="该账户所属的主体ID (principal_id)")

    # --- 源计划筛选参数 (至少提供一组) ---
    source_group = parser.add_argument_group('源计划筛选条件 (至少提供一项)')
    source_group.add_argument("--source-ids", type=int, nargs='+', help="要复制的源计划ID列表，用空格隔开")
    source_group.add_argument("--source-status", type=str, help="按计划状态筛选, 如 DELIVERY_OK, OFFLINE_BUDGET 等")
    source_group.add_argument("--source-aweme-id", type=int, help="按抖音号ID筛选源计划")
    source_group.add_argument("--start-date", type=str, help="计划创建开始日期 (格式: YYYY-MM-DD)")
    source_group.add_argument("--end-date", type=str, help="计划创建结束日期 (格式: YYYY-MM-DD)")

    # --- 目标参数修改 (至少提供一项修改) ---
    target_group = parser.add_argument_group('目标计划修改参数 (至少提供一项)')
    target_group.add_argument("--target-aweme-id", type=int, help="【必填，如果源计划有关联抖音号】要替换成的新抖音号ID")
    target_group.add_argument("--target-budget", type=float, help="为新计划设置新的日预算")
    target_group.add_argument("--target-cpa-bid", type=float, help="为新计划设置新的CPA出价")
    target_group.add_argument("--target-roi-goal", type=float, help="为新计划设置新的ROI目标")
    target_group.add_argument("--name-suffix", type=str, default="_copy", help="为新计划名称添加的后缀 (默认: _copy)")

    # --- 执行控制参数 ---
    control_group = parser.add_argument_group('执行控制')
    control_group.add_argument("--dry-run", action="store_true", help="预演模式，只显示将要执行的操作，不实际创建计划")
    control_group.add_argument("-y", "--yes", action="store_true", help="跳过交互式确认，直接执行")

    return parser.parse_args()

def find_source_plans(client: QianchuanClient, args: argparse.Namespace) -> List[Dict[str, Any]]:
    """根据筛选条件查找源计划"""
    filtering = {}
    # [V39.1] 使用 getattr 保证安全访问属性
    if getattr(args, 'source_ids', None):
        filtering['ids'] = args.source_ids # 保持 int
    if getattr(args, 'source_status', None):
        filtering['status'] = args.source_status
    if getattr(args, 'source_aweme_id', None):
        filtering['aweme_id'] = args.source_aweme_id # 保持 int
    if getattr(args, 'start_date', None):
        filtering['ad_create_start_date'] = args.start_date
    if getattr(args, 'end_date', None):
        filtering['ad_create_end_date'] = args.end_date

    if not filtering:
        logger.error("必须提供至少一个源计划筛选条件！(--source-ids, --source-status 等)")
        return []

    logger.info(f"正在使用筛选条件查找源计划: {json.dumps(filtering, ensure_ascii=False)}")
    plans = client.get_ad_plan_list(advertiser_id=args.advertiser_id, filtering=filtering)
    return plans

def get_status_display(status):
    """获取状态的中文显示"""
    status_map = {
        "DELIVERY_OK": "投放中",
        "DISABLE": "已暂停", 
        "LIVE_ROOM_OFF": "关联直播间未开播",
        "CAMPAIGN_DISABLE": "已被广告组暂停",
        "BUDGET_EXCEED": "预算超限",
        "AUDIT_REJECT": "审核拒绝",
        "NO_SCHEDULE": "不在投放时段"
    }
    return status_map.get(status, status)

def find_campaigns_by_criteria(source_account_id=None, source_status=None, source_aweme_id=None,
                              start_date=None, end_date=None, target_account_ids=None, new_plan_prefix=None):
    """
    根据条件查找符合复制条件的计划（调用千川API获取最新数据）

    Args:
        source_account_id: 源账户ID筛选
        source_status: 源计划状态筛选
        source_aweme_id: 源计划抖音号筛选
        start_date: 计划创建开始日期 (格式: YYYY-MM-DD)
        end_date: 计划创建结束日期 (格式: YYYY-MM-DD)
        target_account_ids: 目标账户ID列表
        new_plan_prefix: 新计划名称前缀

    Returns:
        符合条件的计划列表
    """
    try:
        if not source_account_id:
            logger.error("必须提供源账户ID")
            return []
        
        # 获取配置
        config_manager = get_config_manager()
        app_settings = config_manager.get_config()
        api_credentials = config_manager.get_api_credentials()
        
        # 先获取源账户信息
        source_account_name = None
        principal_id = None
        principal_name = None
        
        with database_session() as db:
            source_account = db.query(AdAccount).filter(
                AdAccount.account_id_qc == source_account_id
            ).first()
            
            if not source_account:
                logger.error(f"未找到源账户: {source_account_id}")
                return []
            
            # 提取需要的信息
            source_account_name = source_account.name
            principal_id = source_account.principal.id
            principal_name = source_account.principal.name
        
        # 创建API客户端
        client = QianchuanClient(
            app_id=api_credentials['app_id'],
            secret=api_credentials['secret'],
            principal_id=principal_id
        )
        
        # 构建API查询过滤条件
        filtering = {}
        
        if source_status:
            filtering["status"] = source_status
        
        if source_aweme_id:
            try:
                filtering["aweme_id"] = int(source_aweme_id)
                logger.info(f"设置aweme_id筛选条件: {source_aweme_id}")
            except ValueError:
                logger.error(f"aweme_id必须是数字: {source_aweme_id}")
                return []

        # 添加时间筛选
        if start_date:
            filtering["ad_create_start_date"] = start_date
            logger.info(f"设置开始日期筛选: {start_date}")

        if end_date:
            filtering["ad_create_end_date"] = end_date
            logger.info(f"设置结束日期筛选: {end_date}")
        
        # 调用千川API获取计划列表
        logger.info(f"正在从千川API获取账户 {source_account_id} 的计划列表...")
        api_campaigns = client.get_ad_plan_list(
            advertiser_id=int(source_account_id),
            filtering=filtering
        )
        
        if not api_campaigns:
            logger.warning("未从API获取到任何计划")
            return []
        
        # 格式化结果
        result = []
        for campaign in api_campaigns:
            try:
                # 获取状态的中文显示
                status = campaign.get('status', '')
                status_display = get_status_display(status)
                
                # 获取抖音号信息
                aweme_info_list = campaign.get('aweme_info', [])
                aweme_id = ''
                if aweme_info_list and len(aweme_info_list) > 0:
                    aweme_id = aweme_info_list[0].get('aweme_id', '')
                
                result.append({
                    'campaign_id': str(campaign.get('ad_id', '')),
                    'name': campaign.get('name', ''),
                    'status': status,
                    'status_display': status_display,
                    'account_name': source_account_name,
                    'account_id': source_account_id,
                    'principal_name': principal_name,
                    'aweme_id': aweme_id,
                    'budget': campaign.get('delivery_setting', {}).get('budget', 0),
                    'bid': campaign.get('delivery_setting', {}).get('bid', 0),
                    'created_at': campaign.get('ad_create_time', ''),
                    'updated_at': campaign.get('ad_modify_time', '')
                })
            except Exception as e:
                logger.warning(f"处理计划数据时出错: {e}, 计划数据: {campaign}")
                continue
        
        logger.success(f"从API成功获取到 {len(result)} 个符合条件的计划")
        return result
        
    except Exception as e:
        logger.error(f"查找计划失败: {e}")
        return []

def list_accounts():
    """列出所有账户"""
    try:
        with database_session() as db:
            accounts = db.query(AdAccount).join(Principal).all()
            
            if not accounts:
                print("暂无账户")
                return
            
            print("\n🏢 当前账户列表:")
            print("-" * 80)
            print(f"{'账户ID':<20} {'名称':<30} {'主体':<20} {'状态':<10}")
            print("-" * 80)
            
            for account in accounts:
                print(f"{account.account_id_qc:<20} {account.name:<30} {account.principal.name:<20} {account.status:<10}")
            
    except Exception as e:
        logger.error(f"获取账户列表失败: {e}")

def list_campaigns():
    """列出所有计划（从千川API获取最新数据）"""
    try:
        config_manager = get_config_manager()
        api_credentials = config_manager.get_api_credentials()
        
        with database_session() as db:
            # 获取所有账户
            accounts = db.query(AdAccount).join(Principal).all()
            
            if not accounts:
                print("暂无账户")
                return
            
            print("\n📋 当前计划列表:")
            print("-" * 120)
            print(f"{'ID':<15} {'名称':<30} {'账户':<20} {'状态':<15} {'抖音号':<15}")
            print("-" * 120)
            
            total_campaigns = 0
            
            for account in accounts:
                try:
                    # 为每个账户创建API客户端
                    client = QianchuanClient(
                        app_id=api_credentials['app_id'],
                        secret=api_credentials['secret'],
                        principal_id=account.principal.id
                    )
                    
                    # 获取该账户的计划列表
                    campaigns = client.get_ad_plan_list(
                        advertiser_id=int(account.account_id_qc),
                        filtering={}
                    )
                    
                    if campaigns:
                        for campaign in campaigns:
                            aweme_info = campaign.get('aweme_info', [])
                            aweme_id = aweme_info[0].get('aweme_id', '') if aweme_info else ''
                            status_display = get_status_display(campaign.get('status', ''))
                            
                            print(f"{campaign.get('ad_id', ''):<15} {campaign.get('name', ''):<30} {account.name:<20} {status_display:<15} {aweme_id:<15}")
                            total_campaigns += 1
                    
                except Exception as e:
                    logger.warning(f"获取账户 {account.name} 的计划列表失败: {e}")
                    continue
            
            print("-" * 120)
            print(f"总计: {total_campaigns} 个计划")
            
    except Exception as e:
        logger.error(f"获取计划列表失败: {e}")

def validate_direct_creative_format(creative_list: List[Dict[str, Any]]) -> bool:
    """
    验证直投创意格式是否正确

    Args:
        creative_list: 直投创意列表

    Returns:
        bool: 格式是否正确
    """
    if not creative_list:
        logger.warning("直投创意列表为空")
        return False

    for i, creative in enumerate(creative_list):
        # 检查必要字段
        if not creative.get("image_mode"):
            logger.error(f"直投创意 {i} 缺少 image_mode 字段")
            return False

        # 直投创意应该使用 AWEME_LIVE_ROOM 模式
        if creative.get("image_mode") != "AWEME_LIVE_ROOM":
            logger.warning(f"直投创意 {i} 的 image_mode 为 {creative.get('image_mode')}，建议使用 AWEME_LIVE_ROOM")

    logger.info(f"直投创意格式验证通过，共 {len(creative_list)} 个创意")
    return True


def convert_detail_to_creation_config(detail: Dict[str, Any], args: argparse.Namespace) -> Dict[str, Any] | None:
    """[V39.3] 将从API获取的计划详情精细化地转换为创建新计划所需的配置字典"""
    try:
        # 1. 清理和复制 delivery_setting
        delivery_setting = detail.get("delivery_setting", {}).copy()
        delivery_setting.pop("revive_budget", None)
        delivery_setting.pop("qcpx_mode", None)
        delivery_setting.pop("allow_qcpx", None)
        # [V39.3] 修正 schedule_fixed_range 问题
        if delivery_setting.get("schedule_fixed_range", 0) < 1800:
            delivery_setting.pop("schedule_fixed_range", None)
        # 清理旧的投放时间，让新计划默认从现在开始
        delivery_setting.pop("start_time", None)
        delivery_setting.pop("end_time", None)

        # 2. 清理和复制 audience
        audience = detail.get("audience", {}).copy()
        audience.pop("inactive_retargeting_tags", None)
        audience.pop("auto_extend_enabled", None) # [V39.5] 移除智能放量参数，避免冲突

        # 3. 根据创意模式处理创意参数
        creative_material_mode = detail.get("creative_material_mode", "PROGRAMMATIC_CREATIVE")

        # 4. 构建基础配置 (白名单模式)
        config = {
            "advertiser_id": args.advertiser_id,
            "marketing_goal": detail["marketing_goal"],
            "campaign_scene": detail["campaign_scene"],
            "name": f"{detail['name']}{args.name_suffix}",
            "lab_ad_type": detail["lab_ad_type"], # [V39.4] 复制计划类型
            "delivery_setting": delivery_setting,
            "audience": audience,
            "creative_material_mode": creative_material_mode,
            "is_intelligent": detail.get("is_intelligent", 0),
            "keywords": detail.get("keywords", []),
            "pivative_words": detail.get("pivative_words", {}),
        }

        # 5. 根据创意模式添加对应的创意参数
        if creative_material_mode == "PROGRAMMATIC_CREATIVE":
            # 程序化创意：需要检查是否为直播间直推创意
            programmatic_creative_media_list = []
            for media in detail.get("programmatic_creative_media_list", []):
                clean_media = media.copy()
                clean_media.pop("video_url", None)
                clean_media.pop("video_poster_url", None)
                programmatic_creative_media_list.append(clean_media)

            programmatic_creative_title_list = detail.get("programmatic_creative_title_list", [])
            creative_list = detail.get("creative_list", [])

            # 检查是否为直播间直推创意（托管计划的特殊情况）
            is_live_room_creative = (
                len(programmatic_creative_media_list) == 0 and
                len(programmatic_creative_title_list) == 0 and
                len(creative_list) > 0 and
                any(creative.get("image_mode") == "AWEME_LIVE_ROOM" for creative in creative_list)
            )

            if is_live_room_creative:
                # 直播间直推创意：使用 creative_list
                clean_creative_list = []
                for creative in creative_list:
                    clean_creative = creative.copy()
                    # 清理不需要的字段
                    clean_creative.pop("creative_id", None)
                    clean_creative_list.append(clean_creative)

                config["creative_list"] = clean_creative_list
                logger.info(f"检测到程序化创意计划的直播间直推创意，创意数量: {len(clean_creative_list)}")
            else:
                # 标准程序化创意：使用 programmatic_creative_media_list 和 programmatic_creative_title_list
                config["programmatic_creative_media_list"] = programmatic_creative_media_list
                config["programmatic_creative_title_list"] = programmatic_creative_title_list
                logger.info(f"检测到标准程序化创意计划，素材数量: {len(programmatic_creative_media_list)}")

        elif creative_material_mode == "CUSTOM_CREATIVE":
            # 直投创意：使用 creative_list
            creative_list = []
            for creative in detail.get("creative_list", []):
                clean_creative = creative.copy()
                # 清理不需要的字段
                clean_creative.pop("creative_id", None)
                clean_creative.pop("status", None)
                clean_creative.pop("create_time", None)
                clean_creative.pop("modify_time", None)
                creative_list.append(clean_creative)

            # 验证直投创意格式
            if not validate_direct_creative_format(creative_list):
                logger.error(f"直投计划 {detail.get('ad_id')} 的创意格式验证失败")
                return None

            config["creative_list"] = creative_list
            logger.info(f"检测到直投创意计划，创意数量: {len(creative_list)}")

        else:
            logger.warning(f"未知的创意模式: {creative_material_mode}，使用默认程序化创意处理")
            # 默认按程序化创意处理
            config["programmatic_creative_media_list"] = []
            config["programmatic_creative_title_list"] = []

        # 6. [V39.4] 关键修复：根据计划类型，决定是否复制 campaign_id
        if config["lab_ad_type"] == "NOT_LAB_AD":
            if not detail.get("campaign_id"):
                logger.error(f"自定义计划 {detail.get('ad_id')} 的详情中缺少 campaign_id，无法复制。")
                return None
            config["campaign_id"] = detail["campaign_id"]

        # 7. 处理抖音号和直播间
        if detail.get("aweme_info"):
            target_aweme_id = getattr(args, 'target_aweme_id', None)

            if target_aweme_id == "keep_original":
                # 保持原计划的抖音号
                aweme_info = detail["aweme_info"]
                if aweme_info and len(aweme_info) > 0:
                    config["aweme_id"] = aweme_info[0].get("aweme_id")
                    logger.info(f"保持原计划的抖音号: {config['aweme_id']}")
            elif target_aweme_id:
                # 使用指定的抖音号
                config["aweme_id"] = target_aweme_id
                logger.info(f"使用指定的抖音号: {target_aweme_id}")
            else:
                # 从数据库获取该账户的最新抖音号
                try:
                    from qianchuan_aw.utils.db_utils import database_session
                    from qianchuan_aw.database.models import AdAccount

                    with database_session() as db:
                        account = db.query(AdAccount).filter(
                            AdAccount.account_id_qc == str(args.advertiser_id)
                        ).first()

                        if account and account.aweme_id:
                            config["aweme_id"] = account.aweme_id
                            logger.info(f"使用数据库中账户 {args.advertiser_id} 的最新抖音号: {account.aweme_id}")
                        else:
                            # 如果数据库中没有抖音号，保持原来的
                            aweme_info = detail["aweme_info"]
                            if aweme_info and len(aweme_info) > 0:
                                config["aweme_id"] = aweme_info[0].get("aweme_id")
                                logger.warning(f"数据库中账户 {args.advertiser_id} 没有抖音号，保持原计划的抖音号")
                except Exception as e:
                    logger.warning(f"获取数据库抖音号失败: {e}，保持原计划的抖音号")
                    aweme_info = detail["aweme_info"]
                    if aweme_info and len(aweme_info) > 0:
                        config["aweme_id"] = aweme_info[0].get("aweme_id")

            if config["marketing_goal"] == "LIVE_PROM_GOODS":
                config["product_info"] = []
                config["room_info"] = []

        # 8. 添加创意分类和标签支持
        # 重要：只有自定义计划（NOT_LAB_AD）才支持创意分类和标签，托管计划（LAB_AD）不支持这些字段
        if detail.get("lab_ad_type") == "NOT_LAB_AD":
            # 检查定向配置设置
            targeting_setting = getattr(args, 'targeting_setting', None)
            campaign_scene = detail.get("campaign_scene", "")

            # 判断是否为自定义类型计划：必须同时满足 NOT_LAB_AD 和 不是新客计划
            is_custom_plan = campaign_scene != "NEW_CUSTOMER_TRANSFORMATION"

            if targeting_setting and targeting_setting.get('option') == "随机定向（随机选择行业类目）" and is_custom_plan:
                # 随机定向：使用随机行业分类标签
                import random
                industry_choice = random.random()

                if industry_choice < 0.4:
                    # 40% 概率：无行业标签
                    logger.info(f"随机定向选择: 无行业标签 (概率: 40%)")
                elif industry_choice < 0.7:
                    # 30% 概率：行业标签组合1
                    config["first_industry_id"] = 1933
                    config["second_industry_id"] = 193309
                    config["third_industry_id"] = 19020402
                    logger.info(f"随机定向选择: 行业标签组合1 (1933/193309/19020402) (概率: 30%)")
                elif industry_choice < 0.9:
                    # 20% 概率：行业标签组合2
                    config["first_industry_id"] = 1933
                    config["second_industry_id"] = 193306
                    config["third_industry_id"] = 19330602
                    logger.info(f"随机定向选择: 行业标签组合2 (1933/193306/19330602) (概率: 20%)")
                else:
                    # 10% 概率：行业标签组合3
                    config["first_industry_id"] = 1933
                    config["second_industry_id"] = 193306
                    config["third_industry_id"] = 19330610
                    logger.info(f"随机定向选择: 行业标签组合3 (1933/193306/19330610) (概率: 10%)")
            else:
                # 完全复制原计划：保持原有的行业分类标签
                if detail.get("first_industry_id"):
                    config["first_industry_id"] = detail["first_industry_id"]
                    logger.info(f"完全复制: 添加一级行业分类: {detail['first_industry_id']}")

                if detail.get("second_industry_id"):
                    config["second_industry_id"] = detail["second_industry_id"]
                    logger.info(f"完全复制: 添加二级行业分类: {detail['second_industry_id']}")

                if detail.get("third_industry_id"):
                    config["third_industry_id"] = detail["third_industry_id"]
                    logger.info(f"完全复制: 添加三级行业分类: {detail['third_industry_id']}")

            # 创意标签字段 - 只对自定义计划添加（始终复制原有标签）
            ad_keywords = detail.get("ad_keywords", [])
            if ad_keywords and isinstance(ad_keywords, list) and len(ad_keywords) > 0:
                config["ad_keywords"] = ad_keywords.copy()
                logger.info(f"添加创意标签: {len(ad_keywords)} 个标签 - {ad_keywords[:5]}{'...' if len(ad_keywords) > 5 else ''}")
        else:
            logger.info(f"托管计划（{detail.get('lab_ad_type')}）不支持创意分类和标签，跳过这些字段")

        # 9. 应用命令行参数覆盖
        if getattr(args, 'target_budget', None):
            config["delivery_setting"]["budget"] = args.target_budget
        if getattr(args, 'target_cpa_bid', None):
            config["delivery_setting"]["cpa_bid"] = args.target_cpa_bid
        if getattr(args, 'target_roi_goal', None):
            config["delivery_setting"]["roi_goal"] = args.target_roi_goal

        # 10. [V41.0] 强制执行业务铁律
        config["is_homepage_hide"] = 1
        config["delivery_setting"]["smart_bid_type"] = "SMART_BID_CUSTOM"

        return config
    except KeyError as e:
        logger.error(f"从计划详情 {detail.get('ad_id')} 提取关键字段时出错: 缺少 {e}")
        return None

def execute_replication_task(args: argparse.Namespace):
    """
    [V40.0] 封装的核心复制任务逻辑 (已移除数据库依赖)
    :param args: 一个包含所有参数的命名空间对象 (可以是命令行解析结果，也可以是手动创建的)
    """
    try:
        # 加载配置
        config_manager = get_config_manager()
        app_settings = config_manager.get_config()
        api_credentials = config_manager.get_api_credentials()
        app_id = api_credentials['app_id']
        secret = api_credentials['secret']

        client = QianchuanClient(app_id=app_id, secret=secret, principal_id=args.principal_id)

        source_plans = find_source_plans(client, args)
        if not source_plans:
            logger.warning("未找到任何符合条件的源计划。")
            return

        logger.success(f"成功找到 {len(source_plans)} 个符合条件的源计划。")

        new_plan_configs = []
        for plan in source_plans:
            ad_id = plan.get('ad_id')
            logger.info(f"--- 正在处理源计划: {ad_id} ({plan.get('name')}) ---")

            detail = client.get_ad_plan_detail(advertiser_id=args.advertiser_id, ad_id=ad_id)
            if not detail:
                logger.error(f"获取计划 {ad_id} 详情失败，跳过此计划。")
                continue

            logger.debug(f"获取到计划 {ad_id} 的原始详情: \n{json.dumps(detail, indent=2, ensure_ascii=False)}")

            new_config = convert_detail_to_creation_config(detail, args)
            if new_config:
                new_plan_configs.append(new_config)

        if not new_plan_configs:
            logger.error("未能成功生成任何新计划的配置。")
            return

        logger.info("="*50)
        logger.info(f"准备创建 {len(new_plan_configs)} 个新计划。")

        if args.dry_run:
            logger.warning(">>> 当前为预演模式 (Dry Run)，不会实际创建计划 <<<")
            for i, config in enumerate(new_plan_configs):
                logger.info(f"  [计划 {i+1}] 名称: {config.get('name')}, 抖音号: {config.get('aweme_id')}, 预算: {config.get('delivery_setting', {}).get('budget')}")
            return

        if not args.yes:
            confirm = input("是否确认执行以上操作? (y/n): ")
            if confirm.lower() != 'y':
                logger.info("操作已取消。")
                return

        # [V39.7] 引入详细的成功/失败报告
        successful_replications = []
        failed_replications = []

        for i, config in enumerate(new_plan_configs):
            source_ad_id = source_plans[i].get('ad_id') # 通过索引获取对应的源ID
            try:
                logger.debug(f"准备使用以下配置创建新计划: \n{json.dumps(config, indent=2, ensure_ascii=False)}")
                result = client.create_ad_plan(config)
                if result and result.get("ad_id"):
                    successful_replications.append({
                        "source_id": source_ad_id,
                        "new_id": result.get("ad_id"),
                        "new_name": config.get("name")
                    })
                    logger.info(f"计划 '{config.get('name')}' 创建成功 (新ID: {result.get('ad_id')})，等待 {app_settings['robustness']['plan_creation_interval']} 秒...")
                    time.sleep(app_settings['robustness']['plan_creation_interval'])
            except QianchuanAPIException as e:
                failed_replications.append({
                    "source_id": source_ad_id,
                    "name": config.get("name"),
                    "reason": str(e)
                })
                logger.error(f"创建计划 '{config.get('name')}' 失败: {e}")

        # 打印最终总结报告
        logger.info("="*50)
        logger.info("计划复制任务最终总结报告")
        logger.info("="*50)

        if successful_replications:
            logger.success(f"✅ 成功复制 {len(successful_replications)} 个计划:")
            for item in successful_replications:
                logger.success(f"  - 源ID: {item['source_id']} -> 新ID: {item['new_id']} ({item['new_name']})")

        if failed_replications:
            logger.error(f"❌ 复制失败 {len(failed_replications)} 个计划:")
            for item in failed_replications:
                logger.error(f"  - 源ID: {item['source_id']} ({item['name']})，原因: {item['reason']}")

        logger.info("="*50)

    except QianchuanAPIException as e:
        logger.error(f"发生API错误: {e}")
    except Exception as e:
        logger.error(f"发生未知错误: {e}", exc_info=True)

def replicate_single_campaign(campaign_id, source_account_id, target_account_id, new_name, aweme_setting=None, bid_setting=None, targeting_setting=None):
    """
    复制单个计划到目标账户（使用历史版本的正确逻辑）

    Args:
        campaign_id: 源计划ID
        source_account_id: 源账户ID
        target_account_id: 目标账户ID
        new_name: 新计划名称
        aweme_setting: 抖音号设置 {'option': str, 'target_aweme_id': str}
        bid_setting: 出价设置 {'option': str, 'cpa_range': [min, max], 'roi_range': [min, max]}
        targeting_setting: 定向配置设置 {'option': str}
    """
    try:
        # 获取配置
        config_manager = get_config_manager()
        api_credentials = config_manager.get_api_credentials()

        # 获取源账户和目标账户的信息
        with database_session() as db:
            source_account = db.query(AdAccount).filter(
                AdAccount.account_id_qc == source_account_id
            ).first()

            target_account = db.query(AdAccount).filter(
                AdAccount.account_id_qc == target_account_id
            ).first()

            if not source_account:
                logger.error(f"未找到源账户 {source_account_id}")
                return False

            if not target_account:
                logger.error(f"未找到目标账户 {target_account_id}")
                return False

            # 使用源账户的主体来获取计划详情
            source_principal_id = source_account.principal.id
            target_principal_id = target_account.principal.id

        # 创建API客户端（使用源账户的主体）
        source_client = QianchuanClient(
            app_id=api_credentials['app_id'],
            secret=api_credentials['secret'],
            principal_id=source_principal_id
        )

        # 获取计划详情（从源账户获取）
        logger.info(f"获取计划 {campaign_id} 的详细配置...")
        detail = source_client.get_ad_plan_detail(
            advertiser_id=int(source_account_id),
            ad_id=int(campaign_id)
        )

        if not detail:
            logger.error(f"无法获取计划 {campaign_id} 的详情")
            return False

        # 构建复制参数（使用历史版本的逻辑）
        class Args:
            def __init__(self):
                self.advertiser_id = int(target_account_id)
                self.principal_id = target_principal_id
                self.name_suffix = ""  # 我们已经在new_name中处理了前缀

                # 根据抖音号设置决定target_aweme_id
                logger.info(f"🔍 抖音号设置参数: {aweme_setting}")
                if aweme_setting and aweme_setting.get('option') == "指定统一的抖音号":
                    self.target_aweme_id = aweme_setting.get('target_aweme_id')
                    logger.info(f"📱 使用指定的抖音号: {self.target_aweme_id}")
                elif aweme_setting and aweme_setting.get('option') == "保持原计划的抖音号":
                    self.target_aweme_id = "keep_original"  # 特殊标记
                    logger.info(f"📱 保持原计划的抖音号")
                else:
                    self.target_aweme_id = None  # 让函数自动从数据库获取
                    logger.info(f"📱 使用数据库中的抖音号")

                self.target_budget = None
                self.target_cpa_bid = None
                self.target_roi_goal = None
                self.dry_run = False
                self.yes = True

                # 定向配置设置
                self.targeting_setting = targeting_setting

        args = Args()

        # 使用历史版本的配置转换逻辑
        config = convert_detail_to_creation_config(detail, args)
        if not config:
            logger.error(f"无法构建计划 {campaign_id} 的新配置")
            return False

        # 设置新名称
        config["name"] = new_name

        # 处理出价设置覆盖
        if bid_setting and bid_setting.get('option') != "保持原计划的出价":
            delivery_setting = config.get("delivery_setting", {})

            if bid_setting.get('option') == "使用配置文件默认范围":
                # 使用配置文件默认范围
                try:
                    from qianchuan_aw.utils.config_manager import load_settings
                    from qianchuan_aw.utils.workflow_helpers import get_random_value_from_range

                    app_settings = load_settings()
                    manual_config = app_settings['plan_creation_defaults'].get('manual_workflow', {})

                    # 根据当前计划的出价类型设置新出价
                    if delivery_setting.get("cpa_bid") is not None:  # DEAL策略
                        default_cpa_range = manual_config.get('default_cpa_bid_range', [35.1, 35.7])
                        new_cpa_bid = get_random_value_from_range(default_cpa_range, precision=2)
                        if new_cpa_bid:
                            delivery_setting["cpa_bid"] = new_cpa_bid
                            logger.info(f"使用配置文件默认CPA范围生成出价: {new_cpa_bid}")

                    if delivery_setting.get("roi_goal") is not None:  # ROI策略
                        default_roi_range = manual_config.get('default_roi_goal_range', [1.95, 2.01])
                        new_roi_goal = get_random_value_from_range(default_roi_range, precision=2)
                        if new_roi_goal:
                            delivery_setting["roi_goal"] = new_roi_goal
                            logger.info(f"使用配置文件默认ROI范围生成目标: {new_roi_goal}")

                except Exception as e:
                    logger.warning(f"使用配置文件默认范围失败: {e}")

            elif bid_setting.get('option') == "自定义出价范围":
                # 使用自定义范围
                try:
                    from qianchuan_aw.utils.workflow_helpers import get_random_value_from_range

                    # 根据当前计划的出价类型设置新出价
                    if delivery_setting.get("cpa_bid") is not None and bid_setting.get('cpa_range'):  # DEAL策略
                        new_cpa_bid = get_random_value_from_range(bid_setting['cpa_range'], precision=2)
                        if new_cpa_bid:
                            delivery_setting["cpa_bid"] = new_cpa_bid
                            logger.info(f"使用自定义CPA范围生成出价: {new_cpa_bid}")

                    if delivery_setting.get("roi_goal") is not None and bid_setting.get('roi_range'):  # ROI策略
                        new_roi_goal = get_random_value_from_range(bid_setting['roi_range'], precision=2)
                        if new_roi_goal:
                            delivery_setting["roi_goal"] = new_roi_goal
                            logger.info(f"使用自定义ROI范围生成目标: {new_roi_goal}")

                except Exception as e:
                    logger.warning(f"使用自定义出价范围失败: {e}")

            config["delivery_setting"] = delivery_setting

        # 调试：输出最终配置中的关键信息
        logger.info(f"🔍 最终计划配置检查:")
        logger.info(f"   计划名称: {config.get('name')}")
        logger.info(f"   抖音号ID: {config.get('aweme_id')}")
        logger.info(f"   CPA出价: {config.get('delivery_setting', {}).get('cpa_bid')}")
        logger.info(f"   ROI目标: {config.get('delivery_setting', {}).get('roi_goal')}")

        # 创建目标账户的客户端（用于创建新计划）
        target_client = QianchuanClient(
            app_id=api_credentials['app_id'],
            secret=api_credentials['secret'],
            principal_id=target_principal_id
        )

        # 创建新计划
        logger.info(f"在账户 {target_account_id} 中创建新计划: {new_name}")
        result = target_client.create_ad_plan(config)

        if result and result.get("ad_id"):
            logger.success(f"计划创建成功，新ID: {result.get('ad_id')}")
            return True
        else:
            logger.error(f"计划创建失败: {result}")
            return False

    except Exception as e:
        logger.error(f"复制计划 {campaign_id} 时发生错误: {e}")
        return False

def preview_name_cleaning(selected_campaigns, new_plan_prefix="", clean_prefixes=True,
                         preserve_standard_format=True):
    """
    预览名称清理结果

    Args:
        selected_campaigns: 选中的计划列表
        new_plan_prefix: 新计划名称前缀
        clean_prefixes: 是否清理历史前缀
        preserve_standard_format: 是否保留标准格式

    Returns:
        预览结果字典
    """
    try:
        name_cleaner = PlanNameCleaner()

        # 提取计划名称
        campaign_names = [campaign['name'] for campaign in selected_campaigns]

        if clean_prefixes:
            # 使用智能清理预览
            raw_preview = name_cleaner.preview_cleaning(
                campaign_names,
                new_plan_prefix,
                preserve_standard_format
            )

            # 将NameCleaningResult对象转换为字典格式
            results = []
            for result in raw_preview['results']:
                results.append({
                    'original_name': result.original_name,
                    'cleaned_name': result.cleaned_name,
                    'detected_prefixes': result.detected_prefixes,
                    'is_standard_format': result.is_standard_format,
                    'final_name': result.final_name,
                    'has_conflict': result.has_conflict
                })

            preview_result = {
                'results': results,
                'statistics': raw_preview['statistics']
            }
        else:
            # 简单前缀预览
            results = []
            for name in campaign_names:
                if new_plan_prefix:
                    final_name = f"{new_plan_prefix}_{name}"
                else:
                    final_name = name

                results.append({
                    'original_name': name,
                    'cleaned_name': name,
                    'detected_prefixes': [],
                    'is_standard_format': False,
                    'final_name': final_name,
                    'has_conflict': False
                })

            preview_result = {
                'results': results,
                'statistics': {
                    'total_count': len(results),
                    'prefix_detected_count': 0,
                    'standard_format_count': 0,
                    'conflict_count': 0,
                    'prefix_stats': {}
                }
            }

        return {
            'success': True,
            'preview': preview_result
        }

    except Exception as e:
        logger.error(f"预览名称清理失败: {e}")
        return {
            'success': False,
            'message': f"预览失败: {str(e)}"
        }

# 旧的跨账户复制函数已删除，现在使用历史版本的 convert_detail_to_creation_config

def run_replication(target_account_ids=None, new_plan_prefix=None, selected_campaigns=None,
                   clean_prefixes=True, preserve_standard_format=True, **kwargs):
    """
    可被外部调用的高级计划复制核心逻辑。
    专门为Web UI设计的简化接口。

    Args:
        target_account_ids: 目标账户ID列表
        new_plan_prefix: 新计划名称前缀
        selected_campaigns: 选中的计划列表
        clean_prefixes: 是否清理历史前缀
        preserve_standard_format: 是否保留标准格式
    """
    try:
        if not target_account_ids:
            return {"success": False, "message": "必须指定目标账户"}

        if not selected_campaigns:
            return {"success": False, "message": "必须选择要复制的计划"}

        logger.info("=== 开始执行高级计划复制 ===")
        logger.info(f"准备复制 {len(selected_campaigns)} 个计划到 {len(target_account_ids)} 个目标账户")

        # 初始化名称清理器
        name_cleaner = PlanNameCleaner()

        success_count = 0
        total_count = len(selected_campaigns) * len(target_account_ids)

        # 获取配置
        config_manager = get_config_manager()
        api_credentials = config_manager.get_api_credentials()
        app_id = api_credentials['app_id']
        secret = api_credentials['secret']

        # 为每个目标账户获取现有计划名称（用于重名检查）
        existing_names_by_account = {}

        # 需要获取一个principal_id来初始化客户端，使用第一个选中计划的账户对应的主体
        with database_session() as db:
            # 从第一个选中的计划获取账户信息
            first_campaign = selected_campaigns[0]
            account = db.query(AdAccount).filter_by(account_id_qc=first_campaign['account_id']).first()
            if not account:
                return {"success": False, "message": f"未找到账户ID {first_campaign['account_id']}"}

            principal_id = account.principal_id

        client = QianchuanClient(app_id=app_id, secret=secret, principal_id=principal_id)

        for target_account_id in target_account_ids:
            try:
                # 获取目标账户现有计划
                existing_campaigns = client.get_ad_plan_list(advertiser_id=int(target_account_id))
                existing_names = {campaign.get('name', '') for campaign in existing_campaigns or []}
                existing_names_by_account[target_account_id] = existing_names
                logger.info(f"账户 {target_account_id} 现有 {len(existing_names)} 个计划")
            except Exception as e:
                logger.warning(f"获取账户 {target_account_id} 现有计划失败: {e}")
                existing_names_by_account[target_account_id] = set()

        # 执行复制
        for campaign in selected_campaigns:
            for target_account_id in target_account_ids:
                try:
                    # 智能生成新计划名称
                    if clean_prefixes:
                        # 使用智能清理
                        cleaning_result = name_cleaner.clean_name(
                            campaign['name'],
                            preserve_standard_format=preserve_standard_format
                        )
                        base_name = cleaning_result.cleaned_name

                        # 记录清理信息
                        if cleaning_result.detected_prefixes:
                            logger.info(f"检测到前缀: {cleaning_result.detected_prefixes}")
                            logger.info(f"清理后名称: {base_name}")
                    else:
                        base_name = campaign['name']

                    # 生成唯一名称
                    existing_names = existing_names_by_account.get(target_account_id, set())
                    new_name = name_cleaner.generate_unique_name(
                        base_name,
                        existing_names,
                        new_plan_prefix or ""
                    )

                    # 更新已使用名称集合
                    existing_names.add(new_name)

                    logger.info(f"复制计划: {campaign['name']} -> {new_name} (账户 {target_account_id})")

                    # 调用实际的复制逻辑
                    success = replicate_single_campaign(
                        campaign_id=campaign['campaign_id'],
                        source_account_id=campaign['account_id'],
                        target_account_id=target_account_id,
                        new_name=new_name,
                        aweme_setting=kwargs.get('aweme_setting'),
                        bid_setting=kwargs.get('bid_setting'),
                        targeting_setting=kwargs.get('targeting_setting')
                    )

                    if success:
                        success_count += 1
                        logger.success(f"✅ 复制成功: {new_name}")
                    else:
                        logger.error(f"❌ 复制失败: {new_name}")

                except Exception as e:
                    logger.error(f"❌ 复制失败: {campaign['name']} -> {target_account_id}, 错误: {e}")

        logger.info("=== 计划复制任务完成 ===")

        if success_count > 0:
            return {
                "success": True,
                "message": f"复制完成: {success_count}/{total_count} 成功"
            }
        else:
            return {
                "success": False,
                "message": "所有复制操作都失败了"
            }

    except Exception as e:
        logger.error(f"复制过程中发生错误: {e}")
        return {"success": False, "message": f"复制失败: {str(e)}"}

def main():
    """交互式主函数"""
    print("🔄 千川广告计划复制工具")
    print("=" * 50)

    while True:
        print("\n请选择操作:")
        print("1. 复制计划")
        print("2. 查看计划列表")
        print("3. 查看账户列表")
        print("4. 高级批量复制")
        print("5. 退出")

        choice = input("\n请输入选择 (1-5): ").strip()

        if choice == "1":
            list_campaigns()
            source_id = input("\n请输入源计划ID: ").strip()
            if not source_id:
                print("❌ 计划ID不能为空")
                continue

            list_accounts()
            target_account = input("\n请输入目标账户ID: ").strip()
            if not target_account:
                print("❌ 账户ID不能为空")
                continue

            new_name = input("请输入新计划名称 (可选，直接回车使用默认): ").strip()
            new_name = new_name if new_name else None

            # 这里需要实现单个计划复制逻辑
            print("✅ 计划复制功能开发中...")

        elif choice == "2":
            list_campaigns()

        elif choice == "3":
            list_accounts()

        elif choice == "4":
            print("\n🔄 高级批量复制")
            print("-" * 30)

            # 选择源账户
            list_accounts()
            source_account_input = input("\n请输入源账户ID: ").strip()
            if not source_account_input:
                print("❌ 源账户ID不能为空")
                continue

            # 获取筛选条件
            print("\n可用的计划状态:")
            print("DELIVERY_OK - 投放中")
            print("DISABLE - 已暂停")
            print("LIVE_ROOM_OFF - 关联直播间未开播")
            print("CAMPAIGN_DISABLE - 已被广告组暂停")
            print("BUDGET_EXCEED - 预算超限")
            print("AUDIT_REJECT - 审核拒绝")

            source_status = input("\n源计划状态筛选 (可选): ").strip() or None
            source_aweme_id = input("源计划抖音号筛选 (可选): ").strip() or None

            if not any([source_status, source_aweme_id]):
                print("❌ 必须提供至少一个源计划筛选条件")
                continue

            # 查找符合条件的计划
            campaigns = find_campaigns_by_criteria(
                source_account_id=source_account_input,
                source_status=source_status,
                source_aweme_id=source_aweme_id
            )

            if not campaigns:
                print("❌ 没有找到符合条件的计划")
                continue

            print(f"\n找到 {len(campaigns)} 个符合条件的计划:")
            for i, campaign in enumerate(campaigns, 1):
                status_display = campaign.get('status_display', campaign['status'])
                print(f"{i}. {campaign['name']} (ID: {campaign['campaign_id']}) - {status_display} - {campaign['account_name']}")

            # 获取目标账户
            print("\n" + "="*50)
            list_accounts()
            target_accounts_input = input("\n请输入目标账户ID (多个用逗号分隔): ").strip()
            if not target_accounts_input:
                print("❌ 目标账户不能为空")
                continue

            target_account_ids = [acc.strip() for acc in target_accounts_input.split(',')]

            # 获取新计划前缀
            new_plan_prefix = input("新计划名称前缀 (可选): ").strip() or None

            # 执行复制
            result = run_replication(
                target_account_ids=target_account_ids,
                new_plan_prefix=new_plan_prefix,
                selected_campaigns=campaigns
            )

            print("✅ 批量复制功能开发中...")

        elif choice == "5":
            print("👋 再见!")
            break

        else:
            print("❌ 无效选择，请重新输入")

def main_cli():
    """主函数，用于命令行调用"""
    args = parse_arguments()
    execute_replication_task(args)


if __name__ == "__main__":
    main()
