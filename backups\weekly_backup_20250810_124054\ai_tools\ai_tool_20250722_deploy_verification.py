#!/usr/bin/env python3
"""
增强版视频验证器部署验证脚本
确保新验证器正常工作并监控效果
"""

import sys
import os
import time
from datetime import datetime, timedelta

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.enhanced_video_validator import create_enhanced_video_validator
from src.qianchuan_aw.utils.config_loader import load_settings
from src.qianchuan_aw.utils.db_utils import database_session
from src.qianchuan_aw.utils.logger import logger
from sqlalchemy import text

def verify_deployment():
    """验证增强版验证器部署"""
    logger.info("🔍 验证增强版视频验证器部署")
    logger.info("=" * 60)
    
    try:
        # 1. 测试验证器创建
        app_settings = load_settings()
        validator = create_enhanced_video_validator(app_settings)
        logger.info("✅ 增强版验证器创建成功")
        
        # 2. 测试白名单加载
        stats = validator.get_validation_stats()
        logger.info(f"✅ 验证器统计初始化成功: {stats}")
        
        # 3. 测试验证功能
        test_files = []
        
        # 查找测试文件
        test_dirs = [
            "G:/workflow_assets/01_materials_to_process/缇萃百货",
            "quarantine/invalid_videos"
        ]
        
        for test_dir in test_dirs:
            if os.path.exists(test_dir):
                for file in os.listdir(test_dir):
                    if file.endswith('.mp4'):
                        test_files.append(os.path.join(test_dir, file))
                        if len(test_files) >= 3:  # 只测试3个文件
                            break
                if test_files:
                    break
        
        if test_files:
            logger.info(f"📋 测试验证功能 ({len(test_files)}个文件):")
            
            for test_file in test_files:
                result = validator.validate_video(test_file)
                filename = os.path.basename(test_file)
                
                status = "✅ 有效" if result.is_valid else "❌ 无效"
                duration_str = f"{result.duration:.1f}s" if result.duration else "未检测"
                
                logger.info(f"  {filename}: {status}")
                logger.info(f"    原因: {result.reason}")
                logger.info(f"    时长: {duration_str}")
                logger.info(f"    置信度: {result.confidence_score:.2f}")
        else:
            logger.warning("未找到测试文件，跳过验证功能测试")
        
        # 4. 验证配置文件
        whitelist_file = "config/video_whitelist.json"
        if os.path.exists(whitelist_file):
            logger.info("✅ 白名单配置文件存在")
        else:
            logger.warning("⚠️ 白名单配置文件不存在")
        
        logger.info("✅ 增强版验证器部署验证完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 部署验证失败: {e}")
        return False

def monitor_validation_effectiveness():
    """监控验证器效果"""
    logger.info(f"\n📊 监控验证器效果")
    logger.info("=" * 60)
    
    with database_session() as db:
        # 监控最近的上传活动
        recent_activity_query = text("""
            SELECT 
                COUNT(*) as total_uploads,
                COUNT(CASE WHEN lc.status = 'upload_failed' THEN 1 END) as failed_uploads,
                COUNT(CASE WHEN lc.status IN ('approved', 'rejected') THEN 1 END) as completed_uploads
            FROM local_creatives lc
            JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
            WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                AND lc.created_at >= NOW() - INTERVAL '1 hour'
        """)
        
        result = db.execute(recent_activity_query).fetchone()
        
        if result.total_uploads > 0:
            failure_rate = (result.failed_uploads / result.total_uploads) * 100
            completion_rate = (result.completed_uploads / result.total_uploads) * 100
            
            logger.info(f"📈 最近1小时活动统计:")
            logger.info(f"  总上传: {result.total_uploads}")
            logger.info(f"  失败: {result.failed_uploads}")
            logger.info(f"  完成: {result.completed_uploads}")
            logger.info(f"  失败率: {failure_rate:.1f}%")
            logger.info(f"  完成率: {completion_rate:.1f}%")
            
            # 评估效果
            if failure_rate < 1:
                logger.info("✅ 验证器效果优秀: 失败率<1%")
            elif failure_rate < 5:
                logger.info("🟡 验证器效果良好: 失败率<5%")
            else:
                logger.warning(f"🔴 验证器效果需要改进: 失败率{failure_rate:.1f}%")
        else:
            logger.info("📊 最近1小时无上传活动")

def create_monitoring_dashboard():
    """创建监控仪表板脚本"""
    dashboard_script = '''#!/usr/bin/env python3
"""
增强版视频验证器监控仪表板
实时显示验证器效果和系统状态
"""

import sys
import os
import time
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.db_utils import database_session
from src.qianchuan_aw.utils.logger import logger
from sqlalchemy import text

def display_dashboard():
    """显示监控仪表板"""
    while True:
        os.system('cls' if os.name == 'nt' else 'clear')
        
        print("🎯 千川增强版视频验证器监控仪表板")
        print("=" * 60)
        print(f"更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        try:
            with database_session() as db:
                # 最近24小时统计
                stats_query = text("""
                    SELECT 
                        COUNT(*) as total,
                        COUNT(CASE WHEN lc.status = 'upload_failed' THEN 1 END) as failed,
                        COUNT(CASE WHEN lc.status IN ('approved', 'rejected') THEN 1 END) as completed,
                        COUNT(CASE WHEN lc.created_at >= NOW() - INTERVAL '1 hour' THEN 1 END) as recent_hour
                    FROM local_creatives lc
                    JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
                    WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                        AND lc.created_at >= NOW() - INTERVAL '24 hours'
                """)
                
                result = db.execute(stats_query).fetchone()
                
                if result.total > 0:
                    failure_rate = (result.failed / result.total) * 100
                    completion_rate = (result.completed / result.total) * 100
                    
                    print("📊 24小时统计:")
                    print(f"  总处理: {result.total}")
                    print(f"  失败: {result.failed} ({failure_rate:.1f}%)")
                    print(f"  完成: {result.completed} ({completion_rate:.1f}%)")
                    print(f"  最近1小时: {result.recent_hour}")
                    print()
                    
                    # 状态指示器
                    if failure_rate < 1:
                        status = "🟢 优秀"
                    elif failure_rate < 5:
                        status = "🟡 良好"
                    else:
                        status = "🔴 需要改进"
                    
                    print(f"系统状态: {status}")
                    print()
                
                # 隔离统计
                quarantine_dir = "quarantine/invalid_videos"
                if os.path.exists(quarantine_dir):
                    quarantine_count = len([f for f in os.listdir(quarantine_dir) if f.endswith('.mp4')])
                    print(f"📁 隔离文件: {quarantine_count}个")
                
        except Exception as e:
            print(f"❌ 数据获取失败: {e}")
        
        print()
        print("按 Ctrl+C 退出监控")
        
        try:
            time.sleep(30)  # 30秒更新一次
        except KeyboardInterrupt:
            print("\\n监控已停止")
            break

if __name__ == "__main__":
    display_dashboard()
'''
    
    dashboard_path = "ai_tools/ai_tool_20250722_validation_dashboard.py"
    with open(dashboard_path, 'w', encoding='utf-8') as f:
        f.write(dashboard_script)
    
    logger.info(f"✅ 创建监控仪表板: {dashboard_path}")
    return dashboard_path

def generate_deployment_report():
    """生成部署报告"""
    logger.info(f"\n📋 增强版验证器部署报告")
    logger.info("=" * 60)
    
    logger.info("🎯 部署目标:")
    logger.info("  - 误隔离率从92.6%降至<1%")
    logger.info("  - 正常视频识别准确率>99%")
    logger.info("  - 系统稳定运行7x24小时")
    
    logger.info(f"\n✅ 已完成的部署步骤:")
    logger.info("  1. 创建增强版视频验证器")
    logger.info("  2. 实施白名单机制")
    logger.info("  3. 多重验证方法集成")
    logger.info("  4. 置信度评估系统")
    logger.info("  5. 二次确认隔离机制")
    logger.info("  6. 更新工作流调用")
    
    logger.info(f"\n📊 预期改善效果:")
    logger.info("  - 误隔离率: 92.6% → <1%")
    logger.info("  - 验证准确率: 提升至99%+")
    logger.info("  - 系统稳定性: 显著提升")
    logger.info("  - 人工干预: 大幅减少")
    
    logger.info(f"\n🔍 监控要点:")
    logger.info("  - 每小时检查上传失败率")
    logger.info("  - 监控隔离文件数量变化")
    logger.info("  - 观察白名单机制效果")
    logger.info("  - 跟踪验证器统计数据")
    
    logger.info(f"\n📞 问题处理:")
    logger.info("  - 如发现误隔离，检查白名单配置")
    logger.info("  - 如验证失败率上升，检查验证逻辑")
    logger.info("  - 如系统异常，可回滚到备份版本")

def main():
    """主函数"""
    try:
        # 1. 验证部署
        if not verify_deployment():
            logger.error("部署验证失败，请检查配置")
            return False
        
        # 2. 监控效果
        monitor_validation_effectiveness()
        
        # 3. 创建监控工具
        dashboard_path = create_monitoring_dashboard()
        
        # 4. 生成部署报告
        generate_deployment_report()
        
        logger.info(f"\n🎉 增强版视频验证器部署完成!")
        logger.info(f"建议运行监控仪表板: python {dashboard_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 部署验证过程中出现错误: {e}")
        return False

if __name__ == "__main__":
    main()
