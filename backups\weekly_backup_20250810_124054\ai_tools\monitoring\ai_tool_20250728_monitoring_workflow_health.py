#!/usr/bin/env python3
"""工作流状态监控脚本"""

import sys
from pathlib import Path

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

from src.qianchuan_aw.database.connection import database_session
from src.qianchuan_aw.database.models import LocalCreative, PlatformCreative, Campaign

def check_workflow_health():
    """检查工作流健康状态"""
    with database_session() as db:
        # 检查各状态素材数量
        status_counts = {}
        for status in ['new', 'pending_upload', 'processing', 'approved', 'rejected', 'upload_failed']:
            count = db.query(LocalCreative).filter(LocalCreative.status == status).count()
            status_counts[status] = count
        
        print("📊 工作流状态监控报告")
        print("=" * 50)
        for status, count in status_counts.items():
            alert = "🚨" if (status == 'pending_upload' and count > 50) else "✅"
            print(f"{alert} {status}: {count}")
        
        # 检查平台素材状态
        platform_counts = {}
        for status in ['AUDITING', 'pending']:
            count = db.query(PlatformCreative).filter(PlatformCreative.review_status == status).count()
            platform_counts[status] = count
        
        print("\n📋 平台素材状态")
        print("-" * 30)
        for status, count in platform_counts.items():
            print(f"   {status}: {count}")

if __name__ == "__main__":
    check_workflow_health()
