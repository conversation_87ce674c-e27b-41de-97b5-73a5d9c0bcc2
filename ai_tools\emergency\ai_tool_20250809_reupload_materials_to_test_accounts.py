#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 重新上传素材到正确的TEST账户
清理条件: 成为素材重新上传工具，长期保留
"""

import os
import sys

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger


class MaterialReuploader:
    """素材重新上传器"""
    
    def __init__(self):
        self.reupload_results = {}
    
    def run_reupload_process(self):
        """运行重新上传流程"""
        logger.info("🔄 素材重新上传到TEST账户")
        logger.info("="*100)
        
        # 1. 检查需要重新上传的素材
        materials_to_reupload = self._check_materials_to_reupload()
        
        # 2. 重置素材状态
        reset_success = self._reset_material_status(materials_to_reupload)
        
        # 3. 删除错误的PlatformCreative记录
        cleanup_success = self._cleanup_platform_creatives(materials_to_reupload)
        
        # 4. 验证重置结果
        verification_success = self._verify_reset_results()
        
        # 5. 生成报告
        self._generate_reupload_report({
            'materials_to_reupload': materials_to_reupload,
            'reset_success': reset_success,
            'cleanup_success': cleanup_success,
            'verification_success': verification_success
        })
    
    def _check_materials_to_reupload(self):
        """检查需要重新上传的素材"""
        logger.info("📊 检查需要重新上传的素材...")
        
        try:
            from qianchuan_aw.database.database import SessionLocal
            from qianchuan_aw.database.models import LocalCreative, Principal
            
            with SessionLocal() as db:
                # 查找最近24小时内的uploaded_pending_plan素材
                materials = db.query(LocalCreative).join(Principal).filter(
                    Principal.name == '缇萃百货',
                    LocalCreative.status == 'uploaded_pending_plan',
                    LocalCreative.created_at >= db.func.now() - db.text("INTERVAL '24 hours'"),
                    LocalCreative.video_id.isnot(None)  # 有video_id但需要重新上传
                ).all()
                
                logger.info(f"📋 发现 {len(materials)} 个需要重新上传的素材")
                
                if materials:
                    # 显示前5个素材的详情
                    logger.info("📋 前5个需要重新上传的素材:")
                    for i, material in enumerate(materials[:5]):
                        logger.info(f"   {i+1}. 素材ID: {material.id}")
                        logger.info(f"      文件: {os.path.basename(material.file_path)}")
                        logger.info(f"      当前video_id: {material.video_id}")
                        logger.info(f"      上传账户ID: {material.uploaded_to_account_id}")
                
                return materials
                
        except Exception as e:
            logger.error(f"❌ 检查需要重新上传的素材失败: {e}")
            return []
    
    def _reset_material_status(self, materials):
        """重置素材状态"""
        logger.info("🔄 重置素材状态为pending_upload...")
        
        if not materials:
            logger.info("ℹ️ 没有需要重置的素材")
            return True
        
        try:
            from qianchuan_aw.database.database import SessionLocal
            
            with SessionLocal() as db:
                reset_count = 0
                
                for material in materials:
                    try:
                        # 重置状态和相关字段
                        material.status = 'pending_upload'
                        material.video_id = None
                        material.material_id_qc = None
                        
                        reset_count += 1
                        
                        if reset_count <= 10:  # 只显示前10个
                            logger.info(f"  ✅ 重置素材 {material.id}: {os.path.basename(material.file_path)}")
                        
                    except Exception as e:
                        logger.error(f"❌ 重置素材 {material.id} 失败: {e}")
                
                # 提交所有更改
                db.commit()
                
                logger.success(f"✅ 成功重置 {reset_count} 个素材状态")
                
                return reset_count > 0
                
        except Exception as e:
            logger.error(f"❌ 重置素材状态失败: {e}")
            return False
    
    def _cleanup_platform_creatives(self, materials):
        """清理错误的PlatformCreative记录"""
        logger.info("🧹 清理错误的PlatformCreative记录...")
        
        if not materials:
            logger.info("ℹ️ 没有需要清理的记录")
            return True
        
        try:
            from qianchuan_aw.database.database import SessionLocal
            from qianchuan_aw.database.models import PlatformCreative
            
            with SessionLocal() as db:
                cleanup_count = 0
                
                for material in materials:
                    try:
                        # 删除对应的PlatformCreative记录
                        platform_creatives = db.query(PlatformCreative).filter(
                            PlatformCreative.local_creative_id == material.id
                        ).all()
                        
                        for pc in platform_creatives:
                            db.delete(pc)
                            cleanup_count += 1
                        
                        if cleanup_count <= 10:  # 只显示前10个
                            logger.info(f"  ✅ 清理素材 {material.id} 的PlatformCreative记录")
                        
                    except Exception as e:
                        logger.error(f"❌ 清理素材 {material.id} 的记录失败: {e}")
                
                # 提交所有更改
                db.commit()
                
                logger.success(f"✅ 成功清理 {cleanup_count} 个PlatformCreative记录")
                
                return cleanup_count > 0
                
        except Exception as e:
            logger.error(f"❌ 清理PlatformCreative记录失败: {e}")
            return False
    
    def _verify_reset_results(self):
        """验证重置结果"""
        logger.info("🔍 验证重置结果...")
        
        try:
            from qianchuan_aw.database.database import SessionLocal
            from qianchuan_aw.database.models import LocalCreative, Principal
            
            with SessionLocal() as db:
                # 检查pending_upload状态的素材数量
                pending_upload_count = db.query(LocalCreative).join(Principal).filter(
                    Principal.name == '缇萃百货',
                    LocalCreative.status == 'pending_upload',
                    LocalCreative.created_at >= db.func.now() - db.text("INTERVAL '24 hours'")
                ).count()
                
                # 检查还有多少uploaded_pending_plan状态
                uploaded_pending_count = db.query(LocalCreative).join(Principal).filter(
                    Principal.name == '缇萃百货',
                    LocalCreative.status == 'uploaded_pending_plan',
                    LocalCreative.created_at >= db.func.now() - db.text("INTERVAL '24 hours'")
                ).count()
                
                logger.info(f"📊 重置后状态:")
                logger.info(f"   pending_upload: {pending_upload_count} 个")
                logger.info(f"   uploaded_pending_plan: {uploaded_pending_count} 个")
                
                if pending_upload_count > 0:
                    logger.success(f"✅ 成功重置，{pending_upload_count} 个素材等待重新上传")
                    return True
                else:
                    logger.warning("⚠️ 没有检测到pending_upload状态的素材")
                    return False
                
        except Exception as e:
            logger.error(f"❌ 验证重置结果失败: {e}")
            return False
    
    def _generate_reupload_report(self, results):
        """生成重新上传报告"""
        logger.info("\n📋 素材重新上传报告")
        logger.info("="*100)
        
        materials_to_reupload = results['materials_to_reupload']
        reset_success = results['reset_success']
        cleanup_success = results['cleanup_success']
        verification_success = results['verification_success']
        
        # 需要重新上传的素材
        logger.info(f"📊 需要重新上传: {len(materials_to_reupload)} 个素材")
        
        # 重置结果
        if reset_success:
            logger.success("✅ 状态重置: 执行成功")
        else:
            logger.error("❌ 状态重置: 执行失败")
        
        # 清理结果
        if cleanup_success:
            logger.success("✅ 记录清理: 执行成功")
        else:
            logger.error("❌ 记录清理: 执行失败")
        
        # 验证结果
        if verification_success:
            logger.success("✅ 验证结果: 素材已准备好重新上传")
        else:
            logger.warning("⚠️ 验证结果: 重置可能不完整")
        
        # 总体评估
        success_count = sum([
            len(materials_to_reupload) > 0,  # 发现了需要处理的素材
            reset_success,                   # 重置成功
            cleanup_success,                 # 清理成功
            verification_success             # 验证通过
        ])
        
        logger.info(f"\n🎯 重新上传准备成功率: {success_count}/4")
        
        if success_count >= 3:
            logger.success("🎊 素材重新上传准备成功")
            logger.success("💡 素材现在将通过正确的工作流重新上传到TEST账户")
            
            logger.info("\n🚀 下一步行动:")
            logger.info("   1. 系统将自动重新上传这些素材到TEST账户")
            logger.info("   2. 素材将获得新的video_id（属于TEST账户）")
            logger.info("   3. 然后可以正常创建测试计划")
            logger.info("   4. 观察上传进度和计划创建")
            
            return True
        else:
            logger.error("❌ 素材重新上传准备失败")
            logger.error("🔧 需要手动检查和修复")
            return False


def main():
    """主函数"""
    reuploader = MaterialReuploader()
    
    logger.info("🚀 启动素材重新上传流程")
    logger.info("🎯 目标：重新上传素材到正确的TEST账户，获得正确的video_id")
    
    success = reuploader.run_reupload_process()
    
    if success:
        logger.success("🎊 素材重新上传准备完成")
        logger.success("💡 建议：观察系统自动重新上传进度")
        return 0
    else:
        logger.error("❌ 素材重新上传准备失败")
        logger.error("🔧 建议：检查错误并手动修复")
        return 1


if __name__ == "__main__":
    exit(main())
