#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 修复千川项目素材状态一致性问题
清理条件: 项目状态管理完全重构时可删除

素材状态一致性修复工具
====================

基于2025-07-24的状态一致性审计报告，此工具用于修复发现的关键问题：
1. 更新MaterialStatus枚举定义
2. 修复STATUS_TRANSLATIONS映射
3. 验证状态转换逻辑
4. 检测和报告状态异常

使用方法:
python ai_tools/maintenance/ai_tool_20250724_maintenance_status_consistency_fix.py [action]

Actions:
- check: 检查当前状态一致性问题
- fix: 执行修复操作
- validate: 验证修复结果
- monitor: 持续监控状态异常
"""

import os
import sys
import json
import argparse
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from enum import Enum

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from src.qianchuan_aw.database.database import SessionLocal
from src.qianchuan_aw.database.models import LocalCreative, PlatformCreative, Campaign
from src.qianchuan_aw.utils.logger import logger
from sqlalchemy import func, text


class MaterialStatus(Enum):
    """统一的素材状态枚举 - 基于实际工作流定义"""
    # 基础流程状态
    NEW = "new"                                 # 新文件入库
    PENDING_GROUPING = "pending_grouping"       # 待分组
    PROCESSING = "processing"                   # 处理中
    UPLOADED_PENDING_PLAN = "uploaded_pending_plan"  # 已上传待建计划
    CREATING_PLAN = "creating_plan"             # 创建计划中
    TESTING_PENDING_REVIEW = "testing_pending_review"  # 测试待审核
    
    # 终态状态
    APPROVED = "approved"                       # 审核通过
    REJECTED = "rejected"                       # 审核拒绝
    ALREADY_TESTED = "already_tested"           # 已测试过
    HARVESTED = "harvested"                     # 已收割
    
    # 异常状态
    UPLOAD_FAILED = "upload_failed"             # 上传失败
    CHECK_FAILED = "check_failed"               # 检查失败
    RESET_BY_SYSTEM = "reset_by_system"         # 系统重置
    
    # 向后兼容状态 (逐步废弃)
    PENDING_UPLOAD = "pending_upload"           # 待上传 (旧)


# 完整的状态翻译映射
UPDATED_STATUS_TRANSLATIONS = {
    # 基础流程状态
    'new': '新入库',
    'pending_grouping': '待分组',
    'processing': '处理中',
    'uploaded_pending_plan': '已上传待建计划',
    'creating_plan': '创建计划中',
    'testing_pending_review': '测试待审核',
    
    # 终态状态
    'approved': '审核通过',
    'rejected': '审核拒绝',
    'already_tested': '已测试',
    'harvested': '已收割',
    
    # 异常状态
    'upload_failed': '上传失败',
    'check_failed': '检查失败',
    'reset_by_system': '系统重置',
    
    # 向后兼容 (标记为旧版本)
    'pending_upload': '待上传 (旧)',
    'uploading': '上传中 (旧)',
    'uploaded': '已上传 (旧)',
    'plan_pending': '等待计划 (旧)',
    'plan_created': '计划已创建 (旧)',
    'under_review': '审核中 (旧)',
    
    # 清理无效映射
    'in_production': '生产中 (无效)',
    'archived': '已归档 (无效)',
    'error': '错误状态 (无效)',
}

# 有效的状态转换路径
VALID_TRANSITIONS = {
    MaterialStatus.NEW: [MaterialStatus.PENDING_GROUPING],
    MaterialStatus.PENDING_GROUPING: [MaterialStatus.PROCESSING, MaterialStatus.UPLOAD_FAILED],
    MaterialStatus.PROCESSING: [MaterialStatus.UPLOADED_PENDING_PLAN, MaterialStatus.UPLOAD_FAILED],
    MaterialStatus.UPLOADED_PENDING_PLAN: [MaterialStatus.CREATING_PLAN, MaterialStatus.ALREADY_TESTED],
    MaterialStatus.CREATING_PLAN: [MaterialStatus.TESTING_PENDING_REVIEW, MaterialStatus.UPLOAD_FAILED],
    MaterialStatus.TESTING_PENDING_REVIEW: [MaterialStatus.APPROVED, MaterialStatus.REJECTED],
    MaterialStatus.APPROVED: [MaterialStatus.HARVESTED],
    MaterialStatus.REJECTED: [],  # 终态
    MaterialStatus.ALREADY_TESTED: [],  # 终态
    MaterialStatus.UPLOAD_FAILED: [MaterialStatus.PENDING_GROUPING],  # 重试
    MaterialStatus.CHECK_FAILED: [MaterialStatus.PENDING_GROUPING],   # 重试
    MaterialStatus.RESET_BY_SYSTEM: [MaterialStatus.PENDING_GROUPING], # 重置
    MaterialStatus.HARVESTED: [],  # 终态
    MaterialStatus.PENDING_UPLOAD: [MaterialStatus.PENDING_GROUPING],  # 向后兼容
}


class StatusConsistencyChecker:
    """状态一致性检查器"""
    
    def __init__(self):
        self.db = SessionLocal()
        self.issues = []
        self.stats = {}
    
    def check_all(self) -> Dict[str, Any]:
        """执行全面的状态一致性检查"""
        logger.info("🔍 开始执行状态一致性检查...")
        
        results = {
            'timestamp': datetime.now().isoformat(),
            'database_status_distribution': self._check_database_status_distribution(),
            'orphan_statuses': self._check_orphan_statuses(),
            'stuck_materials': self._check_stuck_materials(),
            'translation_issues': self._check_translation_issues(),
            'transition_validation': self._check_transition_validation(),
            'summary': {}
        }
        
        # 生成摘要
        total_issues = (
            len(results['orphan_statuses']) +
            len(results['stuck_materials']) +
            len(results['translation_issues'])
        )
        
        results['summary'] = {
            'total_materials': self.stats.get('total_materials', 0),
            'total_issues': total_issues,
            'critical_issues': len(results['stuck_materials']),
            'status_coverage': len([s for s in MaterialStatus]) / len(results['database_status_distribution']) if results['database_status_distribution'] else 0
        }
        
        logger.info(f"✅ 状态一致性检查完成，发现 {total_issues} 个问题")
        return results
    
    def _check_database_status_distribution(self) -> List[Dict[str, Any]]:
        """检查数据库中的状态分布"""
        try:
            status_counts = self.db.query(
                LocalCreative.status,
                func.count(LocalCreative.id).label('count')
            ).group_by(LocalCreative.status).all()
            
            distribution = []
            total = 0
            for status, count in status_counts:
                distribution.append({
                    'status': status,
                    'count': count,
                    'has_translation': status in UPDATED_STATUS_TRANSLATIONS,
                    'is_defined': any(s.value == status for s in MaterialStatus)
                })
                total += count
            
            self.stats['total_materials'] = total
            logger.info(f"📊 数据库状态分布: {len(distribution)} 种状态, {total} 个素材")
            return distribution
            
        except Exception as e:
            logger.error(f"检查数据库状态分布失败: {e}")
            return []
    
    def _check_orphan_statuses(self) -> List[Dict[str, Any]]:
        """检查孤儿状态（数据库中存在但代码中未定义）"""
        try:
            defined_statuses = {status.value for status in MaterialStatus}
            
            orphan_query = self.db.query(
                LocalCreative.status,
                func.count(LocalCreative.id).label('count')
            ).group_by(LocalCreative.status).all()
            
            orphans = []
            for status, count in orphan_query:
                if status not in defined_statuses:
                    orphans.append({
                        'status': status,
                        'count': count,
                        'severity': 'high' if count > 10 else 'medium'
                    })
            
            if orphans:
                logger.warning(f"⚠️ 发现 {len(orphans)} 个孤儿状态")
            
            return orphans
            
        except Exception as e:
            logger.error(f"检查孤儿状态失败: {e}")
            return []
    
    def _check_stuck_materials(self) -> List[Dict[str, Any]]:
        """检查卡死的素材（长时间未更新）"""
        try:
            # 检查超过24小时未更新的处理中素材
            cutoff_time = datetime.utcnow() - timedelta(hours=24)
            
            stuck_materials = self.db.query(LocalCreative).filter(
                LocalCreative.updated_at < cutoff_time,
                LocalCreative.status.in_(['processing', 'creating_plan', 'testing_pending_review'])
            ).all()
            
            stuck_list = []
            for material in stuck_materials:
                hours_stuck = (datetime.utcnow() - material.updated_at).total_seconds() / 3600
                stuck_list.append({
                    'id': material.id,
                    'filename': material.filename or f"ID_{material.id}",
                    'status': material.status,
                    'last_update': material.updated_at.isoformat(),
                    'hours_stuck': round(hours_stuck, 1),
                    'severity': 'critical' if hours_stuck > 72 else 'high'
                })
            
            if stuck_list:
                logger.warning(f"⚠️ 发现 {len(stuck_list)} 个卡死素材")
            
            return stuck_list
            
        except Exception as e:
            logger.error(f"检查卡死素材失败: {e}")
            return []
    
    def _check_translation_issues(self) -> List[Dict[str, Any]]:
        """检查翻译映射问题"""
        try:
            # 获取数据库中实际存在的状态
            actual_statuses = {row[0] for row in self.db.query(LocalCreative.status.distinct()).all()}
            
            issues = []
            
            # 检查缺失翻译
            for status in actual_statuses:
                if status not in UPDATED_STATUS_TRANSLATIONS:
                    issues.append({
                        'type': 'missing_translation',
                        'status': status,
                        'severity': 'medium'
                    })
            
            # 检查无效翻译（翻译存在但状态不存在）
            for status in UPDATED_STATUS_TRANSLATIONS:
                if status not in actual_statuses and not status.endswith(' (旧)') and not status.endswith(' (无效)'):
                    issues.append({
                        'type': 'invalid_translation',
                        'status': status,
                        'severity': 'low'
                    })
            
            return issues
            
        except Exception as e:
            logger.error(f"检查翻译问题失败: {e}")
            return []
    
    def _check_transition_validation(self) -> Dict[str, Any]:
        """检查状态转换验证逻辑"""
        try:
            # 统计各种状态转换的可能性
            defined_statuses = {status.value for status in MaterialStatus}
            transition_coverage = {}
            
            for status in MaterialStatus:
                valid_next = VALID_TRANSITIONS.get(status, [])
                transition_coverage[status.value] = {
                    'can_transition_to': [s.value for s in valid_next],
                    'is_terminal': len(valid_next) == 0,
                    'transition_count': len(valid_next)
                }
            
            return {
                'total_defined_statuses': len(defined_statuses),
                'total_transitions': sum(len(transitions) for transitions in VALID_TRANSITIONS.values()),
                'terminal_statuses': [s for s, info in transition_coverage.items() if info['is_terminal']],
                'transition_coverage': transition_coverage
            }
            
        except Exception as e:
            logger.error(f"检查状态转换验证失败: {e}")
            return {}
    
    def close(self):
        """关闭数据库连接"""
        self.db.close()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='素材状态一致性修复工具')
    parser.add_argument('action', choices=['check', 'fix', 'validate', 'monitor'], 
                       help='执行的操作')
    parser.add_argument('--output', '-o', help='输出文件路径')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    if args.action == 'check':
        # 执行状态一致性检查
        checker = StatusConsistencyChecker()
        try:
            results = checker.check_all()
            
            # 输出结果
            if args.output:
                with open(args.output, 'w', encoding='utf-8') as f:
                    json.dump(results, f, ensure_ascii=False, indent=2)
                logger.info(f"📄 检查结果已保存到: {args.output}")
            else:
                print(json.dumps(results, ensure_ascii=False, indent=2))
            
            # 显示摘要
            summary = results['summary']
            print(f"\n📊 检查摘要:")
            print(f"   总素材数: {summary['total_materials']}")
            print(f"   发现问题: {summary['total_issues']}")
            print(f"   关键问题: {summary['critical_issues']}")
            print(f"   状态覆盖率: {summary['status_coverage']:.1%}")
            
        finally:
            checker.close()
    
    elif args.action == 'fix':
        logger.info("🔧 状态一致性修复功能开发中...")
        logger.info("当前版本仅支持检查功能，修复功能将在后续版本中提供")
    
    elif args.action == 'validate':
        logger.info("✅ 状态一致性验证功能开发中...")
    
    elif args.action == 'monitor':
        logger.info("📊 状态监控功能开发中...")


if __name__ == '__main__':
    main()
