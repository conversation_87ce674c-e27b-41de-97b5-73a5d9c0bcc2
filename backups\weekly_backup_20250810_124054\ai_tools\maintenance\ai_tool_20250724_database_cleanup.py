#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 清理千川自动化项目数据库中的无效记录
清理条件: 功能被替代时删除
"""

import os
import sys
from typing import List, Dict

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from loguru import logger
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import LocalCreative
from qianchuan_aw.utils.config_loader import load_settings

class DatabaseCleaner:
    """数据库清理工具"""
    
    def __init__(self):
        self.app_settings = load_settings()
        self.base_dir = self.app_settings.get('custom_workflow_assets_dir', 'G:/workflow_assets')
        self.process_dir = os.path.join(self.base_dir, '01_materials_to_process', '缇萃百货')
        
    def scan_existing_files(self) -> set:
        """扫描实际存在的文件"""
        logger.info("📋 扫描实际存在的文件...")
        
        existing_files = set()
        video_extensions = ['.mp4', '.mov', '.avi', '.mkv']
        
        try:
            for file in os.listdir(self.process_dir):
                file_path = os.path.join(self.process_dir, file)
                if os.path.isfile(file_path):
                    file_ext = os.path.splitext(file)[1].lower()
                    if file_ext in video_extensions:
                        existing_files.add(file)
            
            logger.success(f"✅ 扫描完成，找到 {len(existing_files)} 个实际文件")
            
        except Exception as e:
            logger.error(f"❌ 扫描实际文件失败: {e}")
        
        return existing_files
    
    def identify_orphaned_records(self) -> List[Dict]:
        """识别孤儿记录（文件不存在但数据库有记录）"""
        logger.info("🔍 识别孤儿记录...")
        
        existing_files = self.scan_existing_files()
        orphaned_records = []
        
        try:
            with database_session() as db:
                # 查询所有相关记录
                creatives = db.query(LocalCreative).filter(
                    LocalCreative.file_path.like('%01_materials_to_process%缇萃百货%')
                ).all()
                
                for creative in creatives:
                    filename = creative.filename
                    
                    # 检查文件是否实际存在
                    if filename not in existing_files:
                        orphaned_records.append({
                            'id': creative.id,
                            'filename': filename,
                            'file_path': creative.file_path,
                            'status': creative.status,
                            'created_at': creative.created_at
                        })
                
                logger.warning(f"⚠️ 发现 {len(orphaned_records)} 条孤儿记录")
                
        except Exception as e:
            logger.error(f"❌ 识别孤儿记录失败: {e}")
        
        return orphaned_records
    
    def clean_orphaned_records(self, dry_run: bool = True) -> int:
        """清理孤儿记录"""
        logger.info(f"🧹 {'预览' if dry_run else '执行'}清理孤儿记录...")
        
        orphaned_records = self.identify_orphaned_records()
        
        if not orphaned_records:
            logger.info("ℹ️ 没有发现需要清理的孤儿记录")
            return 0
        
        # 按状态分类
        status_counts = {}
        for record in orphaned_records:
            status = record['status']
            status_counts[status] = status_counts.get(status, 0) + 1
        
        logger.info("📊 孤儿记录状态分布:")
        for status, count in status_counts.items():
            logger.info(f"   {status}: {count} 条")
        
        if dry_run:
            logger.info("🔍 预览模式：不会实际删除记录")
            logger.info("💡 要执行实际清理，请使用 --no-dry-run 参数")
            return len(orphaned_records)
        
        # 实际删除
        deleted_count = 0

        try:
            with database_session() as db:
                from qianchuan_aw.database.models import PlatformCreative

                for record in orphaned_records:
                    creative = db.query(LocalCreative).filter_by(id=record['id']).first()
                    if creative:
                        # 先删除相关的PlatformCreative记录
                        platform_creatives = db.query(PlatformCreative).filter_by(local_creative_id=creative.id).all()
                        for pc in platform_creatives:
                            db.delete(pc)

                        # 然后删除LocalCreative记录
                        db.delete(creative)
                        deleted_count += 1

                        if deleted_count <= 10:  # 只显示前10个示例
                            logger.info(f"🗑️ 删除记录: {record['filename']} (ID: {record['id']})")

                db.commit()
                logger.success(f"✅ 清理完成，删除了 {deleted_count} 条孤儿记录")
                
        except Exception as e:
            logger.error(f"❌ 清理孤儿记录失败: {e}")
            return 0
        
        return deleted_count
    
    def reset_failed_uploads(self) -> int:
        """重置失败的上传状态"""
        logger.info("🔄 重置失败的上传状态...")
        
        existing_files = self.scan_existing_files()
        reset_count = 0
        
        try:
            with database_session() as db:
                # 查找状态为upload_failed但文件实际存在的记录
                failed_creatives = db.query(LocalCreative).filter(
                    LocalCreative.status == 'upload_failed',
                    LocalCreative.file_path.like('%01_materials_to_process%缇萃百货%')
                ).all()
                
                for creative in failed_creatives:
                    filename = creative.filename
                    
                    # 如果文件实际存在，重置状态
                    if filename in existing_files:
                        creative.status = 'pending_grouping'
                        reset_count += 1
                        
                        if reset_count <= 10:  # 只显示前10个示例
                            logger.info(f"🔄 重置状态: {filename} -> pending_grouping")
                
                if reset_count > 0:
                    db.commit()
                    logger.success(f"✅ 重置完成，共重置 {reset_count} 条记录")
                else:
                    logger.info("ℹ️ 没有发现需要重置的记录")
                
        except Exception as e:
            logger.error(f"❌ 重置失败上传状态失败: {e}")
            return 0
        
        return reset_count
    
    def generate_cleanup_report(self):
        """生成清理报告"""
        logger.info("📊 生成清理报告...")
        
        existing_files = self.scan_existing_files()
        orphaned_records = self.identify_orphaned_records()
        
        try:
            with database_session() as db:
                # 统计各种状态的记录
                total_records = db.query(LocalCreative).filter(
                    LocalCreative.file_path.like('%01_materials_to_process%缇萃百货%')
                ).count()
                
                status_stats = {}
                statuses = ['pending_grouping', 'upload_failed', 'uploaded', 'processing', 'completed']
                
                for status in statuses:
                    count = db.query(LocalCreative).filter(
                        LocalCreative.status == status,
                        LocalCreative.file_path.like('%01_materials_to_process%缇萃百货%')
                    ).count()
                    status_stats[status] = count
                
                logger.critical("📊 数据库清理报告")
                logger.critical("=" * 50)
                logger.critical(f"实际文件数量: {len(existing_files)}")
                logger.critical(f"数据库记录总数: {total_records}")
                logger.critical(f"孤儿记录数量: {len(orphaned_records)}")
                logger.critical(f"有效记录数量: {total_records - len(orphaned_records)}")
                logger.critical("")
                logger.critical("状态分布:")
                for status, count in status_stats.items():
                    logger.critical(f"  {status}: {count}")
                logger.critical("=" * 50)
                
        except Exception as e:
            logger.error(f"❌ 生成清理报告失败: {e}")
    
    def full_cleanup(self, dry_run: bool = True):
        """执行完整清理"""
        logger.critical("🚀 开始完整数据库清理...")
        
        # 1. 生成清理前报告
        logger.info("📊 清理前状态:")
        self.generate_cleanup_report()
        
        # 2. 清理孤儿记录
        orphaned_count = self.clean_orphaned_records(dry_run)
        
        # 3. 重置失败上传状态
        if not dry_run:
            reset_count = self.reset_failed_uploads()
        else:
            reset_count = 0
        
        # 4. 生成清理后报告
        if not dry_run:
            logger.info("📊 清理后状态:")
            self.generate_cleanup_report()
        
        logger.success("✅ 完整清理完成！")
        logger.info(f"📈 清理统计:")
        logger.info(f"   孤儿记录: {orphaned_count} 条")
        logger.info(f"   重置状态: {reset_count} 条")
        
        return orphaned_count, reset_count

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='数据库清理工具')
    parser.add_argument('--no-dry-run', action='store_true', help='执行实际清理（默认为预览模式）')
    parser.add_argument('--report-only', action='store_true', help='只生成报告，不执行清理')
    
    args = parser.parse_args()
    
    try:
        cleaner = DatabaseCleaner()
        
        if args.report_only:
            cleaner.generate_cleanup_report()
        else:
            dry_run = not args.no_dry_run
            cleaner.full_cleanup(dry_run)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库清理过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    main()
