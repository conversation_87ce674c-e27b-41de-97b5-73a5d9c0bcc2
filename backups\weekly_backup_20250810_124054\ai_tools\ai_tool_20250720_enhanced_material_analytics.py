#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI工具: 增强版素材审核报表页面
生成时间: 2025-07-20
用途: 全面的素材审核数据分析报表，支持多维度分析和钻取查询
生命周期: 永久工具
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime, timedelta, time
import pytz
import io
import base64
from typing import Dict, List, Optional, Tuple
import sys
import os

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, project_root)
sys.path.insert(0, src_path)

from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import LocalCreative, Campaign, PlatformCreative, AdAccount, Principal
from sqlalchemy import text, and_, or_, func, desc, asc
from sqlalchemy.orm import joinedload

# 中国时区
CHINA_TZ = pytz.timezone('Asia/Shanghai')

def render_enhanced_material_analytics_page():
    """增强版素材审核报表页面"""
    st.header("📊 素材审核报表 (增强版)")
    st.markdown("全面的素材审核数据分析，支持多维度分析、钻取查询和实时监控")

    # 业务范围说明
    st.info("🎯 **业务范围**: 本报表仅分析TEST账户的素材测试工作流数据，不包含DELIVERY账户（投放户）的数据。")
    
    # 侧边栏配置
    with st.sidebar:
        st.markdown("### 📋 报表配置")
        
        # 时间范围选择
        time_range = render_time_range_selector()
        
        # 数据维度选择
        analysis_dimension = st.selectbox(
            "分析维度",
            ["📊 综合概览", "🏢 广告账户", "🎬 视频素材", "👤 作者分析", "🔍 深度追踪"],
            help="选择要分析的数据维度"
        )
        
        # 刷新控制
        st.markdown("---")
        auto_refresh = st.checkbox("🔄 自动刷新 (5分钟)", value=False)
        manual_refresh = st.button("🔄 立即刷新", use_container_width=True)
        
        if st.button("📥 导出数据", use_container_width=True):
            export_data_to_excel(time_range)
    
    # 自动刷新逻辑
    if auto_refresh:
        st.rerun()
    
    # 根据选择的维度渲染对应页面
    if analysis_dimension == "📊 综合概览":
        render_comprehensive_overview(time_range)
    elif analysis_dimension == "🏢 广告账户":
        render_account_dimension_analysis(time_range)
    elif analysis_dimension == "🎬 视频素材":
        render_material_dimension_analysis(time_range)
    elif analysis_dimension == "👤 作者分析":
        render_author_dimension_analysis(time_range)
    elif analysis_dimension == "🔍 深度追踪":
        render_deep_tracking_analysis(time_range)

def render_time_range_selector() -> Dict:
    """渲染时间范围选择器"""
    st.markdown("#### ⏰ 时间范围")
    
    # 快捷选项
    quick_options = {
        "今天": (datetime.now(CHINA_TZ).replace(hour=0, minute=0, second=0, microsecond=0),
                datetime.now(CHINA_TZ)),
        "昨天": (datetime.now(CHINA_TZ).replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=1),
                datetime.now(CHINA_TZ).replace(hour=0, minute=0, second=0, microsecond=0)),
        "最近7天": (datetime.now(CHINA_TZ) - timedelta(days=7), datetime.now(CHINA_TZ)),
        "最近30天": (datetime.now(CHINA_TZ) - timedelta(days=30), datetime.now(CHINA_TZ)),
        "本月": (datetime.now(CHINA_TZ).replace(day=1, hour=0, minute=0, second=0, microsecond=0),
               datetime.now(CHINA_TZ)),
        "自定义": None
    }
    
    quick_select = st.selectbox("快捷选择", list(quick_options.keys()), index=2)
    
    if quick_select == "自定义":
        col1, col2 = st.columns(2)
        with col1:
            start_date = st.date_input(
                "开始日期",
                value=datetime.now(CHINA_TZ).date() - timedelta(days=7)
            )
            start_time = st.time_input("开始时间", value=time(0, 0))
        
        with col2:
            end_date = st.date_input(
                "结束日期", 
                value=datetime.now(CHINA_TZ).date()
            )
            end_time = st.time_input("结束时间", value=time(23, 59))
        
        start_datetime = CHINA_TZ.localize(datetime.combine(start_date, start_time))
        end_datetime = CHINA_TZ.localize(datetime.combine(end_date, end_time))
    else:
        start_datetime, end_datetime = quick_options[quick_select]
    
    # 显示选择的时间范围
    st.info(f"📅 时间范围: {start_datetime.strftime('%Y-%m-%d %H:%M')} 至 {end_datetime.strftime('%Y-%m-%d %H:%M')}")
    
    return {
        'start': start_datetime,
        'end': end_datetime,
        'quick_select': quick_select
    }

def render_comprehensive_overview(time_range: Dict):
    """渲染综合概览"""
    st.markdown("### 📊 综合数据概览")
    
    try:
        with database_session() as db:
            # 获取基础统计数据
            stats = get_comprehensive_stats(db, time_range)
            
            # 关键指标展示
            render_key_metrics(stats)
            
            # 趋势图表
            render_trend_charts(db, time_range)
            
            # 状态分布
            render_status_distribution(db, time_range)
            
            # 实时处理状态
            render_realtime_processing_status(db)
            
    except Exception as e:
        st.error(f"❌ 获取综合概览数据失败: {e}")

def render_account_dimension_analysis(time_range: Dict):
    """渲染广告账户维度分析"""
    st.markdown("### 🏢 广告账户维度分析")
    
    try:
        with database_session() as db:
            # 获取账户列表
            accounts = db.query(AdAccount).options(joinedload(AdAccount.principal)).all()
            
            if not accounts:
                st.warning("📭 暂无广告账户数据")
                return
            
            # 账户选择器
            selected_accounts = st.multiselect(
                "选择要分析的账户",
                options=[f"{acc.name} ({acc.account_id_qc})" for acc in accounts],
                default=[f"{acc.name} ({acc.account_id_qc})" for acc in accounts[:5]]
            )
            
            if not selected_accounts:
                st.warning("请选择至少一个账户进行分析")
                return
            
            # 获取选中账户的ID
            selected_account_ids = []
            for acc_display in selected_accounts:
                for acc in accounts:
                    if f"{acc.name} ({acc.account_id_qc})" == acc_display:
                        selected_account_ids.append(acc.id)
                        break
            
            # 账户性能对比表
            render_account_performance_table(db, selected_account_ids, time_range)
            
            # 账户详细分析
            render_account_detailed_analysis(db, selected_account_ids, time_range)
            
    except Exception as e:
        st.error(f"❌ 获取账户维度分析数据失败: {e}")

def render_material_dimension_analysis(time_range: Dict):
    """渲染视频素材维度分析"""
    st.markdown("### 🎬 视频素材维度分析")
    
    try:
        with database_session() as db:
            # 素材状态分布
            render_material_status_analysis(db, time_range)
            
            # 素材处理时间线
            render_material_timeline_analysis(db, time_range)
            
            # 素材成功率趋势
            render_material_success_rate_trend(db, time_range)
            
            # 素材来源分析
            render_material_source_analysis(db, time_range)
            
    except Exception as e:
        st.error(f"❌ 获取素材维度分析数据失败: {e}")

def render_author_dimension_analysis(time_range: Dict):
    """渲染作者维度分析"""
    st.markdown("### 👤 作者维度分析")
    
    try:
        with database_session() as db:
            # 作者产出统计
            render_author_productivity_stats(db, time_range)
            
            # 作者质量分析
            render_author_quality_analysis(db, time_range)
            
            # 作者效率对比
            render_author_efficiency_comparison(db, time_range)
            
    except Exception as e:
        st.error(f"❌ 获取作者维度分析数据失败: {e}")

def render_deep_tracking_analysis(time_range: Dict):
    """渲染深度追踪分析"""
    st.markdown("### 🔍 深度追踪分析")
    
    # 搜索功能
    search_type = st.selectbox(
        "搜索类型",
        ["🎬 素材追踪", "🎯 计划追踪", "🏢 账户追踪", "❌ 失败分析"]
    )
    
    if search_type == "🎬 素材追踪":
        render_material_tracking()
    elif search_type == "🎯 计划追踪":
        render_campaign_tracking()
    elif search_type == "🏢 账户追踪":
        render_account_tracking(time_range)
    elif search_type == "❌ 失败分析":
        render_failure_analysis(time_range)

def get_comprehensive_stats(db, time_range: Dict) -> Dict:
    """获取综合统计数据"""
    start_time = time_range['start']
    end_time = time_range['end']

    # 基础查询
    materials_query = db.query(LocalCreative).filter(
        LocalCreative.created_at >= start_time,
        LocalCreative.created_at <= end_time
    )

    campaigns_query = db.query(Campaign).filter(
        Campaign.created_at >= start_time,
        Campaign.created_at <= end_time
    )

    # 统计数据
    total_materials = materials_query.count()
    total_campaigns = campaigns_query.count()

    # 素材状态统计
    material_status_stats = db.query(
        LocalCreative.status,
        func.count(LocalCreative.id).label('count')
    ).filter(
        LocalCreative.created_at >= start_time,
        LocalCreative.created_at <= end_time
    ).group_by(LocalCreative.status).all()

    # 计划状态统计 - 使用原生SQL避免模型字段问题
    try:
        campaign_status_stats = db.execute(text("""
            SELECT status, appeal_status, COUNT(*) as count
            FROM campaigns
            WHERE created_at >= :start_time AND created_at <= :end_time
            GROUP BY status, appeal_status
        """), {
            'start_time': start_time,
            'end_time': end_time
        }).fetchall()
    except Exception as e:
        st.warning(f"获取计划状态统计时出错: {e}")
        campaign_status_stats = []

    return {
        'total_materials': total_materials,
        'total_campaigns': total_campaigns,
        'material_status_stats': material_status_stats,
        'campaign_status_stats': campaign_status_stats
    }

def render_key_metrics(stats: Dict):
    """渲染关键指标"""
    col1, col2, col3, col4, col5 = st.columns(5)
    
    with col1:
        st.metric("📁 总素材数", f"{stats['total_materials']:,}")
    
    with col2:
        st.metric("🎯 总计划数", f"{stats['total_campaigns']:,}")
    
    with col3:
        # 计算素材成功率
        approved_count = sum(stat.count for stat in stats['material_status_stats'] 
                           if stat.status == 'approved')
        success_rate = (approved_count / stats['total_materials'] * 100) if stats['total_materials'] > 0 else 0
        st.metric("✅ 素材成功率", f"{success_rate:.1f}%")
    
    with col4:
        # 计算计划完成率
        completed_count = sum(stat.count for stat in stats['campaign_status_stats'] 
                            if stat.status == 'COMPLETED')
        completion_rate = (completed_count / stats['total_campaigns'] * 100) if stats['total_campaigns'] > 0 else 0
        st.metric("🎯 计划完成率", f"{completion_rate:.1f}%")
    
    with col5:
        # 计算处理效率
        if stats['total_materials'] > 0:
            efficiency = (stats['total_campaigns'] / stats['total_materials'] * 100)
            st.metric("⚡ 处理效率", f"{efficiency:.1f}%")
        else:
            st.metric("⚡ 处理效率", "0%")

def render_trend_charts(db, time_range: Dict):
    """渲染趋势图表"""
    st.markdown("#### 📈 数据趋势分析")
    
    # 获取每日趋势数据
    daily_stats = get_daily_trend_data(db, time_range)
    
    if daily_stats:
        # 创建趋势图
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('素材处理趋势', '计划创建趋势', '成功率趋势', '累计统计'),
            specs=[[{"secondary_y": True}, {"secondary_y": True}],
                   [{"secondary_y": True}, {"secondary_y": True}]]
        )
        
        dates = list(daily_stats.keys())
        materials = [daily_stats[date]['materials'] for date in dates]
        campaigns = [daily_stats[date]['campaigns'] for date in dates]
        success_rates = [daily_stats[date]['success_rate'] for date in dates]
        
        # 素材处理趋势
        fig.add_trace(
            go.Scatter(x=dates, y=materials, name="素材数量", line=dict(color='blue')),
            row=1, col=1
        )
        
        # 计划创建趋势
        fig.add_trace(
            go.Scatter(x=dates, y=campaigns, name="计划数量", line=dict(color='green')),
            row=1, col=2
        )
        
        # 成功率趋势
        fig.add_trace(
            go.Scatter(x=dates, y=success_rates, name="成功率", line=dict(color='orange')),
            row=2, col=1
        )
        
        # 累计统计
        cumulative_materials = pd.Series(materials).cumsum().tolist()
        fig.add_trace(
            go.Scatter(x=dates, y=cumulative_materials, name="累计素材", line=dict(color='purple')),
            row=2, col=2
        )
        
        fig.update_layout(height=600, showlegend=True)
        st.plotly_chart(fig, use_container_width=True)

def render_status_distribution(db, time_range: Dict):
    """渲染状态分布"""
    st.markdown("#### 🥧 状态分布分析")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**素材状态分布**")
        material_status_data = get_material_status_distribution(db, time_range)
        
        if material_status_data:
            fig_material = px.pie(
                values=list(material_status_data.values()),
                names=list(material_status_data.keys()),
                title="素材状态分布"
            )
            st.plotly_chart(fig_material, use_container_width=True)
    
    with col2:
        st.markdown("**计划状态分布**")
        campaign_status_data = get_campaign_status_distribution(db, time_range)
        
        if campaign_status_data:
            fig_campaign = px.pie(
                values=list(campaign_status_data.values()),
                names=list(campaign_status_data.keys()),
                title="计划状态分布"
            )
            st.plotly_chart(fig_campaign, use_container_width=True)

def render_realtime_processing_status(db):
    """渲染实时处理状态"""
    st.markdown("#### 🔄 实时处理状态")
    
    try:
        # 获取当前正在处理的任务
        processing_materials = db.query(LocalCreative).filter(
            LocalCreative.status.in_(['pending_upload', 'creating_plan', 'testing_pending_review'])
        ).count()
        
        pending_campaigns = db.query(Campaign).filter(
            Campaign.status.in_(['AUDITING', 'APPEAL_TIMEOUT'])
        ).count()
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("🔄 处理中素材", processing_materials)
        
        with col2:
            st.metric("⏳ 待处理计划", pending_campaigns)
        
        with col3:
            # 检查Redis队列状态
            try:
                import redis
                from qianchuan_aw.utils.config_manager import get_config_manager
                
                config_manager = get_config_manager()
                redis_config = config_manager.get_redis_config()
                
                r = redis.Redis(
                    host=redis_config.get('host', 'localhost'),
                    port=redis_config.get('port', 6379),
                    db=redis_config.get('db', 0)
                )
                
                queue_length = r.llen('celery')
                st.metric("📋 任务队列", queue_length)
                
            except Exception:
                st.metric("📋 任务队列", "N/A")
                
    except Exception as e:
        st.error(f"获取实时状态失败: {e}")

def render_account_performance_table(db, account_ids: List[int], time_range: Dict):
    """渲染账户性能对比表"""
    st.markdown("#### 📊 账户性能对比")
    
    try:
        # 构建账户性能数据
        performance_data = []
        
        for account_id in account_ids:
            account = db.query(AdAccount).filter(AdAccount.id == account_id).first()
            if not account:
                continue
            
            # 获取该账户的统计数据
            materials_count = db.query(LocalCreative).filter(
                LocalCreative.uploaded_to_account_id == account_id,
                LocalCreative.created_at >= time_range['start'],
                LocalCreative.created_at <= time_range['end']
            ).count()
            
            campaigns_count = db.query(Campaign).filter(
                Campaign.account_id == account_id,
                Campaign.created_at >= time_range['start'],
                Campaign.created_at <= time_range['end']
            ).count()
            
            # 成功率计算
            approved_materials = db.query(LocalCreative).filter(
                LocalCreative.uploaded_to_account_id == account_id,
                LocalCreative.status == 'approved',
                LocalCreative.created_at >= time_range['start'],
                LocalCreative.created_at <= time_range['end']
            ).count()
            
            success_rate = (approved_materials / materials_count * 100) if materials_count > 0 else 0
            
            performance_data.append({
                '账户名称': account.name,
                '账户ID': account.account_id_qc,
                '素材数量': materials_count,
                '计划数量': campaigns_count,
                '成功素材': approved_materials,
                '成功率(%)': f"{success_rate:.1f}%",
                '处理效率': f"{(campaigns_count/materials_count*100):.1f}%" if materials_count > 0 else "0%"
            })
        
        if performance_data:
            df = pd.DataFrame(performance_data)
            st.dataframe(df, use_container_width=True)
            
            # 可点击的账户详情
            if st.button("📋 查看详细分析"):
                st.session_state.show_account_details = True
        else:
            st.info("📭 选定时间范围内无账户数据")
            
    except Exception as e:
        st.error(f"获取账户性能数据失败: {e}")

# 辅助函数
def get_daily_trend_data(db, time_range: Dict) -> Dict:
    """获取每日趋势数据"""
    try:
        # 使用原生SQL查询避免SQLAlchemy版本兼容问题
        daily_materials = db.execute(text("""
            SELECT
                DATE(lc.created_at) as date,
                COUNT(*) as materials,
                SUM(CASE WHEN lc.status = 'approved' THEN 1 ELSE 0 END) as approved
            FROM local_creatives lc
            JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
            WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                AND lc.created_at >= :start_time AND lc.created_at <= :end_time
            GROUP BY DATE(lc.created_at)
            ORDER BY DATE(lc.created_at)
        """), {
            'start_time': time_range['start'],
            'end_time': time_range['end']
        }).fetchall()

        daily_campaigns = db.execute(text("""
            SELECT
                DATE(created_at) as date,
                COUNT(*) as campaigns
            FROM campaigns
            WHERE created_at >= :start_time AND created_at <= :end_time
            GROUP BY DATE(created_at)
            ORDER BY DATE(created_at)
        """), {
            'start_time': time_range['start'],
            'end_time': time_range['end']
        }).fetchall()

        # 合并数据
        result = {}
        for row in daily_materials:
            date_str = row.date.strftime('%Y-%m-%d')
            success_rate = (row.approved / row.materials * 100) if row.materials > 0 else 0
            result[date_str] = {
                'materials': row.materials,
                'campaigns': 0,
                'success_rate': success_rate
            }

        for row in daily_campaigns:
            date_str = row.date.strftime('%Y-%m-%d')
            if date_str in result:
                result[date_str]['campaigns'] = row.campaigns
            else:
                result[date_str] = {'materials': 0, 'campaigns': row.campaigns, 'success_rate': 0}

        return result
    except Exception as e:
        st.error(f"获取每日趋势数据失败: {e}")
        return {}

def get_material_status_distribution(db, time_range: Dict) -> Dict:
    """获取素材状态分布"""
    try:
        status_stats = db.query(
            LocalCreative.status,
            func.count(LocalCreative.id).label('count')
        ).filter(
            LocalCreative.created_at >= time_range['start'],
            LocalCreative.created_at <= time_range['end']
        ).group_by(LocalCreative.status).all()

        # 状态中文映射
        status_map = {
            'new': '新建',
            'pending_upload': '待上传',
            'uploaded_pending_plan': '已上传待建计划',
            'creating_plan': '创建计划中',
            'testing_pending_review': '测试待审核',
            'approved': '审核通过',
            'rejected': '审核拒绝',
            'upload_failed': '上传失败',
            'already_tested': '已测试',
            'pending_grouping': '待分组'
        }

        return {status_map.get(stat.status, stat.status): stat.count for stat in status_stats}
    except Exception as e:
        st.error(f"获取素材状态分布失败: {e}")
        return {}

def get_campaign_status_distribution(db, time_range: Dict) -> Dict:
    """获取计划状态分布"""
    try:
        # 使用原生SQL查询避免模型字段问题
        status_stats = db.execute(text("""
            SELECT status, appeal_status, COUNT(*) as count
            FROM campaigns
            WHERE created_at >= :start_time AND created_at <= :end_time
            GROUP BY status, appeal_status
        """), {
            'start_time': time_range['start'],
            'end_time': time_range['end']
        }).fetchall()

        result = {}
        for stat in status_stats:
            key = f"{stat.status}"
            if stat.appeal_status and stat.appeal_status != 'appeal_pending':
                key += f" ({stat.appeal_status})"
            result[key] = stat.count

        return result
    except Exception as e:
        st.error(f"获取计划状态分布失败: {e}")
        return {}

def export_data_to_excel(time_range: Dict):
    """导出数据到Excel"""
    try:
        with database_session() as db:
            # 获取导出数据
            materials_data = db.query(LocalCreative).options(
                joinedload(LocalCreative.principal)
            ).filter(
                LocalCreative.created_at >= time_range['start'],
                LocalCreative.created_at <= time_range['end']
            ).all()

            # 使用原生SQL获取计划数据，避免模型字段问题
            campaigns_raw = db.execute(text("""
                SELECT c.campaign_id_qc, c.status, c.appeal_status, c.created_at, a.name as account_name
                FROM campaigns c
                LEFT JOIN ad_accounts a ON c.account_id = a.id
                WHERE c.created_at >= :start_time AND c.created_at <= :end_time
                ORDER BY c.created_at DESC
            """), {
                'start_time': time_range['start'],
                'end_time': time_range['end']
            }).fetchall()

            # 创建Excel文件
            output = io.BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                # 素材数据表
                materials_df = pd.DataFrame([{
                    '素材ID': m.id,
                    '文件名': m.filename or 'N/A',
                    '状态': m.status,
                    '作者': m.principal.name if m.principal else 'N/A',
                    '创建时间': m.created_at.strftime('%Y-%m-%d %H:%M:%S') if m.created_at else 'N/A',
                    '更新时间': m.updated_at.strftime('%Y-%m-%d %H:%M:%S') if m.updated_at else 'N/A'
                } for m in materials_data])
                materials_df.to_excel(writer, sheet_name='素材数据', index=False)

                # 计划数据表
                campaigns_df = pd.DataFrame([{
                    '计划ID': c.campaign_id_qc,
                    '账户名称': c.account_name or 'N/A',
                    '状态': c.status,
                    '申诉状态': c.appeal_status or 'N/A',
                    '创建时间': c.created_at.strftime('%Y-%m-%d %H:%M:%S') if c.created_at else 'N/A'
                } for c in campaigns_raw])
                campaigns_df.to_excel(writer, sheet_name='计划数据', index=False)

            # 提供下载
            excel_data = output.getvalue()
            b64 = base64.b64encode(excel_data).decode()

            filename = f"素材审核报表_{time_range['start'].strftime('%Y%m%d')}_{time_range['end'].strftime('%Y%m%d')}.xlsx"
            href = f'<a href="data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,{b64}" download="{filename}">📥 下载Excel报表</a>'
            st.markdown(href, unsafe_allow_html=True)
            st.success("✅ Excel报表已生成，点击上方链接下载")

    except Exception as e:
        st.error(f"导出Excel失败: {e}")

def render_material_tracking():
    """素材追踪功能"""
    st.markdown("#### 🎬 素材追踪")

    search_term = st.text_input("🔍 输入素材文件名或ID进行搜索", placeholder="例如: video_001.mp4")

    if search_term:
        try:
            with database_session() as db:
                # 搜索素材
                materials = db.query(LocalCreative).options(
                    joinedload(LocalCreative.principal)
                ).filter(
                    or_(
                        LocalCreative.filename.ilike(f'%{search_term}%'),
                        LocalCreative.id == int(search_term) if search_term.isdigit() else False
                    )
                ).all()

                if materials:
                    for material in materials:
                        with st.expander(f"📁 {material.filename or f'素材ID: {material.id}'}"):
                            col1, col2 = st.columns(2)

                            with col1:
                                st.markdown("**基本信息**")
                                st.write(f"ID: {material.id}")
                                st.write(f"状态: {material.status}")
                                st.write(f"作者: {material.principal.name if material.principal else 'N/A'}")
                                st.write(f"创建时间: {material.created_at}")

                            with col2:
                                st.markdown("**处理信息**")
                                st.write(f"上传账户: {material.uploaded_to_account_id or 'N/A'}")
                                st.write(f"素材ID(千川): {material.material_id_qc or 'N/A'}")
                                st.write(f"收割状态: {material.harvest_status}")

                            # 查找相关计划
                            if material.uploaded_to_account_id:
                                campaigns = db.query(Campaign).filter(
                                    Campaign.account_id == material.uploaded_to_account_id
                                ).limit(5).all()

                                if campaigns:
                                    st.markdown("**相关计划**")
                                    for campaign in campaigns:
                                        st.write(f"- {campaign.campaign_id_qc} ({campaign.status})")
                else:
                    st.info("🔍 未找到匹配的素材")

        except Exception as e:
            st.error(f"搜索失败: {e}")

def render_failure_analysis(time_range: Dict):
    """失败分析功能"""
    st.markdown("#### ❌ 失败分析")

    failure_type = st.selectbox(
        "选择失败类型",
        ["📁 上传失败素材", "❌ 审核失败素材", "⏰ 超时计划", "🚫 申诉失败计划"]
    )

    try:
        with database_session() as db:
            if failure_type == "📁 上传失败素材":
                failed_materials = db.query(LocalCreative).options(
                    joinedload(LocalCreative.principal)
                ).filter(
                    LocalCreative.status == 'upload_failed',
                    LocalCreative.created_at >= time_range['start'],
                    LocalCreative.created_at <= time_range['end']
                ).all()

                if failed_materials:
                    st.write(f"找到 {len(failed_materials)} 个上传失败的素材:")

                    failure_data = []
                    for material in failed_materials:
                        failure_data.append({
                            'ID': material.id,
                            '文件名': material.filename or 'N/A',
                            '作者': material.principal.name if material.principal else 'N/A',
                            '失败时间': material.updated_at.strftime('%Y-%m-%d %H:%M') if material.updated_at else 'N/A',
                            '重试次数': material.harvest_attempt_count or 0
                        })

                    df = pd.DataFrame(failure_data)
                    st.dataframe(df, use_container_width=True)

                    # 重试按钮
                    if st.button("🔄 批量重试上传失败素材"):
                        st.info("重试功能开发中...")
                else:
                    st.success("✅ 当前时间范围内无上传失败素材")

            elif failure_type == "❌ 审核失败素材":
                rejected_materials = db.query(LocalCreative).options(
                    joinedload(LocalCreative.principal)
                ).filter(
                    LocalCreative.status == 'rejected',
                    LocalCreative.created_at >= time_range['start'],
                    LocalCreative.created_at <= time_range['end']
                ).all()

                if rejected_materials:
                    st.write(f"找到 {len(rejected_materials)} 个审核失败的素材:")

                    # 按作者分组统计
                    author_stats = {}
                    for material in rejected_materials:
                        author = material.principal.name if material.principal else 'Unknown'
                        author_stats[author] = author_stats.get(author, 0) + 1

                    # 显示作者统计
                    st.markdown("**按作者统计审核失败数量:**")
                    for author, count in sorted(author_stats.items(), key=lambda x: x[1], reverse=True):
                        st.write(f"- {author}: {count} 个")
                else:
                    st.success("✅ 当前时间范围内无审核失败素材")

            elif failure_type == "⏰ 超时计划":
                # 使用原生SQL查询避免模型字段问题
                timeout_campaigns = db.execute(text("""
                    SELECT c.campaign_id_qc, c.status, c.created_at, c.appeal_attempt_count, a.name as account_name
                    FROM campaigns c
                    LEFT JOIN ad_accounts a ON c.account_id = a.id
                    WHERE c.status = 'APPEAL_TIMEOUT'
                    AND c.created_at >= :start_time AND c.created_at <= :end_time
                    ORDER BY c.created_at DESC
                """), {
                    'start_time': time_range['start'],
                    'end_time': time_range['end']
                }).fetchall()

                if timeout_campaigns:
                    st.write(f"找到 {len(timeout_campaigns)} 个超时计划:")

                    timeout_data = []
                    for campaign in timeout_campaigns:
                        timeout_data.append({
                            '计划ID': campaign.campaign_id_qc,
                            '账户': campaign.account_name or 'N/A',
                            '创建时间': campaign.created_at.strftime('%Y-%m-%d %H:%M') if campaign.created_at else 'N/A',
                            '申诉次数': campaign.appeal_attempt_count or 0
                        })

                    df = pd.DataFrame(timeout_data)
                    st.dataframe(df, use_container_width=True)
                else:
                    st.success("✅ 当前时间范围内无超时计划")

    except Exception as e:
        st.error(f"失败分析失败: {e}")

def render_author_productivity_stats(db, time_range: Dict):
    """作者产出统计"""
    st.markdown("#### 👤 作者产出统计")

    try:
        # 按作者统计素材数量
        author_stats = db.query(
            Principal.name,
            func.count(LocalCreative.id).label('total_materials'),
            func.sum(func.case([(LocalCreative.status == 'approved', 1)], else_=0)).label('approved_materials'),
            func.sum(func.case([(LocalCreative.status == 'rejected', 1)], else_=0)).label('rejected_materials')
        ).join(
            LocalCreative, Principal.id == LocalCreative.principal_id
        ).filter(
            LocalCreative.created_at >= time_range['start'],
            LocalCreative.created_at <= time_range['end']
        ).group_by(Principal.name).all()

        if author_stats:
            productivity_data = []
            for stat in author_stats:
                success_rate = (stat.approved_materials / stat.total_materials * 100) if stat.total_materials > 0 else 0
                productivity_data.append({
                    '作者': stat.name,
                    '总素材数': stat.total_materials,
                    '通过数量': stat.approved_materials,
                    '拒绝数量': stat.rejected_materials,
                    '通过率(%)': f"{success_rate:.1f}%"
                })

            df = pd.DataFrame(productivity_data)
            df = df.sort_values('总素材数', ascending=False)
            st.dataframe(df, use_container_width=True)

            # 可视化图表
            fig = px.bar(
                df.head(10),
                x='作者',
                y=['通过数量', '拒绝数量'],
                title="Top 10 作者素材产出对比",
                barmode='stack'
            )
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("📭 当前时间范围内无作者数据")

    except Exception as e:
        st.error(f"获取作者产出统计失败: {e}")

if __name__ == "__main__":
    render_enhanced_material_analytics_page()
