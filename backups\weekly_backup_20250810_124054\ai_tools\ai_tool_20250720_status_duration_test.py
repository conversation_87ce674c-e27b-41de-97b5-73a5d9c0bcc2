#!/usr/bin/env python3
"""
状态停滞时长功能测试脚本
验证新增的状态停滞时长统计和显示功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text
import pandas as pd
from datetime import datetime, timedelta

def test_status_duration_stats():
    """测试状态停滞时长统计"""
    print("🔍 状态停滞时长功能测试")
    print("=" * 50)
    
    with database_session() as db:
        # 1. 测试实时处理数据查询（包含停滞时长）
        print("\n📊 测试实时处理数据查询...")
        realtime_query = text("""
            SELECT
                SUM(CASE WHEN lc.status IN ('pending_upload', 'creating_plan', 'testing_pending_review') THEN 1 ELSE 0 END) as processing_materials,
                SUM(CASE WHEN lc.status = 'upload_failed' THEN 1 ELSE 0 END) as failed_materials,
                (SELECT COUNT(*) FROM campaigns c JOIN ad_accounts aa ON c.account_id = aa.id
                 WHERE c.status = 'AUDITING' AND aa.account_type = 'TEST') as auditing_campaigns,
                (SELECT COUNT(*) FROM campaigns c JOIN ad_accounts aa ON c.account_id = aa.id
                 WHERE c.status = 'APPEAL_TIMEOUT' AND aa.account_type = 'TEST') as timeout_campaigns,
                -- 状态停滞时长统计
                AVG(CASE WHEN lc.status = 'testing_pending_review' 
                    THEN EXTRACT(EPOCH FROM (NOW() - lc.updated_at))/3600 ELSE NULL END) as avg_review_hours,
                AVG(CASE WHEN lc.status = 'creating_plan' 
                    THEN EXTRACT(EPOCH FROM (NOW() - lc.updated_at))/60 ELSE NULL END) as avg_creating_minutes,
                AVG(CASE WHEN lc.status = 'uploaded_pending_plan' 
                    THEN EXTRACT(EPOCH FROM (NOW() - lc.updated_at))/60 ELSE NULL END) as avg_pending_minutes
            FROM local_creatives lc
            JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
            WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                AND lc.created_at >= NOW() - INTERVAL '24 hours'
        """)
        
        realtime_result = db.execute(realtime_query).fetchone()
        
        print(f"  处理中素材: {realtime_result.processing_materials or 0}")
        print(f"  失败素材: {realtime_result.failed_materials or 0}")
        print(f"  审核中计划: {realtime_result.auditing_campaigns or 0}")
        print(f"  超时计划: {realtime_result.timeout_campaigns or 0}")
        
        if realtime_result.avg_review_hours:
            print(f"  ✅ 平均审核等待: {realtime_result.avg_review_hours:.1f}小时")
        else:
            print(f"  📭 平均审核等待: 无数据")
            
        if realtime_result.avg_creating_minutes:
            print(f"  ✅ 平均创建计划: {realtime_result.avg_creating_minutes:.1f}分钟")
        else:
            print(f"  📭 平均创建计划: 无数据")
            
        if realtime_result.avg_pending_minutes:
            print(f"  ✅ 平均待建计划: {realtime_result.avg_pending_minutes:.1f}分钟")
        else:
            print(f"  📭 平均待建计划: 无数据")
        
        # 2. 测试详细状态停滞分析
        print(f"\n🔍 详细状态停滞分析...")
        detail_query = text("""
            SELECT 
                lc.status,
                COUNT(*) as count,
                AVG(EXTRACT(EPOCH FROM (NOW() - lc.updated_at))/3600) as avg_hours,
                MIN(EXTRACT(EPOCH FROM (NOW() - lc.updated_at))/3600) as min_hours,
                MAX(EXTRACT(EPOCH FROM (NOW() - lc.updated_at))/3600) as max_hours
            FROM local_creatives lc
            JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
            WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                AND lc.status IN ('testing_pending_review', 'creating_plan', 'uploaded_pending_plan')
            GROUP BY lc.status
            ORDER BY count DESC
        """)
        
        detail_results = db.execute(detail_query).fetchall()
        
        status_map = {
            'testing_pending_review': '测试待审核',
            'creating_plan': '创建计划中',
            'uploaded_pending_plan': '已上传待建计划'
        }
        
        for row in detail_results:
            status_cn = status_map.get(row.status, row.status)
            print(f"  {status_cn}:")
            print(f"    数量: {row.count}")
            print(f"    平均停滞: {row.avg_hours:.1f}小时")
            print(f"    最短停滞: {row.min_hours:.1f}小时")
            print(f"    最长停滞: {row.max_hours:.1f}小时")
            
            # 预警检查
            if row.status == 'testing_pending_review' and row.max_hours > 24:
                print(f"    🔴 预警: 有素材审核超过24小时")
            elif row.status == 'creating_plan' and row.max_hours > 1:
                print(f"    🟡 预警: 有素材创建计划超过1小时")
            elif row.status == 'uploaded_pending_plan' and row.max_hours > 2:
                print(f"    🟡 预警: 有素材待建计划超过2小时")
            else:
                print(f"    ✅ 状态正常")
        
        # 3. 测试搜索结果中的停滞时长显示
        print(f"\n🔍 测试搜索结果停滞时长...")
        search_query = text("""
            SELECT
                lc.id,
                lc.filename,
                lc.status,
                lc.created_at,
                lc.updated_at,
                EXTRACT(EPOCH FROM (NOW() - lc.updated_at))/3600 as hours_since_update
            FROM local_creatives lc
            JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
            WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                AND lc.status IN ('testing_pending_review', 'creating_plan', 'uploaded_pending_plan')
            ORDER BY hours_since_update DESC
            LIMIT 5
        """)
        
        search_results = db.execute(search_query).fetchall()
        
        for result in search_results:
            filename = result.filename or f"ID: {result.id}"
            status_cn = status_map.get(result.status, result.status)
            
            # 格式化停滞时长
            if result.hours_since_update < 1:
                duration_str = f"{result.hours_since_update * 60:.0f}分钟"
            elif result.hours_since_update < 24:
                duration_str = f"{result.hours_since_update:.1f}小时"
            else:
                duration_str = f"{result.hours_since_update / 24:.1f}天"
            
            # 状态颜色判断
            if result.status == 'testing_pending_review' and result.hours_since_update > 12:
                color = "🔴"
                note = "(审核时间较长)"
            elif result.status == 'creating_plan' and result.hours_since_update > 0.5:
                color = "🟡"
                note = "(创建时间较长)"
            else:
                color = "⏱️"
                note = ""
            
            print(f"  📁 {filename}")
            print(f"    状态: {status_cn}")
            print(f"    停滞时长: {color} {duration_str} {note}")
        
        print(f"\n🎉 状态停滞时长功能测试完成!")
        print(f"✅ 实时统计查询正常")
        print(f"✅ 详细分析查询正常")
        print(f"✅ 搜索结果显示正常")
        print(f"✅ 预警机制工作正常")

if __name__ == "__main__":
    test_status_duration_stats()
