#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 智能状态修正，避免重复处理
清理条件: 成为状态管理工具，长期保留
"""

import os
import sys

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger


class SmartStatusCorrector:
    """智能状态修正器"""
    
    def run_smart_correction(self):
        """运行智能状态修正"""
        logger.info("🧠 智能状态修正")
        logger.info("="*100)
        
        # 1. 分析当前状态分布
        status_analysis = self._analyze_current_status()
        
        # 2. 识别需要修正的素材
        correction_plan = self._identify_correction_needs(status_analysis)
        
        # 3. 执行智能修正
        correction_success = self._execute_smart_correction(correction_plan)
        
        # 4. 验证修正结果
        verification_success = self._verify_correction_results()
        
        # 5. 生成修正报告
        self._generate_correction_report({
            'status_analysis': status_analysis,
            'correction_plan': correction_plan,
            'correction_success': correction_success,
            'verification_success': verification_success
        })
    
    def _analyze_current_status(self):
        """分析当前状态分布"""
        logger.info("📊 分析当前状态分布...")
        
        try:
            from qianchuan_aw.database.database import SessionLocal
            from qianchuan_aw.database.models import LocalCreative, Principal, PlatformCreative, Campaign
            
            with SessionLocal() as db:
                # 检查所有缇萃百货素材的状态
                status_distribution = db.query(
                    LocalCreative.status,
                    db.func.count(LocalCreative.id).label('count')
                ).join(Principal).filter(
                    Principal.name == '缇萃百货'
                ).group_by(LocalCreative.status).all()
                
                logger.info("📋 当前状态分布:")
                for status, count in status_distribution:
                    logger.info(f"   {status}: {count} 个")
                
                # 检查pending_upload素材的详情
                pending_upload_details = db.query(LocalCreative).join(Principal).filter(
                    Principal.name == '缇萃百货',
                    LocalCreative.status == 'pending_upload'
                ).all()
                
                # 检查这些素材是否有计划记录
                pending_with_campaigns = []
                pending_without_campaigns = []
                
                for material in pending_upload_details:
                    # 检查是否有PlatformCreative记录
                    pc_exists = db.query(PlatformCreative).filter(
                        PlatformCreative.local_creative_id == material.id
                    ).first()
                    
                    if pc_exists:
                        # 检查是否有Campaign记录
                        campaign_exists = db.query(Campaign).join(
                            db.text('campaign_platform_creative_association cpca ON campaigns.id = cpca.campaign_id')
                        ).join(
                            PlatformCreative, db.text('cpca.platform_creative_id = platform_creatives.id')
                        ).filter(
                            PlatformCreative.local_creative_id == material.id
                        ).first()
                        
                        if campaign_exists:
                            pending_with_campaigns.append(material)
                        else:
                            pending_without_campaigns.append(material)
                    else:
                        pending_without_campaigns.append(material)
                
                logger.info(f"\n📋 pending_upload素材分析:")
                logger.info(f"   有计划记录: {len(pending_with_campaigns)} 个")
                logger.info(f"   无计划记录: {len(pending_without_campaigns)} 个")
                
                return {
                    'status_distribution': dict(status_distribution),
                    'pending_with_campaigns': pending_with_campaigns,
                    'pending_without_campaigns': pending_without_campaigns
                }
                
        except Exception as e:
            logger.error(f"❌ 分析当前状态失败: {e}")
            return {}
    
    def _identify_correction_needs(self, status_analysis):
        """识别需要修正的素材"""
        logger.info("🔍 识别需要修正的素材...")
        
        pending_with_campaigns = status_analysis.get('pending_with_campaigns', [])
        pending_without_campaigns = status_analysis.get('pending_without_campaigns', [])
        
        correction_plan = {
            'to_testing_pending_review': [],  # 需要修正为testing_pending_review
            'keep_pending_upload': [],        # 保持pending_upload状态
            'need_investigation': []          # 需要进一步调查
        }
        
        # 有计划记录的素材应该修正状态
        for material in pending_with_campaigns:
            correction_plan['to_testing_pending_review'].append(material)
            logger.info(f"   📝 素材 {material.id} 有计划记录，应修正为testing_pending_review")
        
        # 无计划记录的素材保持pending_upload
        for material in pending_without_campaigns:
            correction_plan['keep_pending_upload'].append(material)
            logger.info(f"   📝 素材 {material.id} 无计划记录，保持pending_upload")
        
        logger.info(f"\n📋 修正计划:")
        logger.info(f"   修正为testing_pending_review: {len(correction_plan['to_testing_pending_review'])} 个")
        logger.info(f"   保持pending_upload: {len(correction_plan['keep_pending_upload'])} 个")
        
        return correction_plan
    
    def _execute_smart_correction(self, correction_plan):
        """执行智能修正"""
        logger.info("🔧 执行智能状态修正...")
        
        try:
            from qianchuan_aw.database.database import SessionLocal
            from qianchuan_aw.utils.workflow_status import WorkflowStatus
            
            with SessionLocal() as db:
                correction_count = 0
                
                # 修正有计划记录的素材状态
                for material in correction_plan['to_testing_pending_review']:
                    try:
                        material.status = WorkflowStatus.TESTING_PENDING_REVIEW.value
                        correction_count += 1
                        logger.info(f"   ✅ 修正素材 {material.id} 状态为 testing_pending_review")
                    except Exception as e:
                        logger.error(f"   ❌ 修正素材 {material.id} 失败: {e}")
                
                # 提交修正
                db.commit()
                
                logger.success(f"✅ 成功修正 {correction_count} 个素材状态")
                
                return correction_count > 0
                
        except Exception as e:
            logger.error(f"❌ 执行智能修正失败: {e}")
            return False
    
    def _verify_correction_results(self):
        """验证修正结果"""
        logger.info("🔍 验证修正结果...")
        
        try:
            from qianchuan_aw.database.database import SessionLocal
            from qianchuan_aw.database.models import LocalCreative, Principal
            
            with SessionLocal() as db:
                # 检查修正后的状态分布
                new_status_distribution = db.query(
                    LocalCreative.status,
                    db.func.count(LocalCreative.id).label('count')
                ).join(Principal).filter(
                    Principal.name == '缇萃百货'
                ).group_by(LocalCreative.status).all()
                
                logger.info(f"📊 修正后状态分布:")
                for status, count in new_status_distribution:
                    logger.info(f"   {status}: {count} 个")
                
                # 检查pending_upload数量
                pending_upload_count = next(
                    (count for status, count in new_status_distribution if status == 'pending_upload'),
                    0
                )
                
                if pending_upload_count <= 5:
                    logger.success(f"✅ pending_upload数量合理: {pending_upload_count} 个")
                    return True
                else:
                    logger.warning(f"⚠️ pending_upload数量较多: {pending_upload_count} 个")
                    return False
                
        except Exception as e:
            logger.error(f"❌ 验证修正结果失败: {e}")
            return False
    
    def _generate_correction_report(self, results):
        """生成修正报告"""
        logger.info("\n📋 智能状态修正报告")
        logger.info("="*100)
        
        status_analysis = results['status_analysis']
        correction_plan = results['correction_plan']
        correction_success = results['correction_success']
        verification_success = results['verification_success']
        
        # 状态分析
        status_dist = status_analysis.get('status_distribution', {})
        logger.info(f"📊 状态分析: 发现 {sum(status_dist.values())} 个素材")
        
        # 修正计划
        to_correct = len(correction_plan.get('to_testing_pending_review', []))
        to_keep = len(correction_plan.get('keep_pending_upload', []))
        logger.info(f"📝 修正计划: {to_correct} 个需修正, {to_keep} 个保持现状")
        
        # 修正结果
        if correction_success:
            logger.success("✅ 智能修正: 执行成功")
        else:
            logger.error("❌ 智能修正: 执行失败")
        
        # 验证结果
        if verification_success:
            logger.success("✅ 验证结果: 状态分布合理")
        else:
            logger.warning("⚠️ 验证结果: 可能需要进一步处理")
        
        # 总体评估
        success_count = sum([
            len(status_analysis) > 0,  # 成功分析状态
            len(correction_plan) > 0,  # 制定了修正计划
            correction_success,        # 修正成功
            verification_success       # 验证通过
        ])
        
        logger.info(f"\n🎯 智能修正成功率: {success_count}/4")
        
        if success_count >= 3:
            logger.success("🎊 智能状态修正成功")
            logger.success("💡 系统状态现在更加一致和准确")
            
            logger.info("\n🚀 下一步行动:")
            logger.info("   1. pending_upload素材将通过修复后的健康检查重新上传")
            logger.info("   2. uploaded_pending_plan素材将正确分配到健康账户")
            logger.info("   3. 避免重复创建已存在的计划")
            
            return True
        else:
            logger.error("❌ 智能状态修正失败")
            logger.error("🔧 需要手动检查和修复")
            return False


def main():
    """主函数"""
    corrector = SmartStatusCorrector()
    
    logger.info("🚀 启动智能状态修正")
    logger.info("🎯 目标：避免重复处理，确保状态一致性")
    
    success = corrector.run_smart_correction()
    
    if success:
        logger.success("🎊 智能状态修正完成")
        logger.success("💡 建议：观察系统处理剩余素材的效果")
        return 0
    else:
        logger.error("❌ 智能状态修正失败")
        logger.error("🔧 建议：检查错误并手动修复")
        return 1


if __name__ == "__main__":
    exit(main())
