"""
千川自动化工作流弹性恢复系统
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 构建高可靠性、可恢复的工作流系统，确保任何时候停止后都能完整续接
清理条件: 永不删除，持续维护优化
"""

import os
import sys
import time
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import LocalCreative, PlatformCreative, Campaign
from qianchuan_aw.utils.workflow_status import WorkflowStatus


class WorkflowStage(Enum):
    """工作流阶段枚举"""
    UPLOAD = "upload"
    PLAN_CREATION = "plan_creation"
    APPEAL_SUBMISSION = "appeal_submission"
    HARVEST = "harvest"


@dataclass
class WorkflowCheckpoint:
    """工作流检查点"""
    stage: WorkflowStage
    total_items: int
    completed_items: int
    failed_items: int
    pending_items: int
    completion_rate: float
    last_updated: datetime
    issues: List[str]


class WorkflowResilienceSystem:
    """工作流弹性恢复系统"""
    
    def __init__(self, app_settings: Dict[str, Any]):
        self.app_settings = app_settings
        self.checkpoint_file = "ai_temp/workflow_checkpoint.json"
        self.recovery_log = "ai_temp/workflow_recovery.log"
        
    def create_system_checkpoint(self) -> Dict[str, WorkflowCheckpoint]:
        """创建系统检查点"""
        logger.info("🔍 创建工作流系统检查点...")
        
        checkpoints = {}
        
        with database_session() as db:
            # 检查点1: 上传阶段
            upload_checkpoint = self._check_upload_stage(db)
            checkpoints['upload'] = upload_checkpoint
            
            # 检查点2: 计划创建阶段
            plan_checkpoint = self._check_plan_creation_stage(db)
            checkpoints['plan_creation'] = plan_checkpoint
            
            # 检查点3: 提审阶段
            appeal_checkpoint = self._check_appeal_stage(db)
            checkpoints['appeal_submission'] = appeal_checkpoint
            
            # 检查点4: 收割阶段
            harvest_checkpoint = self._check_harvest_stage(db)
            checkpoints['harvest'] = harvest_checkpoint
        
        # 保存检查点到文件
        self._save_checkpoint(checkpoints)
        
        return checkpoints
    
    def _check_upload_stage(self, db) -> WorkflowCheckpoint:
        """检查上传阶段"""
        # 统计今天的素材上传情况
        today_creatives = db.query(LocalCreative).filter(
            LocalCreative.created_at >= datetime.now().date()
        ).all()
        
        total = len(today_creatives)
        completed = len([c for c in today_creatives if c.status in [
            WorkflowStatus.UPLOADED_PENDING_PLAN.value,
            WorkflowStatus.APPROVED.value
        ]])
        failed = len([c for c in today_creatives if c.status in [
            WorkflowStatus.UPLOAD_FAILED.value,
            'rejected'
        ]])
        pending = len([c for c in today_creatives if c.status in [
            'new', 'processing'
        ]])
        
        issues = []
        if pending > 0:
            issues.append(f"{pending}个素材待上传")
        if failed > 0:
            issues.append(f"{failed}个素材上传失败")
        
        return WorkflowCheckpoint(
            stage=WorkflowStage.UPLOAD,
            total_items=total,
            completed_items=completed,
            failed_items=failed,
            pending_items=pending,
            completion_rate=completed / total * 100 if total > 0 else 0,
            last_updated=datetime.now(),
            issues=issues
        )
    
    def _check_plan_creation_stage(self, db) -> WorkflowCheckpoint:
        """检查计划创建阶段"""
        # 统计需要创建计划的素材
        pending_plan_creatives = db.query(LocalCreative).filter(
            LocalCreative.status == WorkflowStatus.UPLOADED_PENDING_PLAN.value,
            LocalCreative.created_at >= datetime.now().date()
        ).all()
        
        # 统计已创建计划的素材
        from qianchuan_aw.database.models import campaign_platform_creative_association
        created_plans = db.query(Campaign).join(
            campaign_platform_creative_association
        ).join(
            PlatformCreative
        ).join(
            LocalCreative
        ).filter(
            LocalCreative.created_at >= datetime.now().date()
        ).all()
        
        total = len(pending_plan_creatives)
        completed = len(created_plans)
        pending = total - completed
        
        issues = []
        if pending > 0:
            issues.append(f"{pending}个素材待创建计划")
        
        return WorkflowCheckpoint(
            stage=WorkflowStage.PLAN_CREATION,
            total_items=total,
            completed_items=completed,
            failed_items=0,
            pending_items=pending,
            completion_rate=completed / total * 100 if total > 0 else 100,
            last_updated=datetime.now(),
            issues=issues
        )
    
    def _check_appeal_stage(self, db) -> WorkflowCheckpoint:
        """检查提审阶段"""
        # 统计今天创建的计划
        from qianchuan_aw.database.models import campaign_platform_creative_association
        today_campaigns = db.query(Campaign).join(
            campaign_platform_creative_association
        ).join(
            PlatformCreative
        ).join(
            LocalCreative
        ).filter(
            LocalCreative.created_at >= datetime.now().date()
        ).all()
        
        total = len(set(c.campaign_id_qc for c in today_campaigns))
        completed = len([c for c in today_campaigns if c.appeal_status == 'appeal_approved'])
        failed = len([c for c in today_campaigns if c.appeal_status == 'submission_failed'])
        pending = total - completed - failed
        
        issues = []
        if failed > 0:
            issues.append(f"{failed}个计划提审失败")
        if pending > 0:
            issues.append(f"{pending}个计划待提审")
        
        return WorkflowCheckpoint(
            stage=WorkflowStage.APPEAL_SUBMISSION,
            total_items=total,
            completed_items=completed,
            failed_items=failed,
            pending_items=pending,
            completion_rate=completed / total * 100 if total > 0 else 0,
            last_updated=datetime.now(),
            issues=issues
        )
    
    def _check_harvest_stage(self, db) -> WorkflowCheckpoint:
        """检查收割阶段"""
        # 统计已通过审核的计划
        from qianchuan_aw.database.models import campaign_platform_creative_association
        approved_campaigns = db.query(Campaign).join(
            campaign_platform_creative_association
        ).join(
            PlatformCreative
        ).join(
            LocalCreative
        ).filter(
            LocalCreative.created_at >= datetime.now().date(),
            Campaign.appeal_status == 'appeal_approved'
        ).all()
        
        # 统计已收割的素材
        harvested_creatives = db.query(LocalCreative).filter(
            LocalCreative.created_at >= datetime.now().date(),
            LocalCreative.status == WorkflowStatus.APPROVED.value
        ).all()
        
        total = len(approved_campaigns)
        completed = len(harvested_creatives)
        pending = total - completed
        
        issues = []
        if pending > 0:
            issues.append(f"{pending}个素材待收割")
        
        return WorkflowCheckpoint(
            stage=WorkflowStage.HARVEST,
            total_items=total,
            completed_items=completed,
            failed_items=0,
            pending_items=pending,
            completion_rate=completed / total * 100 if total > 0 else 100,
            last_updated=datetime.now(),
            issues=issues
        )
    
    def _save_checkpoint(self, checkpoints: Dict[str, WorkflowCheckpoint]):
        """保存检查点到文件"""
        os.makedirs(os.path.dirname(self.checkpoint_file), exist_ok=True)
        
        checkpoint_data = {}
        for stage, checkpoint in checkpoints.items():
            checkpoint_data[stage] = {
                'stage': checkpoint.stage.value,
                'total_items': checkpoint.total_items,
                'completed_items': checkpoint.completed_items,
                'failed_items': checkpoint.failed_items,
                'pending_items': checkpoint.pending_items,
                'completion_rate': checkpoint.completion_rate,
                'last_updated': checkpoint.last_updated.isoformat(),
                'issues': checkpoint.issues
            }
        
        with open(self.checkpoint_file, 'w', encoding='utf-8') as f:
            json.dump(checkpoint_data, f, ensure_ascii=False, indent=2)
    
    def load_checkpoint(self) -> Optional[Dict[str, WorkflowCheckpoint]]:
        """加载检查点"""
        if not os.path.exists(self.checkpoint_file):
            return None
        
        try:
            with open(self.checkpoint_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            checkpoints = {}
            for stage, checkpoint_data in data.items():
                checkpoints[stage] = WorkflowCheckpoint(
                    stage=WorkflowStage(checkpoint_data['stage']),
                    total_items=checkpoint_data['total_items'],
                    completed_items=checkpoint_data['completed_items'],
                    failed_items=checkpoint_data['failed_items'],
                    pending_items=checkpoint_data['pending_items'],
                    completion_rate=checkpoint_data['completion_rate'],
                    last_updated=datetime.fromisoformat(checkpoint_data['last_updated']),
                    issues=checkpoint_data['issues']
                )
            
            return checkpoints
        except Exception as e:
            logger.error(f"加载检查点失败: {e}")
            return None
    
    def generate_recovery_plan(self, checkpoints: Dict[str, WorkflowCheckpoint]) -> List[Dict[str, Any]]:
        """生成恢复计划"""
        logger.info("📋 生成工作流恢复计划...")
        
        recovery_actions = []
        
        # 按优先级排序恢复动作
        for stage_name in ['upload', 'plan_creation', 'appeal_submission', 'harvest']:
            checkpoint = checkpoints.get(stage_name)
            if not checkpoint or checkpoint.pending_items == 0:
                continue
            
            if stage_name == 'upload':
                recovery_actions.append({
                    'priority': 1,
                    'stage': 'upload',
                    'action': 'resume_upload_tasks',
                    'description': f'恢复{checkpoint.pending_items}个素材的上传任务',
                    'items_count': checkpoint.pending_items,
                    'estimated_time': checkpoint.pending_items * 2  # 每个素材预计2分钟
                })
            
            elif stage_name == 'plan_creation':
                recovery_actions.append({
                    'priority': 2,
                    'stage': 'plan_creation',
                    'action': 'resume_plan_creation',
                    'description': f'为{checkpoint.pending_items}个素材创建计划',
                    'items_count': checkpoint.pending_items,
                    'estimated_time': checkpoint.pending_items * 1  # 每个素材预计1分钟
                })
            
            elif stage_name == 'appeal_submission':
                recovery_actions.append({
                    'priority': 3,
                    'stage': 'appeal_submission',
                    'action': 'resume_appeal_submission',
                    'description': f'重新提审{checkpoint.pending_items}个计划',
                    'items_count': checkpoint.pending_items,
                    'estimated_time': checkpoint.pending_items * 0.5  # 每个计划预计30秒
                })
            
            elif stage_name == 'harvest':
                recovery_actions.append({
                    'priority': 4,
                    'stage': 'harvest',
                    'action': 'resume_harvest',
                    'description': f'收割{checkpoint.pending_items}个通过审核的素材',
                    'items_count': checkpoint.pending_items,
                    'estimated_time': checkpoint.pending_items * 0.5  # 每个素材预计30秒
                })
        
        return sorted(recovery_actions, key=lambda x: x['priority'])
    
    def print_system_status(self, checkpoints: Dict[str, WorkflowCheckpoint]):
        """打印系统状态"""
        logger.info("\n" + "="*80)
        logger.info("🔍 千川自动化工作流系统状态报告")
        logger.info("="*80)
        
        total_completion = 0
        stage_count = 0
        
        for stage_name, checkpoint in checkpoints.items():
            stage_count += 1
            total_completion += checkpoint.completion_rate
            
            status_icon = "✅" if checkpoint.completion_rate == 100 else "⚠️" if checkpoint.completion_rate > 0 else "❌"
            
            logger.info(f"\n{status_icon} {checkpoint.stage.value.upper()} 阶段:")
            logger.info(f"   完成率: {checkpoint.completion_rate:.1f}%")
            logger.info(f"   总数: {checkpoint.total_items}")
            logger.info(f"   已完成: {checkpoint.completed_items}")
            logger.info(f"   待处理: {checkpoint.pending_items}")
            logger.info(f"   失败: {checkpoint.failed_items}")
            
            if checkpoint.issues:
                logger.warning(f"   问题: {', '.join(checkpoint.issues)}")
        
        overall_completion = total_completion / stage_count if stage_count > 0 else 0
        logger.info(f"\n🎯 系统整体完成率: {overall_completion:.1f}%")
        logger.info("="*80)


def main():
    """主函数"""
    logger.info("🚀 启动工作流弹性恢复系统...")
    
    # 模拟app_settings
    app_settings = {}
    
    # 创建恢复系统
    recovery_system = WorkflowResilienceSystem(app_settings)
    
    # 创建系统检查点
    checkpoints = recovery_system.create_system_checkpoint()
    
    # 打印系统状态
    recovery_system.print_system_status(checkpoints)
    
    # 生成恢复计划
    recovery_plan = recovery_system.generate_recovery_plan(checkpoints)
    
    if recovery_plan:
        logger.info("\n📋 工作流恢复计划:")
        for i, action in enumerate(recovery_plan, 1):
            logger.info(f"{i}. {action['description']} (预计{action['estimated_time']}分钟)")
    else:
        logger.success("🎉 所有工作流阶段都已完成，无需恢复！")


if __name__ == "__main__":
    main()
