#!/usr/bin/env python3
"""
处理506个new状态素材中的502个缺失文件
制定恢复策略和数据库清理方案
"""

import sys
import os
from datetime import datetime
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text

class MissingFilesHandler:
    """缺失文件处理器"""
    
    def __init__(self):
        logger.critical("🔍 处理506个new状态素材中的502个缺失文件")
        logger.critical("=" * 60)
    
    def analyze_missing_files_pattern(self):
        """分析缺失文件的模式"""
        logger.critical("📊 分析缺失文件的模式")
        logger.critical("=" * 60)
        
        try:
            with database_session() as db:
                # 查询缺失文件的详细信息
                missing_files_query = text("""
                    SELECT 
                        lc.id,
                        lc.filename,
                        lc.file_path,
                        lc.created_at,
                        lc.updated_at,
                        lc.status,
                        p.name as principal_name
                    FROM local_creatives lc
                    LEFT JOIN principals p ON lc.principal_id = p.id
                    WHERE lc.status = 'new'
                    ORDER BY lc.created_at DESC
                """)
                
                results = db.execute(missing_files_query).fetchall()
                
                logger.critical(f"📊 分析 {len(results)} 个new状态素材")
                
                # 分析文件路径模式
                path_patterns = {}
                existing_files = []
                missing_files = []
                
                for result in results:
                    file_path = result.file_path
                    if file_path and Path(file_path).exists():
                        existing_files.append(result)
                    else:
                        missing_files.append(result)
                        
                        # 分析路径模式
                        if file_path:
                            parent_dir = str(Path(file_path).parent)
                            path_patterns[parent_dir] = path_patterns.get(parent_dir, 0) + 1
                
                logger.critical(f"📊 文件状态分析:")
                logger.critical(f"  存在文件: {len(existing_files)} 个")
                logger.critical(f"  缺失文件: {len(missing_files)} 个")
                
                logger.critical(f"\n📋 缺失文件路径分布:")
                for path, count in sorted(path_patterns.items(), key=lambda x: x[1], reverse=True)[:10]:
                    logger.critical(f"  {path}: {count} 个")
                
                # 分析创建时间分布
                from collections import defaultdict
                date_stats = defaultdict(int)
                for result in missing_files:
                    if result.created_at:
                        date_key = result.created_at.date()
                        date_stats[date_key] += 1
                
                logger.critical(f"\n📋 缺失文件创建时间分布:")
                for date_key in sorted(date_stats.keys(), reverse=True)[:7]:
                    logger.critical(f"  {date_key}: {date_stats[date_key]} 个")
                
                return {
                    'existing_files': existing_files,
                    'missing_files': missing_files,
                    'path_patterns': path_patterns,
                    'date_stats': dict(date_stats)
                }
                
        except Exception as e:
            logger.error(f"❌ 分析缺失文件模式失败: {e}")
            return None
    
    def search_for_moved_files(self, missing_files):
        """搜索可能被移动的文件"""
        logger.critical("\n🔍 搜索可能被移动的文件")
        logger.critical("=" * 60)
        
        # 定义可能的搜索目录
        search_directories = [
            "G:\\workflow_assets",
            "G:\\workflow_assets\\01_materials_to_process",
            "G:\\workflow_assets\\02_materials_processing",
            "G:\\workflow_assets\\03_materials_processed",
            "G:\\workflow_assets\\00_materials_archived",
            "D:\\Project\\qianchuangzl\\materials",
            "D:\\Project\\qianchuangzl\\temp"
        ]
        
        found_files = []
        search_results = {}
        
        logger.critical(f"📊 搜索 {len(missing_files)} 个缺失文件")
        
        for i, missing_file in enumerate(missing_files[:50]):  # 只搜索前50个，避免耗时过长
            filename = missing_file.filename
            logger.critical(f"  搜索 {i+1}/50: {filename}")
            
            for search_dir in search_directories:
                if not Path(search_dir).exists():
                    continue
                
                try:
                    # 在目录中搜索同名文件
                    search_path = Path(search_dir)
                    matches = list(search_path.rglob(filename))
                    
                    if matches:
                        for match in matches:
                            found_files.append({
                                'material_id': missing_file.id,
                                'filename': filename,
                                'original_path': missing_file.file_path,
                                'found_path': str(match),
                                'found_size': match.stat().st_size,
                                'found_mtime': datetime.fromtimestamp(match.stat().st_mtime)
                            })
                            logger.critical(f"    ✅ 找到: {match}")
                            break
                    
                except Exception as e:
                    logger.warning(f"    搜索目录 {search_dir} 失败: {e}")
        
        search_results = {
            'found_files': found_files,
            'search_count': min(50, len(missing_files)),
            'found_count': len(found_files)
        }
        
        logger.critical(f"\n📊 搜索结果:")
        logger.critical(f"  搜索文件: {search_results['search_count']} 个")
        logger.critical(f"  找到文件: {search_results['found_count']} 个")
        logger.critical(f"  找到率: {search_results['found_count']/search_results['search_count']*100:.1f}%")
        
        return search_results
    
    def generate_recovery_strategies(self, analysis_result, search_result):
        """生成恢复策略"""
        logger.critical("\n📋 生成恢复策略")
        logger.critical("=" * 60)
        
        existing_files = analysis_result['existing_files']
        missing_files = analysis_result['missing_files']
        found_files = search_result['found_files']
        
        strategies = {
            'immediate_processing': [],  # 立即处理的文件
            'file_recovery': [],         # 需要恢复的文件
            'database_cleanup': [],      # 需要清理的数据库记录
            'manual_review': []          # 需要手动审查的文件
        }
        
        # 策略1: 立即处理存在的文件
        for file_info in existing_files:
            strategies['immediate_processing'].append({
                'material_id': file_info.id,
                'filename': file_info.filename,
                'file_path': file_info.file_path,
                'action': '移动到入库目录并重启工作流'
            })
        
        # 策略2: 恢复找到的文件
        for found_file in found_files:
            strategies['file_recovery'].append({
                'material_id': found_file['material_id'],
                'filename': found_file['filename'],
                'original_path': found_file['original_path'],
                'found_path': found_file['found_path'],
                'action': '更新数据库路径或移动文件'
            })
        
        # 策略3: 清理无法恢复的数据库记录
        found_material_ids = {f['material_id'] for f in found_files}
        existing_material_ids = {f.id for f in existing_files}
        
        for file_info in missing_files:
            if file_info.id not in found_material_ids and file_info.id not in existing_material_ids:
                strategies['database_cleanup'].append({
                    'material_id': file_info.id,
                    'filename': file_info.filename,
                    'file_path': file_info.file_path,
                    'created_at': file_info.created_at,
                    'action': '删除数据库记录或标记为已删除'
                })
        
        logger.critical(f"📊 恢复策略统计:")
        logger.critical(f"  立即处理: {len(strategies['immediate_processing'])} 个")
        logger.critical(f"  文件恢复: {len(strategies['file_recovery'])} 个")
        logger.critical(f"  数据库清理: {len(strategies['database_cleanup'])} 个")
        
        return strategies
    
    def create_recovery_scripts(self, strategies):
        """创建恢复脚本"""
        logger.critical("\n🔧 创建恢复脚本")
        logger.critical("=" * 60)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        scripts_dir = Path("ai_reports/material_recovery")
        scripts_dir.mkdir(parents=True, exist_ok=True)
        
        # 1. 立即处理脚本
        if strategies['immediate_processing']:
            immediate_script = '''@echo off
REM 立即处理4个存在的new状态素材文件
echo 开始处理存在的素材文件...

'''
            for item in strategies['immediate_processing']:
                source_path = item['file_path']
                filename = item['filename']
                immediate_script += f'''
REM 处理文件: {filename}
if exist "{source_path}" (
    move "{source_path}" "G:\\workflow_assets\\01_materials_to_process\\缇萃百货\\{filename}"
    echo 成功移动: {filename}
) else (
    echo 错误: 文件不存在 - {filename}
)
'''
            
            immediate_script += '''
echo 立即处理完成
pause
'''
            
            immediate_file = scripts_dir / f"immediate_processing_{timestamp}.bat"
            with open(immediate_file, 'w', encoding='gbk') as f:
                f.write(immediate_script)
            
            logger.critical(f"✅ 立即处理脚本: {immediate_file}")
        
        # 2. 文件恢复脚本
        if strategies['file_recovery']:
            recovery_script = '''@echo off
REM 恢复找到的文件
echo 开始恢复找到的文件...

'''
            for item in strategies['file_recovery']:
                found_path = item['found_path']
                filename = item['filename']
                recovery_script += f'''
REM 恢复文件: {filename}
if exist "{found_path}" (
    copy "{found_path}" "G:\\workflow_assets\\01_materials_to_process\\缇萃百货\\{filename}"
    echo 成功恢复: {filename}
) else (
    echo 错误: 找到的文件不存在 - {filename}
)
'''
            
            recovery_script += '''
echo 文件恢复完成
pause
'''
            
            recovery_file = scripts_dir / f"file_recovery_{timestamp}.bat"
            with open(recovery_file, 'w', encoding='gbk') as f:
                f.write(recovery_script)
            
            logger.critical(f"✅ 文件恢复脚本: {recovery_file}")
        
        # 3. 数据库清理SQL
        if strategies['database_cleanup']:
            cleanup_sql = '''-- 清理无法恢复的new状态素材记录
-- 这些文件已经不存在，需要清理数据库记录

BEGIN;

'''
            for item in strategies['database_cleanup']:
                material_id = item['material_id']
                filename = item['filename']
                cleanup_sql += f'''-- 清理素材: {filename} (ID: {material_id})
UPDATE local_creatives 
SET status = 'file_missing',
    updated_at = NOW()
WHERE id = {material_id};

'''
            
            cleanup_sql += '''
-- 提交事务
COMMIT;

-- 验证清理结果
SELECT 
    status,
    COUNT(*) as count
FROM local_creatives 
GROUP BY status
ORDER BY count DESC;
'''
            
            cleanup_file = scripts_dir / f"database_cleanup_{timestamp}.sql"
            with open(cleanup_file, 'w', encoding='utf-8') as f:
                f.write(cleanup_sql)
            
            logger.critical(f"✅ 数据库清理SQL: {cleanup_file}")
        
        return {
            'immediate_file': immediate_file if strategies['immediate_processing'] else None,
            'recovery_file': recovery_file if strategies['file_recovery'] else None,
            'cleanup_file': cleanup_file if strategies['database_cleanup'] else None
        }
    
    def generate_final_recommendations(self, strategies, scripts):
        """生成最终建议"""
        logger.critical("\n💡 生成最终建议")
        logger.critical("=" * 60)
        
        logger.critical("🎯 基于分析结果的最终建议:")
        
        logger.critical("\n📋 第一优先级 - 立即处理存在的文件:")
        if strategies['immediate_processing']:
            logger.critical(f"  ✅ {len(strategies['immediate_processing'])} 个文件可以立即处理")
            logger.critical(f"  🔧 执行脚本: {scripts.get('immediate_file', '无')}")
            logger.critical(f"  📊 这些文件确认没有创建过计划，可以安全入库")
        else:
            logger.critical(f"  ❌ 没有可以立即处理的文件")
        
        logger.critical("\n📋 第二优先级 - 恢复找到的文件:")
        if strategies['file_recovery']:
            logger.critical(f"  ✅ {len(strategies['file_recovery'])} 个文件可以恢复")
            logger.critical(f"  🔧 执行脚本: {scripts.get('recovery_file', '无')}")
            logger.critical(f"  📊 需要验证这些文件是否已经创建过计划")
        else:
            logger.critical(f"  ❌ 没有找到可以恢复的文件")
        
        logger.critical("\n📋 第三优先级 - 清理数据库记录:")
        if strategies['database_cleanup']:
            logger.critical(f"  ⚠️ {len(strategies['database_cleanup'])} 个记录需要清理")
            logger.critical(f"  🔧 执行SQL: {scripts.get('cleanup_file', '无')}")
            logger.critical(f"  📊 这些记录对应的文件已经不存在")
        
        logger.critical("\n🚀 推荐执行顺序:")
        logger.critical("  1. 立即处理存在的4个文件")
        logger.critical("  2. 尝试恢复找到的文件")
        logger.critical("  3. 清理无法恢复的数据库记录")
        logger.critical("  4. 重启工作流处理恢复的文件")
        
        logger.critical("\n⚠️ 重要提醒:")
        logger.critical("  - 执行前请备份数据库")
        logger.critical("  - 确保目标目录存在且有足够空间")
        logger.critical("  - 恢复的文件需要重新验证是否已创建计划")
        logger.critical("  - 严格遵循'测试阶段一视频一计划'规则")

def main():
    """主处理函数"""
    try:
        handler = MissingFilesHandler()
        
        # 1. 分析缺失文件模式
        analysis_result = handler.analyze_missing_files_pattern()
        if not analysis_result:
            return False
        
        # 2. 搜索可能被移动的文件
        search_result = handler.search_for_moved_files(analysis_result['missing_files'])
        
        # 3. 生成恢复策略
        strategies = handler.generate_recovery_strategies(analysis_result, search_result)
        
        # 4. 创建恢复脚本
        scripts = handler.create_recovery_scripts(strategies)
        
        # 5. 生成最终建议
        handler.generate_final_recommendations(strategies, scripts)
        
        logger.critical(f"\n🎉 缺失文件处理方案制定完成!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 处理失败: {e}")
        return False

if __name__ == "__main__":
    main()
