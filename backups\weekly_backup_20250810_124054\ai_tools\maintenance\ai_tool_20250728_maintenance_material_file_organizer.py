#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 整理千川自动化项目中'new'状态素材的文件路径，确保路径一致性
清理条件: 项目重构时可考虑删除
"""

import os
import sys
import shutil
import psycopg2
import yaml
from pathlib import Path
from datetime import datetime
from loguru import logger
import json

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def load_database_config():
    """加载数据库配置"""
    config_path = project_root / 'config' / 'settings.yml'
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config['database']['postgresql']

def get_database_connection():
    """获取数据库连接"""
    db_config = load_database_config()
    return psycopg2.connect(
        host=db_config['host'],
        port=db_config['port'],
        database=db_config['dbname'],
        user=db_config['user'],
        password=db_config['password']
    )

def create_backup_record(materials_to_move):
    """创建备份记录"""
    backup_file = project_root / 'ai_temp' / f'material_move_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
    backup_file.parent.mkdir(exist_ok=True)
    
    backup_data = {
        'timestamp': datetime.now().isoformat(),
        'total_materials': len(materials_to_move),
        'materials': []
    }
    
    for material in materials_to_move:
        backup_data['materials'].append({
            'id': material['id'],
            'filename': material['filename'],
            'original_path': material['file_path'],
            'target_path': material['target_path']
        })
    
    with open(backup_file, 'w', encoding='utf-8') as f:
        json.dump(backup_data, f, indent=2, ensure_ascii=False)
    
    logger.info(f"📋 备份记录已创建: {backup_file}")
    return backup_file

def get_materials_to_organize():
    """获取需要整理的素材列表"""
    conn = get_database_connection()
    cursor = conn.cursor()
    
    # 查询所有'new'状态的素材
    cursor.execute("""
        SELECT id, filename, file_path, created_at, updated_at
        FROM local_creatives 
        WHERE status = 'new'
        ORDER BY created_at DESC
    """)
    
    materials = []
    standard_dir = "G:/workflow_assets/01_materials_to_process/缇萃百货"
    
    for row in cursor.fetchall():
        material_id, filename, file_path, created_at, updated_at = row
        
        # 标准化路径格式
        normalized_path = file_path.replace('\\', '/')
        target_path = f"{standard_dir}/{filename}"
        
        # 检查是否需要移动
        if not normalized_path.startswith(standard_dir):
            materials.append({
                'id': material_id,
                'filename': filename,
                'file_path': file_path,
                'normalized_path': normalized_path,
                'target_path': target_path,
                'created_at': created_at,
                'updated_at': updated_at
            })
    
    cursor.close()
    conn.close()
    
    return materials

def ensure_target_directory():
    """确保目标目录存在"""
    target_dir = Path("G:/workflow_assets/01_materials_to_process/缇萃百货")
    target_dir.mkdir(parents=True, exist_ok=True)
    logger.info(f"📁 目标目录已确保存在: {target_dir}")
    return target_dir

def move_file_safely(source_path, target_path):
    """安全地移动文件"""
    source = Path(source_path)
    target = Path(target_path)
    
    # 检查源文件是否存在
    if not source.exists():
        logger.warning(f"⚠️ 源文件不存在: {source}")
        return False, "源文件不存在"
    
    # 检查目标文件是否已存在
    if target.exists():
        # 比较文件大小，如果相同则认为是重复文件
        if source.stat().st_size == target.stat().st_size:
            logger.info(f"📄 目标文件已存在且大小相同，跳过移动: {target.name}")
            return True, "文件已存在（相同大小）"
        else:
            # 文件名冲突但内容不同，添加时间戳
            timestamp = datetime.now().strftime("%H%M%S")
            name_parts = target.stem, timestamp, target.suffix
            new_target = target.parent / f"{name_parts[0]}_{name_parts[1]}{name_parts[2]}"
            target = new_target
            logger.warning(f"⚠️ 文件名冲突，重命名为: {target.name}")
    
    try:
        # 执行移动操作
        shutil.move(str(source), str(target))
        logger.success(f"✅ 文件移动成功: {source.name} -> {target}")
        return True, str(target)
    except Exception as e:
        logger.error(f"❌ 文件移动失败: {source.name} - {e}")
        return False, str(e)

def update_database_path(material_id, new_path):
    """更新数据库中的文件路径"""
    conn = get_database_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute("""
            UPDATE local_creatives 
            SET file_path = %s, updated_at = CURRENT_TIMESTAMP 
            WHERE id = %s
        """, (new_path, material_id))
        
        conn.commit()
        logger.success(f"✅ 数据库路径已更新: ID {material_id}")
        return True
    except Exception as e:
        conn.rollback()
        logger.error(f"❌ 数据库更新失败: ID {material_id} - {e}")
        return False
    finally:
        cursor.close()
        conn.close()

def organize_materials():
    """主要的素材整理函数"""
    logger.info("🚀 开始素材文件整理...")
    
    # 1. 获取需要整理的素材
    materials_to_move = get_materials_to_organize()
    
    if not materials_to_move:
        logger.info("✅ 所有素材文件路径已标准化，无需整理")
        return True
    
    logger.info(f"📊 发现 {len(materials_to_move)} 个素材需要整理")
    
    # 2. 创建备份记录
    backup_file = create_backup_record(materials_to_move)
    
    # 3. 确保目标目录存在
    target_dir = ensure_target_directory()
    
    # 4. 统计信息
    stats = {
        'total': len(materials_to_move),
        'moved': 0,
        'skipped': 0,
        'failed': 0,
        'db_updated': 0,
        'db_failed': 0
    }
    
    # 5. 逐个处理素材
    for i, material in enumerate(materials_to_move, 1):
        logger.info(f"📦 处理素材 {i}/{stats['total']}: {material['filename']}")
        
        # 移动文件
        success, result = move_file_safely(
            material['normalized_path'], 
            material['target_path']
        )
        
        if success:
            if "文件已存在" in result:
                stats['skipped'] += 1
                # 即使文件已存在，也要更新数据库路径
                final_path = material['target_path']
            else:
                stats['moved'] += 1
                final_path = result
            
            # 更新数据库
            if update_database_path(material['id'], final_path):
                stats['db_updated'] += 1
            else:
                stats['db_failed'] += 1
        else:
            stats['failed'] += 1
            logger.error(f"❌ 素材处理失败: {material['filename']} - {result}")
    
    # 6. 输出统计结果
    logger.info("\n" + "="*60)
    logger.info("📊 素材整理完成统计")
    logger.info("="*60)
    logger.info(f"📦 总计素材: {stats['total']}")
    logger.info(f"✅ 成功移动: {stats['moved']}")
    logger.info(f"⏭️ 跳过处理: {stats['skipped']} (文件已存在)")
    logger.info(f"❌ 移动失败: {stats['failed']}")
    logger.info(f"✅ 数据库更新成功: {stats['db_updated']}")
    logger.info(f"❌ 数据库更新失败: {stats['db_failed']}")
    
    success_rate = (stats['moved'] + stats['skipped']) / stats['total'] * 100
    logger.info(f"📈 整体成功率: {success_rate:.1f}%")
    
    if stats['failed'] > 0 or stats['db_failed'] > 0:
        logger.warning(f"⚠️ 发现 {stats['failed'] + stats['db_failed']} 个问题，请检查日志")
        return False
    else:
        logger.success("🎉 所有素材文件整理完成！")
        return True

def verify_organization():
    """验证整理结果"""
    logger.info("🔍 验证素材整理结果...")
    
    conn = get_database_connection()
    cursor = conn.cursor()
    
    # 检查'new'状态素材的路径分布
    cursor.execute("""
        SELECT 
            CASE 
                WHEN file_path LIKE '%01_materials_to_process%缇萃百货%' THEN '标准目录'
                WHEN file_path LIKE '%01_to_process%缇萃百货%' THEN '旧格式目录'
                WHEN file_path LIKE '%缇萃百货%' THEN '其他缇萃目录'
                ELSE '非缇萃目录'
            END as path_category,
            COUNT(*) as count
        FROM local_creatives 
        WHERE status = 'new'
        GROUP BY path_category
        ORDER BY count DESC
    """)
    
    results = cursor.fetchall()
    
    logger.info("📊 验证结果 - 路径分布:")
    logger.info("-" * 40)
    
    standard_count = 0
    total_count = 0
    
    for category, count in results:
        total_count += count
        if category == '标准目录':
            standard_count = count
            logger.success(f"✅ {category}: {count}")
        else:
            logger.warning(f"⚠️ {category}: {count}")
    
    if standard_count == total_count:
        logger.success(f"🎯 验证通过！所有 {total_count} 个素材都在标准目录中")
        success = True
    else:
        logger.warning(f"⚠️ 验证发现问题：{total_count - standard_count} 个素材不在标准目录")
        success = False
    
    cursor.close()
    conn.close()
    
    return success

def main():
    """主函数"""
    logger.info("🎯 千川素材文件整理工具启动")
    
    try:
        # 1. 执行素材整理
        organize_success = organize_materials()
        
        # 2. 验证整理结果
        verify_success = verify_organization()
        
        # 3. 总结
        if organize_success and verify_success:
            logger.success("🎉 素材文件整理完全成功！")
            logger.info("\n📋 后续建议:")
            logger.info("1. 重启Celery Worker和Beat进程")
            logger.info("2. 运行监控脚本观察素材处理进度")
            logger.info("3. 确认工作流正常处理这些素材")
            return True
        else:
            logger.error("❌ 素材文件整理存在问题，请检查日志")
            return False
            
    except Exception as e:
        logger.error(f"❌ 素材整理过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
