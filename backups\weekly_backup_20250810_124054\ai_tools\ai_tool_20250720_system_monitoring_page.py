#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI工具: 千川自动化系统监控页面
生成时间: 2025-07-20
用途: 为Web UI提供系统运行状态监控功能
生命周期: 永久工具
"""

import streamlit as st
import pandas as pd
import time
from datetime import datetime, timedelta
import json
import os
import sys

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, project_root)
sys.path.insert(0, src_path)

def render_system_monitoring_page():
    """系统运行状态监控页面"""
    st.header("🔍 系统运行状态监控")
    st.markdown("实时监控千川自动化系统的运行状态，包括任务执行、数据处理和系统健康度")
    
    # 刷新控制
    col_refresh1, col_refresh2, col_refresh3 = st.columns([2, 1, 1])
    with col_refresh1:
        auto_refresh = st.checkbox("🔄 自动刷新 (30秒)", value=False)
    with col_refresh2:
        manual_refresh = st.button("🔄 手动刷新", use_container_width=True)
    with col_refresh3:
        if st.button("🚀 触发任务", use_container_width=True):
            trigger_workflow_tasks()
    
    # 自动刷新逻辑
    if auto_refresh:
        time.sleep(30)
        st.rerun()
    
    # 系统状态概览
    render_system_overview()
    
    # 任务执行状态
    render_task_status()
    
    # 数据处理统计
    render_data_processing_stats()
    
    # 最新活动记录
    render_recent_activities()
    
    # 系统健康检查
    render_system_health_check()

def render_system_overview():
    """系统状态概览"""
    st.markdown("### 📊 系统状态概览")
    
    try:
        from qianchuan_aw.utils.db_utils import database_session
        from sqlalchemy import text
        
        with database_session() as db:
            # 获取基础统计数据
            stats_query = text("""
                SELECT 
                    (SELECT COUNT(*) FROM local_creatives) as total_materials,
                    (SELECT COUNT(*) FROM campaigns) as total_campaigns,
                    (SELECT COUNT(*) FROM platform_creatives) as total_platform_materials,
                    (SELECT COUNT(*) FROM local_creatives WHERE created_at >= NOW() - INTERVAL '24 hours') as materials_24h,
                    (SELECT COUNT(*) FROM campaigns WHERE created_at >= NOW() - INTERVAL '24 hours') as campaigns_24h
            """)
            
            result = db.execute(stats_query).fetchone()
            
            # 显示指标
            col1, col2, col3, col4, col5 = st.columns(5)
            
            with col1:
                st.metric(
                    "总素材数",
                    f"{result.total_materials:,}",
                    delta=f"+{result.materials_24h}" if result.materials_24h > 0 else None
                )
            
            with col2:
                st.metric(
                    "总计划数", 
                    f"{result.total_campaigns:,}",
                    delta=f"+{result.campaigns_24h}" if result.campaigns_24h > 0 else None
                )
            
            with col3:
                st.metric("平台素材", f"{result.total_platform_materials:,}")
            
            with col4:
                # 计算处理效率
                if result.total_materials > 0:
                    efficiency = (result.total_platform_materials / result.total_materials * 100)
                    st.metric("处理效率", f"{efficiency:.1f}%")
                else:
                    st.metric("处理效率", "0%")
            
            with col5:
                # 系统状态指示器
                if result.materials_24h > 0 or result.campaigns_24h > 0:
                    st.metric("系统状态", "🟢 活跃")
                else:
                    st.metric("系统状态", "🟡 待机")
                    
    except Exception as e:
        st.error(f"❌ 获取系统概览失败: {e}")

def render_task_status():
    """任务执行状态"""
    st.markdown("### ⚙️ 任务执行状态")
    
    try:
        from qianchuan_aw.celery_app import app as celery_app
        
        # 检查Celery应用状态
        col_celery1, col_celery2 = st.columns(2)
        
        with col_celery1:
            st.markdown("#### 🔧 Celery应用状态")
            if celery_app:
                st.success("✅ Celery应用正常加载")
                
                # 显示已注册的任务
                registered_tasks = [task for task in celery_app.tasks.keys() 
                                  if task.startswith('tasks.')]
                
                if registered_tasks:
                    st.markdown("**已注册任务:**")
                    for task in registered_tasks:
                        st.markdown(f"- {task}")
                else:
                    st.warning("⚠️ 未发现已注册的任务")
            else:
                st.error("❌ Celery应用加载失败")
        
        with col_celery2:
            st.markdown("#### 📈 任务执行统计")
            
            # 这里可以添加Redis中的任务队列统计
            try:
                import redis
                from qianchuan_aw.utils.config_manager import get_config_manager
                
                config_manager = get_config_manager()
                redis_config = config_manager.get_redis_config()
                
                r = redis.Redis(
                    host=redis_config.get('host', 'localhost'),
                    port=redis_config.get('port', 6379),
                    db=redis_config.get('db', 0)
                )
                
                # 获取队列信息
                queue_length = r.llen('celery')
                st.metric("队列长度", queue_length)
                
                if queue_length > 0:
                    st.info(f"📋 当前有 {queue_length} 个任务在队列中等待执行")
                else:
                    st.success("✅ 任务队列为空")
                    
            except Exception as e:
                st.warning(f"⚠️ 无法获取队列信息: {e}")
                
    except Exception as e:
        st.error(f"❌ 获取任务状态失败: {e}")

def render_data_processing_stats():
    """数据处理统计"""
    st.markdown("### 📈 数据处理统计")
    
    try:
        from qianchuan_aw.utils.db_utils import database_session
        from sqlalchemy import text
        
        with database_session() as db:
            # 素材状态分布
            status_query = text("""
                SELECT 
                    status,
                    COUNT(*) as count
                FROM local_creatives 
                GROUP BY status
                ORDER BY count DESC
            """)
            
            status_result = db.execute(status_query).fetchall()
            
            col_status1, col_status2 = st.columns(2)
            
            with col_status1:
                st.markdown("#### 📊 素材状态分布")
                if status_result:
                    status_data = []
                    for row in status_result:
                        status_cn = translate_status(row.status)
                        status_data.append({'状态': status_cn, '数量': row.count})
                    
                    status_df = pd.DataFrame(status_data)
                    st.dataframe(status_df, use_container_width=True)
                else:
                    st.info("📭 暂无素材数据")
            
            with col_status2:
                st.markdown("#### 🎯 计划状态分布")
                
                campaign_query = text("""
                    SELECT 
                        status,
                        appeal_status,
                        COUNT(*) as count
                    FROM campaigns 
                    GROUP BY status, appeal_status
                    ORDER BY count DESC
                    LIMIT 10
                """)
                
                campaign_result = db.execute(campaign_query).fetchall()
                
                if campaign_result:
                    campaign_data = []
                    for row in campaign_result:
                        campaign_data.append({
                            '计划状态': row.status,
                            '申诉状态': row.appeal_status,
                            '数量': row.count
                        })
                    
                    campaign_df = pd.DataFrame(campaign_data)
                    st.dataframe(campaign_df, use_container_width=True)
                else:
                    st.info("📭 暂无计划数据")
                    
    except Exception as e:
        st.error(f"❌ 获取数据处理统计失败: {e}")

def render_recent_activities():
    """最新活动记录"""
    st.markdown("### 🕒 最新活动记录")
    
    try:
        from qianchuan_aw.utils.db_utils import database_session
        from sqlalchemy import text
        
        with database_session() as db:
            # 最新素材记录
            recent_materials_query = text("""
                SELECT 
                    filename,
                    status,
                    created_at,
                    updated_at
                FROM local_creatives 
                ORDER BY updated_at DESC 
                LIMIT 10
            """)
            
            recent_materials = db.execute(recent_materials_query).fetchall()
            
            # 最新计划记录
            recent_campaigns_query = text("""
                SELECT 
                    campaign_id_qc,
                    status,
                    appeal_status,
                    created_at
                FROM campaigns 
                ORDER BY created_at DESC 
                LIMIT 10
            """)
            
            recent_campaigns = db.execute(recent_campaigns_query).fetchall()
            
            col_recent1, col_recent2 = st.columns(2)
            
            with col_recent1:
                st.markdown("#### 📁 最新素材活动")
                if recent_materials:
                    materials_data = []
                    for row in recent_materials:
                        materials_data.append({
                            '文件名': row.filename or 'N/A',
                            '状态': translate_status(row.status),
                            '更新时间': row.updated_at.strftime('%m-%d %H:%M') if row.updated_at else 'N/A'
                        })
                    
                    materials_df = pd.DataFrame(materials_data)
                    st.dataframe(materials_df, use_container_width=True)
                else:
                    st.info("📭 暂无最新素材活动")
            
            with col_recent2:
                st.markdown("#### 🎯 最新计划活动")
                if recent_campaigns:
                    campaigns_data = []
                    for row in recent_campaigns:
                        campaigns_data.append({
                            '计划ID': row.campaign_id_qc,
                            '状态': row.status,
                            '创建时间': row.created_at.strftime('%m-%d %H:%M') if row.created_at else 'N/A'
                        })
                    
                    campaigns_df = pd.DataFrame(campaigns_data)
                    st.dataframe(campaigns_df, use_container_width=True)
                else:
                    st.info("📭 暂无最新计划活动")
                    
    except Exception as e:
        st.error(f"❌ 获取最新活动记录失败: {e}")

def render_system_health_check():
    """系统健康检查"""
    st.markdown("### 🏥 系统健康检查")
    
    health_checks = []
    
    # 数据库连接检查
    try:
        from qianchuan_aw.utils.db_utils import database_session
        from sqlalchemy import text
        
        with database_session() as db:
            db.execute(text("SELECT 1")).fetchone()
            health_checks.append({"检查项": "数据库连接", "状态": "✅ 正常", "详情": "连接成功"})
    except Exception as e:
        health_checks.append({"检查项": "数据库连接", "状态": "❌ 异常", "详情": str(e)})
    
    # Redis连接检查
    try:
        import redis
        from qianchuan_aw.utils.config_manager import get_config_manager
        
        config_manager = get_config_manager()
        redis_config = config_manager.get_redis_config()
        
        r = redis.Redis(
            host=redis_config.get('host', 'localhost'),
            port=redis_config.get('port', 6379),
            db=redis_config.get('db', 0)
        )
        r.ping()
        health_checks.append({"检查项": "Redis连接", "状态": "✅ 正常", "详情": "连接成功"})
    except Exception as e:
        health_checks.append({"检查项": "Redis连接", "状态": "❌ 异常", "详情": str(e)})
    
    # 配置文件检查
    try:
        from qianchuan_aw.utils.config_manager import get_config_manager
        config_manager = get_config_manager()
        config = config_manager.get_config()
        
        if config and 'workflow' in config:
            health_checks.append({"检查项": "配置文件", "状态": "✅ 正常", "详情": "配置加载成功"})
        else:
            health_checks.append({"检查项": "配置文件", "状态": "⚠️ 警告", "详情": "配置不完整"})
    except Exception as e:
        health_checks.append({"检查项": "配置文件", "状态": "❌ 异常", "详情": str(e)})
    
    # 显示健康检查结果
    if health_checks:
        health_df = pd.DataFrame(health_checks)
        st.dataframe(health_df, use_container_width=True)
        
        # 总体健康状态
        normal_count = len([check for check in health_checks if "✅" in check["状态"]])
        total_count = len(health_checks)
        
        if normal_count == total_count:
            st.success(f"🎉 系统健康状态良好 ({normal_count}/{total_count})")
        elif normal_count >= total_count * 0.7:
            st.warning(f"⚠️ 系统基本正常，有少量问题 ({normal_count}/{total_count})")
        else:
            st.error(f"❌ 系统存在较多问题，需要检查 ({normal_count}/{total_count})")

def trigger_workflow_tasks():
    """触发工作流任务"""
    try:
        from qianchuan_aw.workflows.tasks import (
            task_ingest_and_upload,
            task_group_and_dispatch,
            task_monitor_materials
        )
        
        # 触发任务
        task_ingest_and_upload.delay()
        task_group_and_dispatch.delay()
        task_monitor_materials.delay()
        
        st.success("✅ 工作流任务已触发！")
        
    except Exception as e:
        st.error(f"❌ 触发任务失败: {e}")

def translate_status(status):
    """翻译状态为中文"""
    status_map = {
        'new': '新建',
        'pending_upload': '待上传',
        'uploaded_pending_plan': '已上传待建计划',
        'creating_plan': '创建计划中',
        'testing_pending_review': '测试待审核',
        'approved': '审核通过',
        'rejected': '审核拒绝',
        'upload_failed': '上传失败',
        'already_tested': '已测试',
        'pending_grouping': '待分组'
    }
    return status_map.get(status, status)

# 如果直接运行此文件，显示监控页面
if __name__ == "__main__":
    render_system_monitoring_page()
