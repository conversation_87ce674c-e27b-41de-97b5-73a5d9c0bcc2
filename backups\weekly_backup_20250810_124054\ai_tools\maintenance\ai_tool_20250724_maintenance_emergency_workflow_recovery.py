#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 千川项目紧急工作流恢复工具
清理条件: 项目工作流管理完全重构时可删除

千川项目紧急工作流恢复工具
========================

基于2025-07-25 00:00-05:00异常诊断结果，提供紧急修复和恢复功能。

主要功能：
1. 系统服务重启和清理
2. 异常状态检测和修复
3. 系统健康检查
4. 紧急监控和告警

使用方法:
python ai_tools/maintenance/ai_tool_20250724_maintenance_emergency_workflow_recovery.py [action]

Actions:
- diagnose: 快速诊断系统状态
- emergency: 执行紧急修复
- monitor: 启动紧急监控
- recover: 全面恢复流程
"""

import os
import sys
import time
import json
import psutil
import subprocess
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
from collections import defaultdict

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

try:
    from src.qianchuan_aw.utils.logger import logger
    from src.qianchuan_aw.database.database import SessionLocal
    from src.qianchuan_aw.database.models import LocalCreative
    from sqlalchemy import text, func
except ImportError as e:
    import logging
    logger = logging.getLogger(__name__)
    logging.basicConfig(level=logging.INFO)
    logger.error(f"导入失败: {e}")


class EmergencyWorkflowRecovery:
    """紧急工作流恢复器"""
    
    def __init__(self):
        self.project_root = Path(project_root)
        self.log_dir = self.project_root / "logs"
        self.recovery_log = []
        
        # 异常阈值定义
        self.thresholds = {
            'log_rate_per_minute': 500,      # 每分钟日志条数
            'error_rate_per_hour': 100,      # 每小时错误数
            'api_calls_per_hour': 100,       # 每小时API调用数
            'status_changes_per_hour': 1000, # 每小时状态变更数
            'cpu_usage_percent': 80,         # CPU使用率
            'memory_usage_percent': 85,      # 内存使用率
            'disk_usage_percent': 90         # 磁盘使用率
        }
        
        logger.info("🚨 初始化紧急工作流恢复器")
    
    def quick_diagnose(self) -> Dict[str, Any]:
        """快速诊断系统状态"""
        logger.info("🔍 执行快速系统诊断...")
        
        diagnosis = {
            'timestamp': datetime.now().isoformat(),
            'system_resources': self._check_system_resources(),
            'log_analysis': self._analyze_recent_logs(),
            'database_status': self._check_database_status(),
            'process_status': self._check_process_status(),
            'disk_space': self._check_disk_space(),
            'overall_health': 'unknown',
            'critical_issues': [],
            'recommendations': []
        }
        
        # 评估整体健康状况
        critical_count = len([issue for issue in diagnosis['critical_issues']])
        if critical_count == 0:
            diagnosis['overall_health'] = 'healthy'
        elif critical_count <= 2:
            diagnosis['overall_health'] = 'warning'
        else:
            diagnosis['overall_health'] = 'critical'
        
        return diagnosis
    
    def _check_system_resources(self) -> Dict[str, Any]:
        """检查系统资源"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            resources = {
                'cpu_usage': cpu_percent,
                'memory_usage': memory.percent,
                'memory_available_gb': memory.available / (1024**3),
                'disk_usage': disk.percent,
                'disk_free_gb': disk.free / (1024**3)
            }
            
            # 检查是否超过阈值
            if cpu_percent > self.thresholds['cpu_usage_percent']:
                self.recovery_log.append(f"⚠️ CPU使用率过高: {cpu_percent}%")
            
            if memory.percent > self.thresholds['memory_usage_percent']:
                self.recovery_log.append(f"⚠️ 内存使用率过高: {memory.percent}%")
            
            if disk.percent > self.thresholds['disk_usage_percent']:
                self.recovery_log.append(f"⚠️ 磁盘使用率过高: {disk.percent}%")
            
            return resources
            
        except Exception as e:
            logger.error(f"检查系统资源失败: {e}")
            return {}
    
    def _analyze_recent_logs(self) -> Dict[str, Any]:
        """分析最近的日志"""
        try:
            # 分析最近1小时的日志
            current_time = datetime.now()
            log_file = self.log_dir / f"app_{current_time.strftime('%Y-%m-%d')}.log"
            
            if not log_file.exists():
                return {'error': '日志文件不存在'}
            
            # 统计最近1小时的日志
            one_hour_ago = current_time - timedelta(hours=1)
            log_stats = {
                'total_lines': 0,
                'error_count': 0,
                'warning_count': 0,
                'api_calls': 0,
                'status_changes': 0
            }
            
            try:
                with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                    # 只读取最后10000行以提高性能
                    lines = f.readlines()[-10000:]
                    
                    for line in lines:
                        if one_hour_ago.strftime('%Y-%m-%d %H:') in line:
                            log_stats['total_lines'] += 1
                            
                            if '| ERROR' in line:
                                log_stats['error_count'] += 1
                            elif '| WARNING' in line:
                                log_stats['warning_count'] += 1
                            
                            if 'api' in line.lower() or '_make_api_call' in line:
                                log_stats['api_calls'] += 1
                            
                            if '状态' in line or 'status' in line.lower():
                                log_stats['status_changes'] += 1
            
            except Exception as e:
                logger.error(f"读取日志文件失败: {e}")
                return {'error': f'读取日志失败: {e}'}
            
            # 检查是否异常
            if log_stats['total_lines'] > self.thresholds['log_rate_per_minute'] * 60:
                self.recovery_log.append(f"🚨 日志产生异常: {log_stats['total_lines']}条/小时")
            
            if log_stats['error_count'] > self.thresholds['error_rate_per_hour']:
                self.recovery_log.append(f"🚨 错误率异常: {log_stats['error_count']}个/小时")
            
            return log_stats
            
        except Exception as e:
            logger.error(f"分析日志失败: {e}")
            return {'error': str(e)}
    
    def _check_database_status(self) -> Dict[str, Any]:
        """检查数据库状态"""
        try:
            db = SessionLocal()
            
            # 检查数据库连接
            db.execute(text("SELECT 1"))
            
            # 检查最近的素材状态
            recent_materials = db.query(LocalCreative).filter(
                LocalCreative.updated_at > datetime.now() - timedelta(hours=1)
            ).count()
            
            # 检查各状态分布
            status_distribution = db.query(
                LocalCreative.status,
                func.count(LocalCreative.id).label('count')
            ).group_by(LocalCreative.status).all()
            
            db.close()
            
            return {
                'connection_ok': True,
                'recent_updates': recent_materials,
                'status_distribution': [{'status': s, 'count': c} for s, c in status_distribution]
            }
            
        except Exception as e:
            logger.error(f"检查数据库状态失败: {e}")
            self.recovery_log.append(f"❌ 数据库连接异常: {e}")
            return {'connection_ok': False, 'error': str(e)}
    
    def _check_process_status(self) -> Dict[str, Any]:
        """检查进程状态"""
        try:
            processes = {
                'python_processes': 0,
                'celery_processes': 0,
                'scheduler_processes': 0,
                'high_cpu_processes': [],
                'high_memory_processes': []
            }
            
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'memory_percent']):
                try:
                    info = proc.info
                    cmdline = ' '.join(info['cmdline']) if info['cmdline'] else ''
                    
                    if 'python' in info['name'].lower():
                        processes['python_processes'] += 1
                    
                    if 'celery' in cmdline.lower():
                        processes['celery_processes'] += 1
                    
                    if 'scheduler' in cmdline.lower():
                        processes['scheduler_processes'] += 1
                    
                    # 检查高CPU使用率进程
                    if info['cpu_percent'] and info['cpu_percent'] > 50:
                        processes['high_cpu_processes'].append({
                            'pid': info['pid'],
                            'name': info['name'],
                            'cpu_percent': info['cpu_percent']
                        })
                    
                    # 检查高内存使用率进程
                    if info['memory_percent'] and info['memory_percent'] > 10:
                        processes['high_memory_processes'].append({
                            'pid': info['pid'],
                            'name': info['name'],
                            'memory_percent': info['memory_percent']
                        })
                
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            return processes
            
        except Exception as e:
            logger.error(f"检查进程状态失败: {e}")
            return {}
    
    def _check_disk_space(self) -> Dict[str, Any]:
        """检查磁盘空间"""
        try:
            disk_info = {}
            
            # 检查项目根目录
            root_usage = psutil.disk_usage(str(self.project_root))
            disk_info['project_root'] = {
                'total_gb': root_usage.total / (1024**3),
                'used_gb': root_usage.used / (1024**3),
                'free_gb': root_usage.free / (1024**3),
                'percent': (root_usage.used / root_usage.total) * 100
            }
            
            # 检查日志目录大小
            if self.log_dir.exists():
                log_size = sum(f.stat().st_size for f in self.log_dir.rglob('*') if f.is_file())
                disk_info['logs_size_gb'] = log_size / (1024**3)
            
            return disk_info
            
        except Exception as e:
            logger.error(f"检查磁盘空间失败: {e}")
            return {}
    
    def emergency_fix(self) -> Dict[str, Any]:
        """执行紧急修复"""
        logger.info("🚨 开始执行紧急修复...")
        
        fix_results = {
            'timestamp': datetime.now().isoformat(),
            'actions_taken': [],
            'errors': [],
            'success': False
        }
        
        try:
            # 1. 清理高CPU进程
            self._kill_runaway_processes(fix_results)
            
            # 2. 清理日志文件
            self._cleanup_logs(fix_results)
            
            # 3. 重置异常状态
            self._reset_abnormal_states(fix_results)
            
            # 4. 清理Celery队列
            self._cleanup_celery_queues(fix_results)
            
            # 5. 检查修复效果
            time.sleep(5)  # 等待系统稳定
            post_fix_diagnosis = self.quick_diagnose()
            
            if post_fix_diagnosis['overall_health'] in ['healthy', 'warning']:
                fix_results['success'] = True
                fix_results['actions_taken'].append("✅ 紧急修复成功")
            else:
                fix_results['actions_taken'].append("⚠️ 紧急修复部分成功，需要进一步处理")
            
        except Exception as e:
            logger.error(f"紧急修复失败: {e}")
            fix_results['errors'].append(str(e))
        
        return fix_results
    
    def _kill_runaway_processes(self, results: Dict):
        """终止失控进程"""
        try:
            killed_processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent']):
                try:
                    info = proc.info
                    cmdline = ' '.join(info['cmdline']) if info['cmdline'] else ''
                    
                    # 终止高CPU使用率的Python进程
                    if (info['cpu_percent'] and info['cpu_percent'] > 80 and 
                        'python' in info['name'].lower() and 
                        'qianchuan' in cmdline.lower()):
                        
                        proc.terminate()
                        killed_processes.append(f"PID {info['pid']}: {info['name']}")
                        logger.info(f"终止失控进程: PID {info['pid']}")
                
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            if killed_processes:
                results['actions_taken'].append(f"🔪 终止失控进程: {len(killed_processes)}个")
            
        except Exception as e:
            results['errors'].append(f"终止进程失败: {e}")
    
    def _cleanup_logs(self, results: Dict):
        """清理日志文件"""
        try:
            cleaned_size = 0
            cleaned_files = 0
            
            # 删除超过7天的日志文件
            cutoff_date = datetime.now() - timedelta(days=7)
            
            for log_file in self.log_dir.glob("*.log"):
                if log_file.stat().st_mtime < cutoff_date.timestamp():
                    file_size = log_file.stat().st_size
                    log_file.unlink()
                    cleaned_size += file_size
                    cleaned_files += 1
            
            # 压缩大型日志文件
            for log_file in self.log_dir.glob("*.log"):
                if log_file.stat().st_size > 100 * 1024 * 1024:  # 100MB
                    try:
                        subprocess.run(['gzip', str(log_file)], check=True)
                        results['actions_taken'].append(f"📦 压缩大型日志: {log_file.name}")
                    except subprocess.CalledProcessError:
                        pass  # 压缩失败不是致命错误
            
            if cleaned_files > 0:
                results['actions_taken'].append(
                    f"🧹 清理日志文件: {cleaned_files}个, {cleaned_size/(1024**2):.1f}MB"
                )
            
        except Exception as e:
            results['errors'].append(f"清理日志失败: {e}")
    
    def _reset_abnormal_states(self, results: Dict):
        """重置异常状态"""
        try:
            db = SessionLocal()
            
            # 重置长时间处于processing状态的素材
            cutoff_time = datetime.now() - timedelta(hours=2)
            
            reset_count = db.query(LocalCreative).filter(
                LocalCreative.status == 'processing',
                LocalCreative.updated_at < cutoff_time
            ).update({
                'status': 'pending_grouping',
                'updated_at': datetime.now()
            })
            
            db.commit()
            db.close()
            
            if reset_count > 0:
                results['actions_taken'].append(f"🔄 重置异常状态素材: {reset_count}个")
            
        except Exception as e:
            results['errors'].append(f"重置状态失败: {e}")
    
    def _cleanup_celery_queues(self, results: Dict):
        """清理Celery队列"""
        try:
            # 尝试清理Celery队列
            celery_script = self.project_root / "tools" / "purge_celery_queue.py"
            if celery_script.exists():
                result = subprocess.run([
                    sys.executable, str(celery_script)
                ], capture_output=True, text=True, timeout=30)
                
                if result.returncode == 0:
                    results['actions_taken'].append("🧹 清理Celery队列成功")
                else:
                    results['errors'].append(f"清理Celery队列失败: {result.stderr}")
            
        except subprocess.TimeoutExpired:
            results['errors'].append("清理Celery队列超时")
        except Exception as e:
            results['errors'].append(f"清理Celery队列异常: {e}")
    
    def start_emergency_monitoring(self) -> None:
        """启动紧急监控"""
        logger.info("📊 启动紧急监控模式...")
        
        try:
            monitor_duration = 300  # 监控5分钟
            check_interval = 30     # 每30秒检查一次
            
            start_time = time.time()
            
            while time.time() - start_time < monitor_duration:
                diagnosis = self.quick_diagnose()
                
                if diagnosis['overall_health'] == 'critical':
                    logger.error("🚨 检测到关键问题，建议立即执行紧急修复")
                    break
                elif diagnosis['overall_health'] == 'warning':
                    logger.warning("⚠️ 检测到警告级别问题")
                else:
                    logger.info("✅ 系统状态正常")
                
                time.sleep(check_interval)
            
            logger.info("📊 紧急监控完成")
            
        except KeyboardInterrupt:
            logger.info("📊 监控被用户中断")
        except Exception as e:
            logger.error(f"监控过程出错: {e}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='千川项目紧急工作流恢复工具')
    parser.add_argument('action', choices=['diagnose', 'emergency', 'monitor', 'recover'], 
                       help='执行的操作')
    parser.add_argument('--output', '-o', help='输出文件路径')
    
    args = parser.parse_args()
    
    recovery = EmergencyWorkflowRecovery()
    
    if args.action == 'diagnose':
        diagnosis = recovery.quick_diagnose()
        
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(diagnosis, f, ensure_ascii=False, indent=2)
            logger.info(f"📄 诊断结果已保存到: {args.output}")
        
        print(f"\n🏥 系统健康诊断:")
        print(f"   整体状态: {diagnosis['overall_health']}")
        print(f"   CPU使用率: {diagnosis['system_resources'].get('cpu_usage', 'N/A')}%")
        print(f"   内存使用率: {diagnosis['system_resources'].get('memory_usage', 'N/A')}%")
        print(f"   最近1小时错误: {diagnosis['log_analysis'].get('error_count', 'N/A')}个")
        
    elif args.action == 'emergency':
        fix_results = recovery.emergency_fix()
        
        print(f"\n🚨 紧急修复结果:")
        print(f"   修复状态: {'成功' if fix_results['success'] else '失败'}")
        print(f"   执行操作: {len(fix_results['actions_taken'])}个")
        print(f"   错误数量: {len(fix_results['errors'])}个")
        
        for action in fix_results['actions_taken']:
            print(f"   {action}")
        
        for error in fix_results['errors']:
            print(f"   ❌ {error}")
    
    elif args.action == 'monitor':
        recovery.start_emergency_monitoring()
    
    elif args.action == 'recover':
        # 完整恢复流程
        print("🚀 开始完整恢复流程...")
        
        # 1. 诊断
        diagnosis = recovery.quick_diagnose()
        print(f"📊 当前状态: {diagnosis['overall_health']}")
        
        # 2. 如果需要，执行紧急修复
        if diagnosis['overall_health'] in ['critical', 'warning']:
            print("🚨 执行紧急修复...")
            fix_results = recovery.emergency_fix()
            print(f"修复结果: {'成功' if fix_results['success'] else '失败'}")
        
        # 3. 启动监控
        print("📊 启动恢复监控...")
        recovery.start_emergency_monitoring()


if __name__ == '__main__':
    main()
