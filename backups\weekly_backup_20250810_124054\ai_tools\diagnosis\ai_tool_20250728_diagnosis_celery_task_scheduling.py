#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 诊断千川自动化项目Celery任务调度问题
清理条件: 项目重构时可考虑删除
"""

import os
import sys
import time
import redis
import psycopg2
import yaml
from pathlib import Path
from datetime import datetime, timedelta
from loguru import logger
import json

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def load_config():
    """加载配置文件"""
    config_path = project_root / 'config' / 'settings.yml'
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def check_celery_beat_schedule():
    """检查Celery Beat调度配置"""
    logger.info("🔍 检查Celery Beat调度配置...")
    
    try:
        # 导入Celery应用
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.celery_app import app
        
        beat_schedule = app.conf.beat_schedule
        
        logger.info(f"📋 Beat调度配置总数: {len(beat_schedule)} 个任务")
        logger.info("="*60)
        
        critical_tasks = [
            'file-ingestion-configurable',
            'group-dispatch-configurable', 
            'plan-creation-configurable',
            'plan-appeal-configurable',
            'material-monitoring-configurable',
            'independent-harvest-configurable'
        ]
        
        for task_name in critical_tasks:
            if task_name in beat_schedule:
                config = beat_schedule[task_name]
                task_func = config.get('task', 'Unknown')
                schedule = config.get('schedule', 'Unknown')
                
                logger.success(f"✅ {task_name}")
                logger.info(f"   📌 任务函数: {task_func}")
                logger.info(f"   ⏰ 调度间隔: {schedule}秒")
            else:
                logger.error(f"❌ {task_name} - 未找到配置")
        
        logger.info("\n📊 完整Beat调度配置:")
        logger.info("-" * 60)
        for name, config in beat_schedule.items():
            logger.info(f"🔹 {name}: {config.get('task')} (间隔: {config.get('schedule')})")
        
        return beat_schedule
        
    except Exception as e:
        logger.error(f"❌ 检查Beat调度配置失败: {e}")
        return None

def check_redis_queues():
    """检查Redis队列状态"""
    logger.info("\n🔍 检查Redis队列状态...")
    
    try:
        config = load_config()
        redis_config = config.get('database', {}).get('redis', {})
        
        r = redis.Redis(
            host=redis_config.get('host', 'localhost'),
            port=redis_config.get('port', 6379),
            db=redis_config.get('db', 0)
        )
        
        # 检查Celery相关键
        celery_keys = r.keys('celery*')
        logger.info(f"📋 Redis中Celery相关键数量: {len(celery_keys)}")
        
        # 检查队列长度
        queue_info = {}
        for key in celery_keys:
            key_str = key.decode('utf-8')
            if key_str.startswith('celery'):
                try:
                    length = r.llen(key)
                    if length > 0:
                        queue_info[key_str] = length
                except:
                    pass
        
        if queue_info:
            logger.info("📊 非空队列状态:")
            for queue, length in queue_info.items():
                status = "⚠️" if length > 100 else "📋"
                logger.info(f"   {status} {queue}: {length} 个任务")
        else:
            logger.info("✅ 所有队列为空")
        
        # 检查最近的任务活动
        recent_keys = r.keys('celery-task-meta-*')
        logger.info(f"📈 最近任务元数据: {len(recent_keys)} 个")
        
        return queue_info
        
    except Exception as e:
        logger.error(f"❌ 检查Redis队列失败: {e}")
        return None

def monitor_task_execution(duration_minutes=3):
    """监控任务执行情况"""
    logger.info(f"\n📊 开始监控任务执行 ({duration_minutes} 分钟)...")
    
    try:
        config = load_config()
        redis_config = config.get('database', {}).get('redis', {})
        
        r = redis.Redis(
            host=redis_config.get('host', 'localhost'),
            port=redis_config.get('port', 6379),
            db=redis_config.get('db', 0)
        )
        
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        task_counts = {}
        check_interval = 30  # 每30秒检查一次
        
        logger.info(f"⏰ 监控开始时间: {start_time.strftime('%H:%M:%S')}")
        logger.info(f"⏰ 监控结束时间: {end_time.strftime('%H:%M:%S')}")
        
        while datetime.now() < end_time:
            # 检查最近的任务
            recent_keys = r.keys('celery-task-meta-*')
            
            for key in recent_keys[-50:]:  # 只检查最近50个任务
                try:
                    task_data = r.get(key)
                    if task_data:
                        task_info = json.loads(task_data.decode('utf-8'))
                        task_name = task_info.get('task_id', 'unknown')
                        
                        # 提取任务类型
                        if 'upload_single_video' in str(task_info):
                            task_type = 'upload_single_video'
                        elif 'create_plans' in str(task_info):
                            task_type = 'create_plans'
                        elif 'monitor_materials' in str(task_info):
                            task_type = 'monitor_materials'
                        elif 'harvest_materials' in str(task_info):
                            task_type = 'harvest_materials'
                        elif 'appeal_plans' in str(task_info):
                            task_type = 'appeal_plans'
                        else:
                            task_type = 'other'
                        
                        task_counts[task_type] = task_counts.get(task_type, 0) + 1
                except:
                    pass
            
            # 显示当前统计
            current_time = datetime.now().strftime('%H:%M:%S')
            logger.info(f"📊 [{current_time}] 任务执行统计: {dict(task_counts)}")
            
            time.sleep(check_interval)
        
        logger.info("\n📈 监控完成 - 任务执行统计:")
        logger.info("-" * 50)
        
        total_tasks = sum(task_counts.values())
        if total_tasks > 0:
            for task_type, count in sorted(task_counts.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / total_tasks) * 100
                logger.info(f"🔹 {task_type}: {count} 次 ({percentage:.1f}%)")
        else:
            logger.warning("⚠️ 监控期间未检测到任务执行")
        
        return task_counts
        
    except Exception as e:
        logger.error(f"❌ 监控任务执行失败: {e}")
        return {}

def check_workflow_task_status():
    """检查工作流任务状态"""
    logger.info("\n🔍 检查工作流任务状态...")
    
    try:
        config = load_config()
        db_config = config['database']['postgresql']
        
        conn = psycopg2.connect(
            host=db_config['host'],
            port=db_config['port'],
            database=db_config['dbname'],
            user=db_config['user'],
            password=db_config['password']
        )
        
        cursor = conn.cursor()
        
        # 检查各状态素材数量
        cursor.execute("""
            SELECT status, COUNT(*) 
            FROM local_creatives 
            GROUP BY status 
            ORDER BY COUNT(*) DESC
        """)
        
        logger.info("📊 素材状态分布:")
        logger.info("-" * 40)
        
        status_counts = {}
        for status, count in cursor.fetchall():
            status_counts[status] = count
            
            if status == 'new' and count > 0:
                logger.info(f"🎯 {status}: {count} (待上传)")
            elif status == 'processing' and count > 0:
                logger.info(f"⚙️ {status}: {count} (上传中)")
            elif status == 'uploaded_pending_plan' and count > 0:
                logger.info(f"📋 {status}: {count} (待建计划)")
            elif status == 'approved' and count > 0:
                logger.info(f"✅ {status}: {count} (待收割)")
            else:
                logger.info(f"📄 {status}: {count}")
        
        # 分析工作流瓶颈
        logger.info("\n🔍 工作流瓶颈分析:")
        logger.info("-" * 40)
        
        new_count = status_counts.get('new', 0)
        processing_count = status_counts.get('processing', 0)
        pending_plan_count = status_counts.get('uploaded_pending_plan', 0)
        approved_count = status_counts.get('approved', 0)
        
        if new_count > 100:
            logger.warning(f"⚠️ 上传瓶颈: {new_count} 个素材待上传")
        
        if pending_plan_count > 50:
            logger.warning(f"⚠️ 计划创建瓶颈: {pending_plan_count} 个素材待建计划")
        
        if approved_count > 100:
            logger.warning(f"⚠️ 收割瓶颈: {approved_count} 个素材待收割")
        
        if processing_count > 20:
            logger.warning(f"⚠️ 处理积压: {processing_count} 个素材处理中")
        
        cursor.close()
        conn.close()
        
        return status_counts
        
    except Exception as e:
        logger.error(f"❌ 检查工作流状态失败: {e}")
        return {}

def analyze_task_dependencies():
    """分析任务依赖关系"""
    logger.info("\n🔍 分析任务依赖关系...")
    
    task_flow = {
        'file-ingestion-configurable': {
            'function': 'tasks.ingest_and_upload',
            'purpose': '文件摄取和上传',
            'triggers': ['new状态素材'],
            'outputs': ['processing状态', 'uploaded_pending_plan状态']
        },
        'group-dispatch-configurable': {
            'function': 'tasks.group_and_dispatch', 
            'purpose': '分组和派发上传任务',
            'triggers': ['new状态素材'],
            'outputs': ['processing状态']
        },
        'plan-creation-configurable': {
            'function': 'tasks.create_plans',
            'purpose': '创建广告计划',
            'triggers': ['uploaded_pending_plan状态素材'],
            'outputs': ['testing_pending_review状态']
        },
        'material-monitoring-configurable': {
            'function': 'tasks.monitor_materials',
            'purpose': '监控素材审核状态',
            'triggers': ['testing_pending_review状态素材'],
            'outputs': ['approved状态']
        },
        'independent-harvest-configurable': {
            'function': 'tasks.harvest_materials',
            'purpose': '收割审核通过素材',
            'triggers': ['approved状态素材'],
            'outputs': ['harvested状态']
        },
        'plan-appeal-configurable': {
            'function': 'tasks.appeal_plans',
            'purpose': '申诉被拒绝计划',
            'triggers': ['rejected计划'],
            'outputs': ['重新审核']
        }
    }
    
    logger.info("📋 工作流任务依赖关系:")
    logger.info("="*60)
    
    for task_name, info in task_flow.items():
        logger.info(f"🔹 {task_name}")
        logger.info(f"   📌 功能: {info['purpose']}")
        logger.info(f"   🎯 触发条件: {', '.join(info['triggers'])}")
        logger.info(f"   📤 输出结果: {', '.join(info['outputs'])}")
        logger.info("")
    
    return task_flow

def generate_diagnosis_report():
    """生成诊断报告"""
    logger.info("🚀 开始Celery任务调度诊断...")
    logger.info("="*80)
    
    # 1. 检查Beat调度配置
    beat_schedule = check_celery_beat_schedule()
    
    # 2. 检查Redis队列
    queue_info = check_redis_queues()
    
    # 3. 检查工作流状态
    workflow_status = check_workflow_task_status()
    
    # 4. 分析任务依赖
    task_dependencies = analyze_task_dependencies()
    
    # 5. 监控任务执行
    task_execution = monitor_task_execution(2)  # 监控2分钟
    
    # 6. 生成诊断结论
    logger.info("\n" + "="*80)
    logger.info("🎯 诊断结论和建议")
    logger.info("="*80)
    
    issues_found = []
    recommendations = []
    
    # 分析Beat调度问题
    if not beat_schedule:
        issues_found.append("Beat调度配置加载失败")
        recommendations.append("检查Celery应用配置和导入路径")
    
    # 分析队列积压问题
    if queue_info and any(length > 50 for length in queue_info.values()):
        issues_found.append("Redis队列存在积压")
        recommendations.append("检查Worker处理能力和任务执行效率")
    
    # 分析任务执行不均衡
    if task_execution:
        upload_tasks = task_execution.get('upload_single_video', 0)
        other_tasks = sum(v for k, v in task_execution.items() if k != 'upload_single_video')
        
        if upload_tasks > 0 and other_tasks == 0:
            issues_found.append("只有上传任务在执行，其他工作流任务未运行")
            recommendations.append("检查Beat调度器是否正常派发所有类型任务")
        elif upload_tasks > other_tasks * 10:
            issues_found.append("上传任务占比过高，可能阻塞其他任务")
            recommendations.append("调整任务优先级或增加Worker并发数")
    
    # 分析工作流瓶颈
    if workflow_status:
        new_count = workflow_status.get('new', 0)
        pending_plan_count = workflow_status.get('uploaded_pending_plan', 0)
        
        if new_count > 100 and pending_plan_count == 0:
            issues_found.append("大量素材待上传，但无计划创建活动")
            recommendations.append("确认计划创建任务是否正常调度")
    
    # 输出诊断结果
    if issues_found:
        logger.error("🚨 发现的问题:")
        for i, issue in enumerate(issues_found, 1):
            logger.error(f"   {i}. {issue}")
        
        logger.info("\n💡 建议的解决方案:")
        for i, rec in enumerate(recommendations, 1):
            logger.info(f"   {i}. {rec}")
    else:
        logger.success("✅ 未发现明显的调度问题")
    
    return {
        'beat_schedule': beat_schedule,
        'queue_info': queue_info,
        'workflow_status': workflow_status,
        'task_execution': task_execution,
        'issues': issues_found,
        'recommendations': recommendations
    }

def main():
    """主函数"""
    try:
        report = generate_diagnosis_report()
        
        # 保存诊断报告
        report_file = project_root / 'ai_temp' / f'celery_diagnosis_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        report_file.parent.mkdir(exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"\n📋 诊断报告已保存: {report_file}")
        
        return len(report.get('issues', [])) == 0
        
    except Exception as e:
        logger.error(f"❌ 诊断过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
