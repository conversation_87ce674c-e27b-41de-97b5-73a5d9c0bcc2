#!/usr/bin/env python3
"""
提审模块工作流集成补丁
替换原有的handle_plans_awaiting_appeal函数
"""

from qianchuan_aw.services.unified_appeal_service import unified_appeal_service, AppealRequest
from qianchuan_aw.utils.appeal_state_manager import appeal_state_manager
from qianchuan_aw.utils.logger import logger

def handle_plans_awaiting_appeal_v2():
    """
    处理等待提审的计划 (V2版本)
    使用统一提审服务和原子性状态管理
    """
    logger.info("--- [工作流] 开始：检查并提审计划 (V2) ---")
    
    try:
        # 获取需要提审的计划
        plans_to_appeal = get_plans_needing_appeal()
        
        if not plans_to_appeal:
            logger.info("✅ 无需要提审的计划")
            return
        
        logger.info(f"📋 发现 {len(plans_to_appeal)} 个需要提审的计划")
        
        success_count = 0
        failed_count = 0
        
        for plan in plans_to_appeal:
            try:
                # 创建提审请求
                request = AppealRequest(
                    plan_id=plan['plan_id'],
                    account_id=plan['account_id'],
                    appeal_type="auto",
                    priority=1,
                    max_retries=3
                )
                
                # 使用原子性状态管理执行提审
                with appeal_state_manager.atomic_appeal_operation(plan['plan_id']):
                    result = unified_appeal_service.submit_appeal(request)
                    
                    if result.success:
                        success_count += 1
                        logger.info(f"✅ 计划 {plan['plan_id']} 提审成功")
                    else:
                        failed_count += 1
                        logger.warning(f"❌ 计划 {plan['plan_id']} 提审失败: {result.message}")
                    
                    # 更新提审结果
                    appeal_state_manager.update_appeal_result(
                        plan['plan_id'], 
                        result.success, 
                        result.message
                    )
                    
            except Exception as e:
                failed_count += 1
                logger.error(f"❌ 计划 {plan['plan_id']} 提审异常: {e}")
        
        # 输出统计信息
        logger.info(f"📊 提审完成统计: 成功 {success_count}, 失败 {failed_count}")
        
        # 清理过期的提审状态
        appeal_state_manager.cleanup_stale_appeals()
        
        # 输出服务统计
        stats = unified_appeal_service.get_stats()
        logger.info(f"📈 提审服务统计: 总计 {stats['total_appeals']}, 成功率 {stats['success_rate']:.1f}%")
        
    except Exception as e:
        logger.error(f"❌ 提审工作流异常: {e}")

def get_plans_needing_appeal():
    """获取需要提审的计划"""
    from qianchuan_aw.utils.db_utils import database_session
    from sqlalchemy import text
    
    try:
        with database_session() as db:
            query = text("""
                SELECT DISTINCT 
                    ap.plan_id,
                    ap.account_id,
                    ap.status,
                    ap.appeal_status,
                    ap.appeal_time
                FROM campaigns ap
                JOIN ad_accounts aa ON ap.account_id = aa.id
                WHERE aa.status = 'active'
                    AND ap.status IN ('rejected', 'under_review', 'failed_review')
                    AND (ap.appeal_status IS NULL 
                         OR ap.appeal_status NOT IN ('appealing', 'pending_review'))
                    AND (ap.appeal_time IS NULL 
                         OR ap.appeal_time < NOW() - INTERVAL '1 hour')
                ORDER BY ap.updated_at ASC
                LIMIT 10
            """)
            
            results = db.execute(query).fetchall()
            
            return [
                {
                    'plan_id': row.plan_id,
                    'account_id': row.account_id,
                    'status': row.status,
                    'appeal_status': row.appeal_status,
                    'appeal_time': row.appeal_time
                }
                for row in results
            ]
            
    except Exception as e:
        logger.error(f"获取需要提审的计划失败: {e}")
        return []
