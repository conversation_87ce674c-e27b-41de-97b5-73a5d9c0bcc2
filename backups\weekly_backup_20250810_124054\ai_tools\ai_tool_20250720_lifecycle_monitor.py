#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时监控任务脚本
"""

import sys
import os
from datetime import datetime, timedelta

project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

def check_lifecycle_health():
    """检查TEST账户生命周期健康状态"""
    try:
        from qianchuan_aw.utils.db_utils import database_session
        from sqlalchemy import text

        with database_session() as db:
            # 只检查TEST账户的停滞素材
            stuck_materials = db.execute(text("""
                SELECT
                    lc.status,
                    COUNT(*) as count
                FROM local_creatives lc
                JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
                WHERE aa.account_type = 'TEST'
                    AND aa.status = 'active'
                    AND lc.updated_at < NOW() - INTERVAL '48 hours'
                    AND lc.status NOT IN ('approved', 'rejected', 'already_tested')
                GROUP BY lc.status
            """)).fetchall()

            total_stuck = sum(row.count for row in stuck_materials)

            # 检查TEST账户的审核通过率
            approval_stats = db.execute(text("""
                SELECT
                    COUNT(CASE WHEN lc.status = 'approved' THEN 1 END) as approved,
                    COUNT(CASE WHEN lc.status = 'rejected' THEN 1 END) as rejected
                FROM local_creatives lc
                JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
                WHERE aa.account_type = 'TEST'
                    AND aa.status = 'active'
                    AND lc.status IN ('approved', 'rejected')
            """)).fetchone()

            total_tested = approval_stats.approved + approval_stats.rejected
            approval_rate = (approval_stats.approved / total_tested * 100) if total_tested > 0 else 0

            print(f"📊 TEST账户监控状态:")
            print(f"  停滞素材: {total_stuck} 个")
            print(f"  审核通过率: {approval_rate:.1f}%")

            if total_stuck > 100:
                print(f"🚨 CRITICAL: TEST账户有 {total_stuck} 个素材停滞超过48小时")
                return False
            elif total_stuck > 50:
                print(f"⚠️ WARNING: TEST账户有 {total_stuck} 个素材停滞")
                return True
            elif approval_rate < 40:
                print(f"⚠️ WARNING: TEST账户审核通过率偏低 ({approval_rate:.1f}%)")
                return True
            else:
                print("✅ INFO: TEST账户素材测试工作流运行正常")
                return True

    except Exception as e:
        print(f"❌ TEST账户监控检查失败: {e}")
        return False

if __name__ == "__main__":
    check_lifecycle_health()
