#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 修复千川自动化项目工作流瓶颈问题
清理条件: 项目重构时可考虑删除
"""

import os
import sys
import time
import redis
import psycopg2
import yaml
from pathlib import Path
from datetime import datetime, timedelta
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def load_config():
    """加载配置文件"""
    config_path = project_root / 'config' / 'settings.yml'
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def clear_redis_queue_backlog():
    """清理Redis队列积压"""
    logger.info("🔧 清理Redis队列积压...")
    
    try:
        config = load_config()
        redis_config = config.get('database', {}).get('redis', {})
        
        r = redis.Redis(
            host=redis_config.get('host', 'localhost'),
            port=redis_config.get('port', 6379),
            db=redis_config.get('db', 0)
        )
        
        # 检查队列长度
        queue_length = r.llen('celery')
        logger.info(f"📊 当前队列长度: {queue_length}")
        
        if queue_length > 50:
            logger.warning(f"⚠️ 队列积压严重，建议清理部分任务")
            
            # 可选：清理过期的任务（谨慎操作）
            # r.ltrim('celery', -10, -1)  # 只保留最后10个任务
            logger.info("💡 建议重启Worker进程以提高处理效率")
        else:
            logger.success("✅ 队列长度正常")
        
        return queue_length
        
    except Exception as e:
        logger.error(f"❌ 清理Redis队列失败: {e}")
        return -1

def trigger_plan_creation_manually():
    """手动触发计划创建任务"""
    logger.info("🚀 手动触发计划创建任务...")
    
    try:
        # 导入Celery应用和任务
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.celery_app import app
        from qianchuan_aw.workflows import tasks
        
        # 手动派发计划创建任务
        result = tasks.task_create_plans.delay()
        logger.success(f"✅ 计划创建任务已派发: {result.id}")
        
        # 等待一段时间查看结果
        time.sleep(5)
        
        if result.ready():
            if result.successful():
                logger.success("✅ 计划创建任务执行成功")
            else:
                logger.error(f"❌ 计划创建任务执行失败: {result.result}")
        else:
            logger.info("⏳ 计划创建任务正在执行中...")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 手动触发计划创建失败: {e}")
        return False

def trigger_harvest_manually():
    """手动触发收割任务"""
    logger.info("🌾 手动触发收割任务...")
    
    try:
        # 导入Celery应用和任务
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.celery_app import app
        from qianchuan_aw.workflows import tasks
        
        # 手动派发收割任务
        result = tasks.task_harvest_materials.delay()
        logger.success(f"✅ 收割任务已派发: {result.id}")
        
        # 等待一段时间查看结果
        time.sleep(5)
        
        if result.ready():
            if result.successful():
                logger.success("✅ 收割任务执行成功")
            else:
                logger.error(f"❌ 收割任务执行失败: {result.result}")
        else:
            logger.info("⏳ 收割任务正在执行中...")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 手动触发收割失败: {e}")
        return False

def trigger_material_monitoring_manually():
    """手动触发素材监控任务"""
    logger.info("👁️ 手动触发素材监控任务...")
    
    try:
        # 导入Celery应用和任务
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.celery_app import app
        from qianchuan_aw.workflows import tasks
        
        # 手动派发素材监控任务
        result = tasks.task_monitor_materials.delay()
        logger.success(f"✅ 素材监控任务已派发: {result.id}")
        
        # 等待一段时间查看结果
        time.sleep(5)
        
        if result.ready():
            if result.successful():
                logger.success("✅ 素材监控任务执行成功")
            else:
                logger.error(f"❌ 素材监控任务执行失败: {result.result}")
        else:
            logger.info("⏳ 素材监控任务正在执行中...")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 手动触发素材监控失败: {e}")
        return False

def check_workflow_progress():
    """检查工作流进度"""
    logger.info("📊 检查工作流进度...")
    
    try:
        config = load_config()
        db_config = config['database']['postgresql']
        
        conn = psycopg2.connect(
            host=db_config['host'],
            port=db_config['port'],
            database=db_config['dbname'],
            user=db_config['user'],
            password=db_config['password']
        )
        
        cursor = conn.cursor()
        
        # 检查关键状态的素材数量
        cursor.execute("""
            SELECT status, COUNT(*) 
            FROM local_creatives 
            WHERE status IN ('new', 'processing', 'uploaded_pending_plan', 'approved', 'testing_pending_review')
            GROUP BY status 
            ORDER BY COUNT(*) DESC
        """)
        
        status_counts = {}
        for status, count in cursor.fetchall():
            status_counts[status] = count
        
        logger.info("📈 当前工作流状态:")
        logger.info("-" * 40)
        
        for status, count in status_counts.items():
            if status == 'uploaded_pending_plan':
                alert = "🚨" if count > 100 else "📋"
                logger.info(f"{alert} {status}: {count} (计划创建瓶颈)")
            elif status == 'approved':
                alert = "🚨" if count > 100 else "✅"
                logger.info(f"{alert} {status}: {count} (收割瓶颈)")
            elif status == 'processing':
                alert = "⚠️" if count > 50 else "⚙️"
                logger.info(f"{alert} {status}: {count}")
            else:
                logger.info(f"📄 {status}: {count}")
        
        cursor.close()
        conn.close()
        
        return status_counts
        
    except Exception as e:
        logger.error(f"❌ 检查工作流进度失败: {e}")
        return {}

def optimize_celery_configuration():
    """优化Celery配置建议"""
    logger.info("⚙️ 分析Celery配置优化建议...")
    
    suggestions = []
    
    # 检查当前配置
    config = load_config()
    workflow_config = config.get('workflow', {})
    
    # 分析任务调度间隔
    plan_creation_interval = workflow_config.get('plan_creation', {}).get('interval_seconds', 120)
    material_monitoring_interval = workflow_config.get('material_monitoring', {}).get('interval_seconds', 180)
    harvest_interval = workflow_config.get('independent_harvest', {}).get('interval_seconds', 300)
    
    logger.info("📋 当前任务调度间隔:")
    logger.info(f"   🔹 计划创建: {plan_creation_interval}秒")
    logger.info(f"   🔹 素材监控: {material_monitoring_interval}秒")
    logger.info(f"   🔹 素材收割: {harvest_interval}秒")
    
    # 提供优化建议
    if plan_creation_interval > 60:
        suggestions.append("建议将计划创建间隔调整为60秒以加快处理速度")
    
    if material_monitoring_interval > 120:
        suggestions.append("建议将素材监控间隔调整为120秒以及时发现审核通过的素材")
    
    if harvest_interval > 180:
        suggestions.append("建议将收割间隔调整为180秒以快速收割审核通过的素材")
    
    logger.info("\n💡 优化建议:")
    logger.info("-" * 40)
    if suggestions:
        for i, suggestion in enumerate(suggestions, 1):
            logger.info(f"   {i}. {suggestion}")
    else:
        logger.success("✅ 当前配置已经比较合理")
    
    return suggestions

def run_comprehensive_fix():
    """运行综合修复"""
    logger.info("🚀 开始工作流瓶颈综合修复...")
    logger.info("="*60)
    
    results = {
        'redis_queue_cleared': False,
        'plan_creation_triggered': False,
        'harvest_triggered': False,
        'monitoring_triggered': False,
        'progress_checked': False
    }
    
    # 1. 检查初始状态
    initial_progress = check_workflow_progress()
    results['progress_checked'] = bool(initial_progress)
    
    # 2. 清理Redis队列积压
    queue_length = clear_redis_queue_backlog()
    results['redis_queue_cleared'] = queue_length >= 0
    
    # 3. 手动触发关键任务
    if initial_progress.get('uploaded_pending_plan', 0) > 50:
        logger.info("\n🎯 检测到计划创建瓶颈，手动触发计划创建任务...")
        results['plan_creation_triggered'] = trigger_plan_creation_manually()
    
    if initial_progress.get('approved', 0) > 50:
        logger.info("\n🌾 检测到收割瓶颈，手动触发收割任务...")
        results['harvest_triggered'] = trigger_harvest_manually()
    
    # 4. 触发素材监控以更新状态
    logger.info("\n👁️ 触发素材监控任务以更新审核状态...")
    results['monitoring_triggered'] = trigger_material_monitoring_manually()
    
    # 5. 等待任务执行并检查结果
    logger.info("\n⏳ 等待任务执行完成...")
    time.sleep(30)  # 等待30秒
    
    # 6. 检查修复后的状态
    final_progress = check_workflow_progress()
    
    # 7. 分析修复效果
    logger.info("\n📊 修复效果分析:")
    logger.info("="*60)
    
    if initial_progress and final_progress:
        for status in ['uploaded_pending_plan', 'approved', 'processing']:
            initial = initial_progress.get(status, 0)
            final = final_progress.get(status, 0)
            change = final - initial
            
            if change < 0:
                logger.success(f"✅ {status}: {initial} → {final} (减少 {abs(change)})")
            elif change > 0:
                logger.warning(f"⚠️ {status}: {initial} → {final} (增加 {change})")
            else:
                logger.info(f"➡️ {status}: {initial} (无变化)")
    
    # 8. 提供优化建议
    suggestions = optimize_celery_configuration()
    
    # 9. 总结修复结果
    logger.info("\n🎯 修复结果总结:")
    logger.info("="*60)
    
    success_count = sum(results.values())
    total_count = len(results)
    
    for action, success in results.items():
        status = "✅" if success else "❌"
        action_name = action.replace('_', ' ').title()
        logger.info(f"{status} {action_name}")
    
    success_rate = (success_count / total_count) * 100
    logger.info(f"\n📈 修复成功率: {success_count}/{total_count} ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        logger.success("🎉 工作流瓶颈修复基本成功！")
        logger.info("\n📋 后续建议:")
        logger.info("1. 持续监控工作流状态")
        logger.info("2. 如果瓶颈持续，考虑调整任务调度间隔")
        logger.info("3. 增加Worker并发数以提高处理能力")
    else:
        logger.error("❌ 工作流瓶颈修复存在问题")
        logger.info("\n🔧 建议操作:")
        logger.info("1. 检查Celery Worker进程状态")
        logger.info("2. 查看Worker日志中的错误信息")
        logger.info("3. 考虑重启Celery进程")
    
    return success_rate >= 80

def main():
    """主函数"""
    try:
        success = run_comprehensive_fix()
        return 0 if success else 1
    except Exception as e:
        logger.error(f"❌ 修复过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
