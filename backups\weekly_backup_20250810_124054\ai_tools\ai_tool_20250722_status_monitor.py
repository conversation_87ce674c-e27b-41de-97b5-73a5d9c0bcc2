#!/usr/bin/env python3
"""
素材状态流转监控脚本
实时监控素材状态变化和计划创建情况
"""

import sys
import os
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text

def monitor_status_flow():
    """监控状态流转"""
    logger.info("🔍 素材状态流转监控")
    logger.info("=" * 50)
    
    try:
        with database_session() as db:
            # 检查关键状态的素材数量
            status_query = text("""
                SELECT 
                    lc.status,
                    COUNT(*) as count
                FROM local_creatives lc
                JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                JOIN ad_accounts aa ON pc.account_id = aa.id
                WHERE aa.status = 'active'
                    AND lc.status IN ('approved', 'uploaded_pending_plan', 'creating_plan')
                GROUP BY lc.status
                ORDER BY count DESC
            """)
            
            results = db.execute(status_query).fetchall()
            
            logger.info("📊 关键状态素材数量:")
            for row in results:
                logger.info(f"  {row.status}: {row.count}个")
            
            # 检查可创建计划的账户
            ready_accounts_query = text("""
                SELECT 
                    aa.name as account_name,
                    aa.account_type,
                    COUNT(lc.id) as ready_count
                FROM local_creatives lc
                JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                JOIN ad_accounts aa ON pc.account_id = aa.id
                WHERE lc.status = 'uploaded_pending_plan'
                    AND aa.status = 'active'
                GROUP BY aa.name, aa.account_type
                HAVING COUNT(lc.id) >= 9
                ORDER BY ready_count DESC
            """)
            
            ready_results = db.execute(ready_accounts_query).fetchall()
            
            if ready_results:
                logger.info("\n✅ 可创建计划的账户:")
                for row in ready_results:
                    logger.info(f"  {row.account_name} ({row.account_type}): {row.ready_count}个素材")
            else:
                logger.warning("\n⚠️ 暂无账户达到9个素材的要求")
            
            # 检查最近的计划创建
            recent_plans_query = text("""
                SELECT COUNT(*) as count
                FROM campaigns
                WHERE created_at > NOW() - INTERVAL '1 hour'
            """)
            
            recent_count = db.execute(recent_plans_query).fetchone()
            logger.info(f"\n📈 最近1小时创建的计划: {recent_count.count}个")
            
    except Exception as e:
        logger.error(f"❌ 监控失败: {e}")

if __name__ == "__main__":
    monitor_status_flow()
