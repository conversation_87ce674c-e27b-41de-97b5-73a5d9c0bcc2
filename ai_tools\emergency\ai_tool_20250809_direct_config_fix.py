#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 直接修复配置问题，绕过缓存
清理条件: 成为配置修复工具，长期保留
"""

import os
import sys

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger


def direct_config_fix():
    """直接修复配置问题"""
    logger.info("🔧 直接修复配置问题")
    logger.info("="*80)
    
    try:
        # 1. 检查当前配置加载情况
        logger.info("📋 检查当前配置加载...")
        
        from qianchuan_aw.utils.config_manager import get_config_manager
        config_manager = get_config_manager()
        app_settings = config_manager.get_config()
        
        test_workflow = app_settings.get('plan_creation_defaults', {}).get('test_workflow', {})
        current_creative_count = test_workflow.get('creative_count', 9)
        
        logger.info(f"   当前creative_count: {current_creative_count}")
        
        if current_creative_count == 3:
            logger.success("✅ 配置已正确加载，creative_count = 3")
        else:
            logger.warning(f"⚠️ 配置未正确加载，creative_count = {current_creative_count}")
        
        # 2. 直接调用handle_plan_creation并观察行为
        logger.info("🧪 直接测试handle_plan_creation...")
        
        from qianchuan_aw.workflows.scheduler import handle_plan_creation
        from qianchuan_aw.utils.db_utils import database_session
        from qianchuan_aw.database.database import SessionLocal
        from qianchuan_aw.database.models import LocalCreative, Principal, Campaign
        
        # 检查修复前状态
        with SessionLocal() as db:
            ticui_uploaded_before = db.query(LocalCreative).join(Principal).filter(
                Principal.name == '缇萃百货',
                LocalCreative.status == 'uploaded_pending_plan'
            ).count()
            
            campaigns_before = db.query(Campaign).count()
            
            logger.info(f"📊 测试前状态:")
            logger.info(f"   uploaded_pending_plan: {ticui_uploaded_before} 个")
            logger.info(f"   现有计划: {campaigns_before} 个")
        
        # 执行handle_plan_creation
        with database_session() as db:
            logger.info("   执行handle_plan_creation...")
            handle_plan_creation(db, app_settings)
            logger.info("   执行完成")
        
        # 检查修复后状态
        with SessionLocal() as db:
            ticui_uploaded_after = db.query(LocalCreative).join(Principal).filter(
                Principal.name == '缇萃百货',
                LocalCreative.status == 'uploaded_pending_plan'
            ).count()
            
            ticui_testing_after = db.query(LocalCreative).join(Principal).filter(
                Principal.name == '缇萃百货',
                LocalCreative.status == 'testing_pending_review'
            ).count()
            
            campaigns_after = db.query(Campaign).count()
            
            logger.info(f"📊 测试后状态:")
            logger.info(f"   uploaded_pending_plan: {ticui_uploaded_after} 个")
            logger.info(f"   testing_pending_review: {ticui_testing_after} 个")
            logger.info(f"   计划总数: {campaigns_after} 个")
            
            # 计算变化
            uploaded_change = ticui_uploaded_before - ticui_uploaded_after
            campaigns_change = campaigns_after - campaigns_before
            
            logger.info(f"📈 变化:")
            logger.info(f"   uploaded_pending_plan 减少: {uploaded_change} 个")
            logger.info(f"   新创建计划: {campaigns_change} 个")
            
            if campaigns_change > 0:
                logger.success(f"🎊 成功！创建了 {campaigns_change} 个新计划")
                return True
            elif uploaded_change > 0:
                logger.success(f"✅ 进展！{uploaded_change} 个素材状态已改变")
                return True
            else:
                logger.warning("⚠️ 没有明显变化")
                
                # 3. 如果还是没有效果，尝试临时修改代码中的默认值
                logger.info("🔧 尝试临时代码修复...")
                return self._try_temporary_code_fix()
        
    except Exception as e:
        logger.error(f"❌ 直接修复失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


def _try_temporary_code_fix():
    """尝试临时代码修复"""
    logger.info("🛠️ 尝试临时代码修复...")
    
    try:
        # 检查scheduler.py中的默认值设置
        scheduler_file = os.path.join(project_root, 'src/qianchuan_aw/workflows/scheduler.py')
        
        with open(scheduler_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找关键行
        if "required_creative_count = workflow_config.get('creative_count', 9 if account.account_type == 'TEST' else 3)" in content:
            logger.info("🔍 找到问题：代码中TEST账户默认值仍为9")
            
            # 临时修改默认值
            new_content = content.replace(
                "required_creative_count = workflow_config.get('creative_count', 9 if account.account_type == 'TEST' else 3)",
                "required_creative_count = workflow_config.get('creative_count', 3 if account.account_type == 'TEST' else 3)"
            )
            
            # 备份原文件
            backup_file = scheduler_file + '.backup'
            with open(backup_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 写入修改后的内容
            with open(scheduler_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            logger.success("✅ 临时修改代码：TEST账户默认值改为3")
            logger.warning("⚠️ 这是临时修复，建议重启Celery服务后恢复原文件")
            
            return True
        else:
            logger.warning("⚠️ 未找到预期的代码行，可能已经被修改")
            return False
            
    except Exception as e:
        logger.error(f"❌ 临时代码修复失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("🚀 启动直接配置修复")
    logger.info("🎯 目标：绕过配置缓存问题，直接修复")
    
    success = direct_config_fix()
    
    if success:
        logger.success("🎊 直接配置修复成功")
        logger.success("💡 建议：重启Celery服务使修复永久生效")
        return 0
    else:
        logger.error("❌ 直接配置修复失败")
        logger.error("🔧 建议：手动重启Celery服务")
        return 1


if __name__ == "__main__":
    exit(main())
