#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 千川自动化系统综合健康监控工具
清理条件: 系统运行期间需要持续使用，建议永久保留
"""

import os
import sys
import json
import psutil
from datetime import datetime, timezone, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import LocalCreative, Campaign, AdAccount, Principal


@dataclass
class HealthCheckResult:
    """健康检查结果"""
    component: str
    status: str  # HEALTHY, WARNING, CRITICAL, ERROR
    message: str
    details: Dict[str, Any]
    timestamp: datetime
    severity_score: int  # 0-100, 100最严重


@dataclass
class SystemHealthReport:
    """系统健康报告"""
    overall_status: str
    overall_score: int
    check_results: List[HealthCheckResult]
    recommendations: List[str]
    critical_issues: List[str]
    generated_at: datetime


class ComprehensiveHealthMonitor:
    """综合健康监控器"""
    
    def __init__(self):
        self.check_results = []
        self.critical_issues = []
        self.recommendations = []
        
    def run_comprehensive_health_check(self) -> SystemHealthReport:
        """运行综合健康检查"""
        logger.info("🏥 开始综合系统健康检查...")
        
        self.check_results = []
        self.critical_issues = []
        self.recommendations = []
        
        # 1. 数据库健康检查
        self._check_database_health()
        
        # 2. 工作流状态健康检查
        self._check_workflow_health()
        
        # 3. 业务铁律合规性检查
        self._check_business_rules_compliance()
        
        # 4. 系统资源健康检查
        self._check_system_resources()
        
        # 5. 文件系统健康检查
        self._check_file_system_health()
        
        # 6. 配置一致性检查
        self._check_configuration_consistency()
        
        # 生成综合报告
        return self._generate_health_report()
    
    def _check_database_health(self):
        """检查数据库健康状态"""
        logger.info("🗄️ 检查数据库健康状态...")
        
        try:
            with database_session() as db:
                from sqlalchemy import text, func

                # 检查数据库连接
                db.execute(text("SELECT 1"))

                # 检查关键表的记录数
                principals_count = db.query(Principal).count()
                accounts_count = db.query(AdAccount).count()
                creatives_count = db.query(LocalCreative).count()
                campaigns_count = db.query(Campaign).count()

                # 检查状态分布
                status_distribution = {}
                for status, count in db.query(LocalCreative.status, func.count(LocalCreative.id)).group_by(LocalCreative.status).all():
                    status_distribution[status] = count
                
                # 检查是否有卡住的状态
                stuck_processing = db.query(LocalCreative).filter(
                    LocalCreative.status == 'processing',
                    LocalCreative.updated_at < datetime.now(timezone.utc) - timedelta(hours=2)
                ).count()
                
                details = {
                    'connection_status': 'connected',
                    'principals_count': principals_count,
                    'accounts_count': accounts_count,
                    'creatives_count': creatives_count,
                    'campaigns_count': campaigns_count,
                    'status_distribution': status_distribution,
                    'stuck_processing_count': stuck_processing
                }
                
                # 评估健康状态
                if stuck_processing > 10:
                    status = 'CRITICAL'
                    message = f"发现{stuck_processing}个卡住的processing状态素材"
                    severity = 80
                    self.critical_issues.append(message)
                    self.recommendations.append("运行状态重置工具修复卡住的素材")
                elif stuck_processing > 0:
                    status = 'WARNING'
                    message = f"发现{stuck_processing}个可能卡住的processing状态素材"
                    severity = 40
                    self.recommendations.append("监控processing状态素材，必要时进行重置")
                else:
                    status = 'HEALTHY'
                    message = "数据库状态正常"
                    severity = 0
                
                self.check_results.append(HealthCheckResult(
                    component='database',
                    status=status,
                    message=message,
                    details=details,
                    timestamp=datetime.now(timezone.utc),
                    severity_score=severity
                ))
                
        except Exception as e:
            self.check_results.append(HealthCheckResult(
                component='database',
                status='ERROR',
                message=f"数据库检查失败: {str(e)}",
                details={'error': str(e)},
                timestamp=datetime.now(timezone.utc),
                severity_score=100
            ))
            self.critical_issues.append(f"数据库连接失败: {str(e)}")
    
    def _check_workflow_health(self):
        """检查工作流健康状态"""
        logger.info("🔄 检查工作流健康状态...")
        
        try:
            with database_session() as db:
                from sqlalchemy import func

                # 检查各个工作流阶段的素材数量
                workflow_stats = {
                    'new': db.query(LocalCreative).filter(LocalCreative.status == 'new').count(),
                    'pending_grouping': db.query(LocalCreative).filter(LocalCreative.status == 'pending_grouping').count(),
                    'processing': db.query(LocalCreative).filter(LocalCreative.status == 'processing').count(),
                    'uploaded_pending_plan': db.query(LocalCreative).filter(LocalCreative.status == 'uploaded_pending_plan').count(),
                    'testing_pending_review': db.query(LocalCreative).filter(LocalCreative.status == 'testing_pending_review').count(),
                    'approved': db.query(LocalCreative).filter(LocalCreative.status == 'approved').count(),
                    'harvested': db.query(LocalCreative).filter(LocalCreative.status == 'harvested').count()
                }

                # 检查计划状态分布
                campaign_stats = {}
                for status, count in db.query(Campaign.status, func.count(Campaign.id)).group_by(Campaign.status).all():
                    campaign_stats[status] = count

                # 检查提审状态
                appeal_stats = {}
                for status, count in db.query(Campaign.appeal_status, func.count(Campaign.id)).group_by(Campaign.appeal_status).all():
                    appeal_stats[status or 'null'] = count
                
                details = {
                    'workflow_stats': workflow_stats,
                    'campaign_stats': campaign_stats,
                    'appeal_stats': appeal_stats
                }
                
                # 评估工作流健康状态
                issues = []
                severity = 0
                
                # 检查是否有大量素材堆积
                if workflow_stats['uploaded_pending_plan'] > 50:
                    issues.append(f"有{workflow_stats['uploaded_pending_plan']}个素材等待创建计划")
                    severity = max(severity, 60)
                
                if workflow_stats['testing_pending_review'] > 100:
                    issues.append(f"有{workflow_stats['testing_pending_review']}个素材等待审核")
                    severity = max(severity, 40)
                
                # 检查是否有长时间未处理的素材
                old_pending = db.query(LocalCreative).filter(
                    LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review']),
                    LocalCreative.updated_at < datetime.now(timezone.utc) - timedelta(days=3)
                ).count()
                
                if old_pending > 0:
                    issues.append(f"有{old_pending}个素材超过3天未处理")
                    severity = max(severity, 70)
                
                if issues:
                    status = 'CRITICAL' if severity >= 70 else 'WARNING'
                    message = '; '.join(issues)
                    if severity >= 70:
                        self.critical_issues.extend(issues)
                    self.recommendations.append("检查Celery任务调度是否正常运行")
                else:
                    status = 'HEALTHY'
                    message = "工作流状态正常"
                
                self.check_results.append(HealthCheckResult(
                    component='workflow',
                    status=status,
                    message=message,
                    details=details,
                    timestamp=datetime.now(timezone.utc),
                    severity_score=severity
                ))
                
        except Exception as e:
            self.check_results.append(HealthCheckResult(
                component='workflow',
                status='ERROR',
                message=f"工作流检查失败: {str(e)}",
                details={'error': str(e)},
                timestamp=datetime.now(timezone.utc),
                severity_score=90
            ))
    
    def _check_business_rules_compliance(self):
        """检查业务铁律合规性"""
        logger.info("⚖️ 检查业务铁律合规性...")
        
        try:
            with database_session() as db:
                from sqlalchemy import text

                violations = []

                # 检查账户类型分离（铁律1）
                # UNSET是合法的账户类型，用于API同步数据，不参与工作流
                invalid_accounts = db.execute(text("""
                    SELECT aa.account_id_qc, aa.name, aa.account_type
                    FROM ad_accounts aa
                    WHERE aa.account_type NOT IN ('TEST', 'DELIVERY', 'UNSET')
                    OR aa.account_type IS NULL
                """)).fetchall()

                if invalid_accounts:
                    violations.append(f"发现{len(invalid_accounts)}个账户类型不规范")

                # 检查素材唯一性约束（铁律2）
                # 排除已标记为重复的计划
                duplicate_test_materials = db.execute(text("""
                    SELECT lc.file_hash, COUNT(DISTINCT c.id) as plan_count
                    FROM local_creatives lc
                    JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    JOIN ad_accounts aa ON pc.account_id = aa.id
                    JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                    JOIN campaigns c ON cpca.campaign_id = c.id
                    WHERE aa.account_type = 'TEST'
                      AND c.status != 'DUPLICATE_MARKED_FOR_REVIEW'
                    GROUP BY lc.file_hash
                    HAVING COUNT(DISTINCT c.id) > 1
                """)).fetchall()

                if duplicate_test_materials:
                    violations.append(f"发现{len(duplicate_test_materials)}个素材违反唯一性约束")

                # 检查重复提审（铁律4）
                duplicate_appeals = db.execute(text("""
                    SELECT campaign_id_qc
                    FROM campaigns
                    WHERE appeal_status = 'appeal_pending'
                    AND first_appeal_at IS NOT NULL
                    AND first_appeal_at != last_appeal_at
                """)).fetchall()
                
                if duplicate_appeals:
                    violations.append(f"发现{len(duplicate_appeals)}个计划可能重复提审")
                
                details = {
                    'invalid_accounts_count': len(invalid_accounts),
                    'duplicate_materials_count': len(duplicate_test_materials),
                    'duplicate_appeals_count': len(duplicate_appeals),
                    'violations': violations
                }
                
                if violations:
                    status = 'CRITICAL'
                    message = f"发现{len(violations)}项业务铁律违规"
                    severity = 90
                    self.critical_issues.extend(violations)
                    self.recommendations.append("立即修复业务铁律违规问题")
                else:
                    status = 'HEALTHY'
                    message = "业务铁律合规性检查通过"
                    severity = 0
                
                self.check_results.append(HealthCheckResult(
                    component='business_rules',
                    status=status,
                    message=message,
                    details=details,
                    timestamp=datetime.now(timezone.utc),
                    severity_score=severity
                ))
                
        except Exception as e:
            self.check_results.append(HealthCheckResult(
                component='business_rules',
                status='ERROR',
                message=f"业务规则检查失败: {str(e)}",
                details={'error': str(e)},
                timestamp=datetime.now(timezone.utc),
                severity_score=85
            ))
    
    def _check_system_resources(self):
        """检查系统资源"""
        logger.info("💻 检查系统资源...")

        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)

            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent

            # 磁盘使用率 - Windows系统使用C盘
            disk = psutil.disk_usage('C:' if os.name == 'nt' else '/')
            disk_percent = disk.percent

            # 进程数量
            process_count = len(psutil.pids())
            
            details = {
                'cpu_percent': cpu_percent,
                'memory_percent': memory_percent,
                'memory_total_gb': round(memory.total / (1024**3), 2),
                'memory_available_gb': round(memory.available / (1024**3), 2),
                'disk_percent': disk_percent,
                'disk_total_gb': round(disk.total / (1024**3), 2),
                'disk_free_gb': round(disk.free / (1024**3), 2),
                'process_count': process_count
            }
            
            # 评估资源状态
            issues = []
            severity = 0
            
            if cpu_percent > 85:
                issues.append(f"CPU使用率过高: {cpu_percent}%")
                severity = max(severity, 70)
            elif cpu_percent > 70:
                issues.append(f"CPU使用率较高: {cpu_percent}%")
                severity = max(severity, 40)
            
            if memory_percent > 90:
                issues.append(f"内存使用率过高: {memory_percent}%")
                severity = max(severity, 80)
            elif memory_percent > 75:
                issues.append(f"内存使用率较高: {memory_percent}%")
                severity = max(severity, 50)
            
            if disk_percent > 90:
                issues.append(f"磁盘使用率过高: {disk_percent}%")
                severity = max(severity, 75)
            elif disk_percent > 80:
                issues.append(f"磁盘使用率较高: {disk_percent}%")
                severity = max(severity, 45)
            
            if issues:
                status = 'CRITICAL' if severity >= 70 else 'WARNING'
                message = '; '.join(issues)
                if severity >= 70:
                    self.critical_issues.extend(issues)
                self.recommendations.append("监控系统资源使用情况，必要时进行优化")
            else:
                status = 'HEALTHY'
                message = "系统资源使用正常"
            
            self.check_results.append(HealthCheckResult(
                component='system_resources',
                status=status,
                message=message,
                details=details,
                timestamp=datetime.now(timezone.utc),
                severity_score=severity
            ))
            
        except Exception as e:
            self.check_results.append(HealthCheckResult(
                component='system_resources',
                status='ERROR',
                message=f"系统资源检查失败: {str(e)}",
                details={'error': str(e)},
                timestamp=datetime.now(timezone.utc),
                severity_score=60
            ))
    
    def _check_file_system_health(self):
        """检查文件系统健康状态"""
        logger.info("📁 检查文件系统健康状态...")
        
        try:
            issues = []
            severity = 0
            
            # 检查关键目录
            critical_dirs = [
                'workflow_assets',
                'logs',
                'ai_temp',
                'ai_tools',
                'ai_reports'
            ]
            
            dir_stats = {}
            for dir_name in critical_dirs:
                dir_path = project_root / dir_name
                if dir_path.exists():
                    # 计算目录大小和文件数量
                    total_size = 0
                    file_count = 0
                    for root, dirs, files in os.walk(dir_path):
                        for file in files:
                            file_path = os.path.join(root, file)
                            try:
                                total_size += os.path.getsize(file_path)
                                file_count += 1
                            except (OSError, IOError):
                                continue
                    
                    dir_stats[dir_name] = {
                        'exists': True,
                        'size_mb': round(total_size / (1024*1024), 2),
                        'file_count': file_count
                    }
                    
                    # 检查是否有异常大的目录
                    if total_size > 10 * 1024 * 1024 * 1024:  # 10GB
                        issues.append(f"{dir_name}目录过大: {dir_stats[dir_name]['size_mb']}MB")
                        severity = max(severity, 50)
                else:
                    dir_stats[dir_name] = {'exists': False}
                    if dir_name in ['workflow_assets', 'logs']:
                        issues.append(f"关键目录不存在: {dir_name}")
                        severity = max(severity, 60)
            
            # 检查AI临时文件清理
            ai_temp_path = project_root / 'ai_temp'
            if ai_temp_path.exists():
                old_files = []
                cutoff_time = datetime.now() - timedelta(days=7)
                
                for file_path in ai_temp_path.rglob('*'):
                    if file_path.is_file():
                        try:
                            file_mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                            if file_mtime < cutoff_time:
                                old_files.append(str(file_path))
                        except (OSError, IOError):
                            continue
                
                if len(old_files) > 100:
                    issues.append(f"AI临时目录有{len(old_files)}个过期文件需要清理")
                    severity = max(severity, 30)
                    self.recommendations.append("运行AI文件清理工具清理过期文件")
            
            details = {
                'directory_stats': dir_stats,
                'old_temp_files_count': len(old_files) if 'old_files' in locals() else 0
            }
            
            if issues:
                status = 'WARNING' if severity < 60 else 'CRITICAL'
                message = '; '.join(issues)
                if severity >= 60:
                    self.critical_issues.extend(issues)
            else:
                status = 'HEALTHY'
                message = "文件系统状态正常"
            
            self.check_results.append(HealthCheckResult(
                component='file_system',
                status=status,
                message=message,
                details=details,
                timestamp=datetime.now(timezone.utc),
                severity_score=severity
            ))
            
        except Exception as e:
            self.check_results.append(HealthCheckResult(
                component='file_system',
                status='ERROR',
                message=f"文件系统检查失败: {str(e)}",
                details={'error': str(e)},
                timestamp=datetime.now(timezone.utc),
                severity_score=50
            ))
    
    def _check_configuration_consistency(self):
        """检查配置一致性"""
        logger.info("⚙️ 检查配置一致性...")
        
        try:
            issues = []
            severity = 0
            
            # 检查关键配置文件
            config_files = [
                'config/settings.yml',
                'config/auth_tokens.json',
                'config/browser_management.yml'
            ]
            
            config_status = {}
            for config_file in config_files:
                config_path = project_root / config_file
                if config_path.exists():
                    config_status[config_file] = {
                        'exists': True,
                        'size': config_path.stat().st_size,
                        'modified': datetime.fromtimestamp(config_path.stat().st_mtime).isoformat()
                    }
                else:
                    config_status[config_file] = {'exists': False}
                    issues.append(f"配置文件不存在: {config_file}")
                    severity = max(severity, 80)
            
            details = {
                'config_files_status': config_status
            }
            
            if issues:
                status = 'CRITICAL'
                message = '; '.join(issues)
                self.critical_issues.extend(issues)
                self.recommendations.append("检查并恢复缺失的配置文件")
            else:
                status = 'HEALTHY'
                message = "配置文件完整"
            
            self.check_results.append(HealthCheckResult(
                component='configuration',
                status=status,
                message=message,
                details=details,
                timestamp=datetime.now(timezone.utc),
                severity_score=severity
            ))
            
        except Exception as e:
            self.check_results.append(HealthCheckResult(
                component='configuration',
                status='ERROR',
                message=f"配置检查失败: {str(e)}",
                details={'error': str(e)},
                timestamp=datetime.now(timezone.utc),
                severity_score=70
            ))
    
    def _generate_health_report(self) -> SystemHealthReport:
        """生成健康报告"""
        
        # 计算总体健康分数
        if not self.check_results:
            overall_score = 0
            overall_status = 'ERROR'
        else:
            total_severity = sum(result.severity_score for result in self.check_results)
            max_possible_severity = len(self.check_results) * 100
            overall_score = max(0, 100 - (total_severity / max_possible_severity * 100))
            
            # 确定总体状态
            if overall_score >= 80:
                overall_status = 'HEALTHY'
            elif overall_score >= 60:
                overall_status = 'WARNING'
            else:
                overall_status = 'CRITICAL'
        
        # 添加通用建议
        if overall_score < 80:
            self.recommendations.append("建议立即处理发现的问题以提升系统健康度")
        
        if self.critical_issues:
            self.recommendations.append("优先处理Critical级别的问题")
        
        return SystemHealthReport(
            overall_status=overall_status,
            overall_score=int(overall_score),
            check_results=self.check_results,
            recommendations=self.recommendations,
            critical_issues=self.critical_issues,
            generated_at=datetime.now(timezone.utc)
        )


def main():
    """主函数"""
    monitor = ComprehensiveHealthMonitor()
    
    try:
        report = monitor.run_comprehensive_health_check()
        
        # 保存报告
        report_file = project_root / 'ai_reports' / 'health' / f'health_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        report_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(asdict(report), f, indent=2, ensure_ascii=False, default=str)
        
        # 输出报告摘要
        print("\n" + "="*80)
        print("🏥 千川自动化系统健康检查报告")
        print("="*80)
        print(f"总体状态: {report.overall_status}")
        print(f"健康分数: {report.overall_score}/100")
        print(f"检查时间: {report.generated_at.strftime('%Y-%m-%d %H:%M:%S')}")
        
        if report.critical_issues:
            print(f"\n🚨 Critical问题 ({len(report.critical_issues)}个):")
            for issue in report.critical_issues:
                print(f"  - {issue}")
        
        print(f"\n📋 组件检查结果:")
        for result in report.check_results:
            status_emoji = {
                'HEALTHY': '✅',
                'WARNING': '⚠️',
                'CRITICAL': '🚨',
                'ERROR': '❌'
            }.get(result.status, '❓')
            
            print(f"  {status_emoji} {result.component}: {result.message}")
        
        if report.recommendations:
            print(f"\n💡 建议 ({len(report.recommendations)}个):")
            for i, rec in enumerate(report.recommendations, 1):
                print(f"  {i}. {rec}")
        
        print(f"\n📄 详细报告已保存到: {report_file}")
        
        return 0 if report.overall_status in ['HEALTHY', 'WARNING'] else 1
        
    except Exception as e:
        logger.error(f"健康检查过程发生错误: {e}", exc_info=True)
        return 1


if __name__ == "__main__":
    exit(main())
