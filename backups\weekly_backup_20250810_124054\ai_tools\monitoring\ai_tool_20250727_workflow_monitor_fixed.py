#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 千川自动化工作流监控工具（修复版）
清理条件: 系统不再需要工作流监控时删除

千川自动化工作流监控工具（修复版）
==============================

监控修复后的工作流状态，确保各环节正常运行
"""

import os
import sys
from datetime import datetime, timezone
from typing import Dict, List, Any

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.database import SessionLocal
from qianchuan_aw.database.models import LocalCreative, Campaign, PlatformCreative
from contextlib import contextmanager

@contextmanager
def database_session():
    """数据库会话上下文管理器"""
    session = SessionLocal()
    try:
        yield session
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()


class WorkflowMonitor:
    """工作流监控器"""
    
    def __init__(self):
        self.monitor_start_time = datetime.now()
    
    def monitor_material_status(self, db):
        """监控素材状态分布"""
        logger.info("📊 监控素材状态分布...")
        
        from sqlalchemy import text
        result = db.execute(text("""
            SELECT 
                status,
                COUNT(*) as count,
                COUNT(DISTINCT file_hash) as unique_count
            FROM local_creatives 
            GROUP BY status 
            ORDER BY count DESC
        """))
        
        status_stats = {}
        total_materials = 0
        
        for row in result.fetchall():
            status, count, unique_count = row
            status_stats[status] = {
                'count': count,
                'unique_count': unique_count
            }
            total_materials += count
        
        logger.info(f"📋 素材状态分布 (总计: {total_materials}个):")
        for status, stats in status_stats.items():
            percentage = (stats['count'] / total_materials * 100) if total_materials > 0 else 0
            logger.info(f"   {status}: {stats['count']}个 ({percentage:.1f}%) | 去重: {stats['unique_count']}个")
        
        return status_stats
    
    def monitor_appeal_status(self, db):
        """监控申诉状态"""
        logger.info("📊 监控申诉状态...")
        
        from sqlalchemy import text
        result = db.execute(text("""
            SELECT 
                appeal_status,
                COUNT(*) as count,
                AVG(appeal_attempt_count) as avg_attempts,
                COUNT(CASE WHEN appeal_attempt_count > 0 THEN 1 END) as attempted_count
            FROM campaigns 
            WHERE appeal_status IS NOT NULL
            GROUP BY appeal_status 
            ORDER BY count DESC
        """))
        
        appeal_stats = {}
        total_appeals = 0
        
        for row in result.fetchall():
            appeal_status, count, avg_attempts, attempted_count = row
            appeal_stats[appeal_status] = {
                'count': count,
                'avg_attempts': float(avg_attempts) if avg_attempts else 0,
                'attempted_count': attempted_count
            }
            total_appeals += count
        
        logger.info(f"📋 申诉状态分布 (总计: {total_appeals}个):")
        for status, stats in appeal_stats.items():
            percentage = (stats['count'] / total_appeals * 100) if total_appeals > 0 else 0
            logger.info(f"   {status}: {stats['count']}个 ({percentage:.1f}%) | 平均申诉次数: {stats['avg_attempts']:.1f}")
        
        return appeal_stats
    
    def monitor_workflow_health(self, db):
        """监控工作流健康状态"""
        logger.info("🔍 监控工作流健康状态...")
        
        # 检查各个环节的处理能力
        from sqlalchemy import text
        
        # 1. 检查待处理积压
        pending_stats = db.execute(text("""
            SELECT 
                'pending_upload' as stage,
                COUNT(*) as count
            FROM local_creatives 
            WHERE status = 'pending_upload'
            
            UNION ALL
            
            SELECT 
                'uploaded_pending_plan' as stage,
                COUNT(*) as count
            FROM local_creatives 
            WHERE status = 'uploaded_pending_plan'
            
            UNION ALL
            
            SELECT 
                'appeal_pending' as stage,
                COUNT(*) as count
            FROM campaigns 
            WHERE appeal_status = 'appeal_pending'
        """)).fetchall()
        
        # 2. 检查最近活动
        recent_activity = db.execute(text("""
            SELECT 
                'materials_created_today' as activity,
                COUNT(*) as count
            FROM local_creatives 
            WHERE created_at > CURRENT_DATE
            
            UNION ALL
            
            SELECT 
                'plans_created_today' as activity,
                COUNT(*) as count
            FROM campaigns 
            WHERE created_at > CURRENT_DATE
            
            UNION ALL
            
            SELECT 
                'appeals_started_today' as activity,
                COUNT(*) as count
            FROM campaigns 
            WHERE appeal_started_at > CURRENT_DATE
        """)).fetchall()
        
        logger.info("📈 工作流积压情况:")
        for stage, count in pending_stats:
            if count > 0:
                logger.warning(f"   {stage}: {count}个待处理")
            else:
                logger.success(f"   {stage}: 无积压")
        
        logger.info("📈 今日活动统计:")
        for activity, count in recent_activity:
            logger.info(f"   {activity}: {count}个")
        
        return dict(pending_stats), dict(recent_activity)
    
    def monitor_business_rules_compliance(self, db):
        """监控业务规则遵循情况"""
        logger.info("🛡️ 监控业务规则遵循情况...")
        
        from sqlalchemy import text
        
        # 检查素材唯一性
        uniqueness_violations = db.execute(text("""
            SELECT COUNT(*) as violations
            FROM (
                SELECT lc.file_hash, COUNT(DISTINCT c.id) as plan_count
                FROM local_creatives lc
                JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                JOIN campaigns c ON cpca.campaign_id = c.id
                JOIN ad_accounts aa ON c.account_id = aa.id
                WHERE aa.account_type = 'TEST'
                GROUP BY lc.file_hash
                HAVING COUNT(DISTINCT c.id) > 1
            ) violations
        """)).scalar()
        
        # 检查申诉一次性
        multiple_appeals = db.execute(text("""
            SELECT COUNT(*) as violations
            FROM campaigns 
            WHERE appeal_attempt_count > 1
        """)).scalar()
        
        # 检查收割精确控制
        premature_harvests = db.execute(text("""
            SELECT COUNT(*) as violations
            FROM local_creatives lc
            JOIN platform_creatives pc ON lc.id = pc.local_creative_id
            JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
            JOIN campaigns c ON cpca.campaign_id = c.id
            WHERE lc.status = 'rejected'
            AND c.appeal_status IN ('appeal_submitted', 'appeal_pending')
            AND c.appeal_completed_at IS NULL
        """)).scalar()
        
        logger.info("🛡️ 业务规则遵循检查:")
        if uniqueness_violations == 0:
            logger.success("   ✅ 素材唯一性测试铁律: 遵循")
        else:
            logger.error(f"   ❌ 素材唯一性测试铁律: {uniqueness_violations}个违规")
        
        if multiple_appeals == 0:
            logger.success("   ✅ 申诉提审一次性原则: 遵循")
        else:
            logger.error(f"   ❌ 申诉提审一次性原则: {multiple_appeals}个违规")
        
        if premature_harvests == 0:
            logger.success("   ✅ 收割动作精确控制: 遵循")
        else:
            logger.error(f"   ❌ 收割动作精确控制: {premature_harvests}个违规")
        
        return {
            'uniqueness_violations': uniqueness_violations,
            'multiple_appeals': multiple_appeals,
            'premature_harvests': premature_harvests
        }
    
    def run_comprehensive_monitoring(self):
        """运行全面监控"""
        logger.info("🔍 开始千川自动化工作流全面监控...")
        
        with database_session() as db:
            # 1. 监控素材状态
            material_stats = self.monitor_material_status(db)
            
            # 2. 监控申诉状态
            appeal_stats = self.monitor_appeal_status(db)
            
            # 3. 监控工作流健康
            pending_stats, activity_stats = self.monitor_workflow_health(db)
            
            # 4. 监控业务规则遵循
            compliance_stats = self.monitor_business_rules_compliance(db)
        
        # 生成监控报告
        self.generate_monitoring_report(material_stats, appeal_stats, pending_stats, activity_stats, compliance_stats)
        
        return {
            'material_stats': material_stats,
            'appeal_stats': appeal_stats,
            'pending_stats': pending_stats,
            'activity_stats': activity_stats,
            'compliance_stats': compliance_stats
        }
    
    def generate_monitoring_report(self, material_stats, appeal_stats, pending_stats, activity_stats, compliance_stats):
        """生成监控报告"""
        monitor_duration = (datetime.now() - self.monitor_start_time).total_seconds()
        
        logger.info("=" * 60)
        logger.info("📊 千川自动化工作流监控报告")
        logger.info("=" * 60)
        logger.info(f"监控时间: {self.monitor_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"监控耗时: {monitor_duration:.2f} 秒")
        
        # 总体健康评分
        total_violations = sum(compliance_stats.values())
        total_pending = sum(pending_stats.values())
        
        if total_violations == 0 and total_pending < 100:
            health_score = "优秀"
            health_icon = "🟢"
        elif total_violations == 0 and total_pending < 500:
            health_score = "良好"
            health_icon = "🟡"
        else:
            health_score = "需要关注"
            health_icon = "🔴"
        
        logger.info(f"工作流健康状态: {health_icon} {health_score}")
        
        # 关键指标
        harvested_count = material_stats.get('harvested', {}).get('count', 0)
        appeal_pending_count = appeal_stats.get('appeal_pending', {}).get('count', 0)
        
        logger.info("📈 关键指标:")
        logger.info(f"   弹药库素材: {harvested_count}个")
        logger.info(f"   待申诉计划: {appeal_pending_count}个")
        logger.info(f"   业务规则违规: {total_violations}个")
        logger.info(f"   工作流积压: {total_pending}个")
        
        logger.info("=" * 60)


def main():
    """主函数"""
    monitor = WorkflowMonitor()
    
    print("📊 千川自动化工作流监控工具（修复版）")
    print("=" * 50)
    print("监控内容:")
    print("1. 素材状态分布")
    print("2. 申诉状态统计")
    print("3. 工作流健康状态")
    print("4. 业务规则遵循情况")
    print("=" * 50)
    
    # 运行全面监控
    monitoring_result = monitor.run_comprehensive_monitoring()
    
    return 0


if __name__ == '__main__':
    exit(main())
