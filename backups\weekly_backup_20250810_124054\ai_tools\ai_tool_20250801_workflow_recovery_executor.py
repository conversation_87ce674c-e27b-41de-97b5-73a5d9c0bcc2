"""
千川自动化工作流恢复执行器
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 执行具体的工作流恢复动作，确保系统能够自动恢复到正常状态
清理条件: 永不删除，持续维护优化
"""

import os
import sys
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import LocalCreative, PlatformCreative, Campaign, AdAccount
from qianchuan_aw.utils.workflow_status import WorkflowStatus
from qianchuan_aw.workflows.scheduler import (
    process_single_video_upload,
    handle_plan_creation,
    handle_plan_submission,
    handle_plans_awaiting_appeal,
    handle_independent_material_harvest
)


class WorkflowRecoveryExecutor:
    """工作流恢复执行器"""
    
    def __init__(self, app_settings: Dict[str, Any]):
        self.app_settings = app_settings
        self.recovery_log = []
        
    def execute_recovery_plan(self, recovery_actions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """执行恢复计划"""
        logger.info("🔧 开始执行工作流恢复计划...")
        
        results = {
            'total_actions': len(recovery_actions),
            'successful_actions': 0,
            'failed_actions': 0,
            'details': []
        }
        
        for i, action in enumerate(recovery_actions, 1):
            logger.info(f"执行恢复动作 {i}/{len(recovery_actions)}: {action['description']}")
            
            try:
                if action['action'] == 'resume_plan_creation':
                    result = self._execute_plan_creation_recovery()
                elif action['action'] == 'resume_appeal_submission':
                    result = self._execute_appeal_recovery()
                elif action['action'] == 'resume_upload_tasks':
                    result = self._execute_upload_recovery()
                elif action['action'] == 'resume_harvest':
                    result = self._execute_harvest_recovery()
                else:
                    result = {'success': False, 'error': f"未知的恢复动作: {action['action']}"}
                
                if result['success']:
                    results['successful_actions'] += 1
                    logger.success(f"✅ 恢复动作成功: {action['description']}")
                else:
                    results['failed_actions'] += 1
                    logger.error(f"❌ 恢复动作失败: {action['description']} - {result.get('error', 'Unknown error')}")
                
                results['details'].append({
                    'action': action['action'],
                    'description': action['description'],
                    'result': result
                })
                
            except Exception as e:
                results['failed_actions'] += 1
                error_msg = f"执行恢复动作时发生异常: {e}"
                logger.error(error_msg)
                results['details'].append({
                    'action': action['action'],
                    'description': action['description'],
                    'result': {'success': False, 'error': error_msg}
                })
        
        return results
    
    def _execute_plan_creation_recovery(self) -> Dict[str, Any]:
        """执行计划创建恢复"""
        logger.info("📋 执行计划创建恢复...")
        
        try:
            with database_session() as db:
                # 查找需要创建计划的素材
                pending_creatives = db.query(LocalCreative).filter(
                    LocalCreative.status == WorkflowStatus.UPLOADED_PENDING_PLAN.value,
                    LocalCreative.created_at >= datetime.now().date()
                ).all()
                
                if not pending_creatives:
                    return {'success': True, 'message': '没有需要创建计划的素材'}
                
                logger.info(f"发现 {len(pending_creatives)} 个素材需要创建计划")
                
                # 调用计划创建工作流
                handle_plan_creation(db, self.app_settings)
                
                # 检查创建结果
                remaining_creatives = db.query(LocalCreative).filter(
                    LocalCreative.status == WorkflowStatus.UPLOADED_PENDING_PLAN.value,
                    LocalCreative.created_at >= datetime.now().date()
                ).count()
                
                created_count = len(pending_creatives) - remaining_creatives
                
                return {
                    'success': True,
                    'message': f'成功为 {created_count} 个素材创建计划，剩余 {remaining_creatives} 个',
                    'created_count': created_count,
                    'remaining_count': remaining_creatives
                }
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _execute_appeal_recovery(self) -> Dict[str, Any]:
        """执行提审恢复"""
        logger.info("📤 执行提审恢复...")
        
        try:
            with database_session() as db:
                # 查找提审失败的计划
                failed_campaigns = db.query(Campaign).filter(
                    Campaign.appeal_status == 'submission_failed',
                    Campaign.created_at >= datetime.now().date()
                ).all()
                
                if not failed_campaigns:
                    return {'success': True, 'message': '没有需要重新提审的计划'}
                
                logger.info(f"发现 {len(failed_campaigns)} 个计划需要重新提审")
                
                # 重置提审状态
                for campaign in failed_campaigns:
                    campaign.appeal_status = 'appeal_pending'
                    campaign.appeal_attempt_count = 0
                    campaign.appeal_error_message = None
                    campaign.first_appeal_at = None
                    campaign.last_appeal_at = None
                
                db.commit()
                
                # 调用提审工作流
                handle_plan_submission(db, self.app_settings)
                handle_plans_awaiting_appeal(db, self.app_settings)
                
                # 检查提审结果
                still_failed = db.query(Campaign).filter(
                    Campaign.appeal_status == 'submission_failed',
                    Campaign.created_at >= datetime.now().date()
                ).count()
                
                submitted_count = len(failed_campaigns) - still_failed
                
                return {
                    'success': True,
                    'message': f'成功提审 {submitted_count} 个计划，失败 {still_failed} 个',
                    'submitted_count': submitted_count,
                    'failed_count': still_failed
                }
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _execute_upload_recovery(self) -> Dict[str, Any]:
        """执行上传恢复"""
        logger.info("📤 执行上传恢复...")
        
        try:
            with database_session() as db:
                # 查找上传失败的素材
                failed_creatives = db.query(LocalCreative).filter(
                    LocalCreative.status == 'upload_failed',
                    LocalCreative.created_at >= datetime.now().date()
                ).all()
                
                if not failed_creatives:
                    return {'success': True, 'message': '没有需要重新上传的素材'}
                
                logger.info(f"发现 {len(failed_creatives)} 个素材需要重新上传")
                
                # 重置上传状态
                for creative in failed_creatives:
                    creative.status = 'new'
                
                db.commit()
                
                # 触发上传任务（这里需要调用Celery任务）
                from qianchuan_aw.workflows.tasks import upload_single_video
                
                success_count = 0
                for creative in failed_creatives:
                    try:
                        # 异步提交上传任务
                        upload_single_video.delay(
                            creative.id,
                            creative.uploaded_to_account_id or 1,  # 默认账户ID
                            creative.file_path,
                            "缇萃百货"  # 默认主体名称
                        )
                        success_count += 1
                    except Exception as e:
                        logger.error(f"提交上传任务失败: {creative.filename} - {e}")
                
                return {
                    'success': True,
                    'message': f'成功提交 {success_count} 个上传任务',
                    'submitted_count': success_count,
                    'total_count': len(failed_creatives)
                }
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _execute_harvest_recovery(self) -> Dict[str, Any]:
        """执行收割恢复"""
        logger.info("🌾 执行收割恢复...")
        
        try:
            with database_session() as db:
                # 调用收割检查工作流
                handle_independent_material_harvest(db, self.app_settings)
                
                return {
                    'success': True,
                    'message': '收割检查工作流已执行'
                }
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def create_auto_recovery_scheduler(self) -> str:
        """创建自动恢复调度器脚本"""
        logger.info("⏰ 创建自动恢复调度器...")
        
        scheduler_script = '''"""
千川自动化工作流自动恢复调度器
每5分钟检查一次系统状态，自动执行恢复动作
"""

import schedule
import time
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

from ai_tool_20250801_workflow_resilience_system import WorkflowResilienceSystem
from ai_tool_20250801_workflow_recovery_executor import WorkflowRecoveryExecutor
from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.config_manager import get_config


def auto_recovery_check():
    """自动恢复检查"""
    try:
        logger.info("🔍 执行自动恢复检查...")
        
        app_settings = get_config()
        
        # 创建系统检查点
        recovery_system = WorkflowResilienceSystem(app_settings)
        checkpoints = recovery_system.create_system_checkpoint()
        
        # 生成恢复计划
        recovery_plan = recovery_system.generate_recovery_plan(checkpoints)
        
        if recovery_plan:
            logger.info(f"发现 {len(recovery_plan)} 个恢复动作需要执行")
            
            # 执行恢复
            executor = WorkflowRecoveryExecutor(app_settings)
            results = executor.execute_recovery_plan(recovery_plan)
            
            logger.info(f"恢复执行完成: 成功 {results['successful_actions']}, 失败 {results['failed_actions']}")
        else:
            logger.info("✅ 系统状态正常，无需恢复")
            
    except Exception as e:
        logger.error(f"自动恢复检查失败: {e}")


def main():
    """主函数"""
    logger.info("🚀 启动千川自动化工作流自动恢复调度器...")
    
    # 每5分钟执行一次恢复检查
    schedule.every(5).minutes.do(auto_recovery_check)
    
    # 立即执行一次
    auto_recovery_check()
    
    logger.info("⏰ 调度器已启动，每5分钟检查一次...")
    
    while True:
        schedule.run_pending()
        time.sleep(30)  # 每30秒检查一次调度


if __name__ == "__main__":
    main()
'''
        
        scheduler_file = "ai_tool_20250801_auto_recovery_scheduler.py"
        with open(scheduler_file, 'w', encoding='utf-8') as f:
            f.write(scheduler_script)
        
        logger.success(f"✅ 自动恢复调度器已创建: {scheduler_file}")
        return scheduler_file


def main():
    """主函数"""
    logger.info("🔧 启动工作流恢复执行器...")
    
    # 获取app_settings
    from qianchuan_aw.utils.config_manager import get_settings
    app_settings = get_settings()
    
    # 创建恢复执行器
    executor = WorkflowRecoveryExecutor(app_settings)
    
    # 创建自动恢复调度器
    scheduler_file = executor.create_auto_recovery_scheduler()
    
    logger.info("💡 使用方法:")
    logger.info(f"1. 运行自动调度器: python {scheduler_file}")
    logger.info("2. 或手动执行恢复: python ai_tool_20250801_workflow_resilience_system.py")


if __name__ == "__main__":
    main()
