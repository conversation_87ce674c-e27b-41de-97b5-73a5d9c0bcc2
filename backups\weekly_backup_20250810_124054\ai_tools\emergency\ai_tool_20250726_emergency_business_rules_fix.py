#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 紧急修复业务规则违反问题
清理条件: 问题解决后可删除

业务规则违反紧急修复工具
======================

修复三个关键业务规则违反：
1. 一个素材只能创建一次测试计划
2. 申诉发送成功后不再重复申诉
3. 收割规则正确执行
"""

import os
import sys
from datetime import datetime
from collections import defaultdict

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from src.qianchuan_aw.database.models import LocalCreative, PlatformCreative, Campaign, campaign_platform_creative_association


class BusinessRulesFixer:
    """业务规则修复器"""
    
    def __init__(self):
        self.project_root = project_root
        self.duplicate_plans = []
        self.fixed_count = 0
        
    def analyze_duplicate_plans(self):
        """分析重复计划问题"""
        logger.info("🔍 分析重复计划创建问题...")
        
        try:
            with database_session() as db:
                # 查找有多个计划的素材
                query = """
                SELECT 
                    lc.id as local_creative_id,
                    lc.filename,
                    lc.file_hash,
                    COUNT(DISTINCT c.id) as plan_count,
                    STRING_AGG(DISTINCT c.campaign_id_qc, ', ') as plan_ids,
                    MIN(c.created_at) as first_plan_created,
                    MAX(c.created_at) as last_plan_created
                FROM local_creatives lc
                JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                JOIN campaigns c ON cpca.campaign_id = c.id
                GROUP BY lc.id, lc.filename, lc.file_hash
                HAVING COUNT(DISTINCT c.id) > 1
                ORDER BY plan_count DESC
                """
                
                result = db.execute(query).fetchall()
                
                logger.info(f"📊 发现 {len(result)} 个素材违反了单计划规则")
                
                total_duplicate_plans = 0
                for row in result:
                    duplicate_count = row.plan_count - 1  # 减去应该保留的1个
                    total_duplicate_plans += duplicate_count
                    
                    self.duplicate_plans.append({
                        'local_creative_id': row.local_creative_id,
                        'filename': row.filename,
                        'file_hash': row.file_hash,
                        'plan_count': row.plan_count,
                        'duplicate_count': duplicate_count,
                        'plan_ids': row.plan_ids.split(', ') if row.plan_ids else [],
                        'first_created': row.first_plan_created,
                        'last_created': row.last_plan_created
                    })
                
                logger.info(f"📊 总计需要清理 {total_duplicate_plans} 个重复计划")
                
                # 显示最严重的违规案例
                logger.warning("⚠️ 最严重的违规案例:")
                for i, item in enumerate(self.duplicate_plans[:5]):
                    logger.warning(f"   {i+1}. {item['filename']}: {item['plan_count']} 个计划")
                
                return len(result) > 0
                
        except Exception as e:
            logger.error(f"❌ 分析重复计划失败: {e}")
            return False
    
    def fix_duplicate_plans(self, dry_run=True):
        """修复重复计划问题"""
        logger.info(f"🔧 {'预览' if dry_run else '执行'}重复计划修复...")
        
        if not self.duplicate_plans:
            logger.info("✅ 没有发现重复计划问题")
            return True
        
        try:
            with database_session() as db:
                for item in self.duplicate_plans:
                    logger.info(f"🔄 处理素材: {item['filename']} ({item['plan_count']} 个计划)")
                    
                    # 获取所有相关计划，按创建时间排序
                    campaigns = db.query(Campaign).join(
                        campaign_platform_creative_association
                    ).join(PlatformCreative).join(LocalCreative).filter(
                        LocalCreative.file_hash == item['file_hash']
                    ).order_by(Campaign.created_at.asc()).all()
                    
                    if not campaigns:
                        logger.warning(f"⚠️ 未找到素材 {item['filename']} 的计划")
                        continue
                    
                    # 保留第一个计划，删除其余的
                    keep_campaign = campaigns[0]
                    duplicate_campaigns = campaigns[1:]
                    
                    logger.info(f"   保留计划: {keep_campaign.campaign_id_qc} (创建于 {keep_campaign.created_at})")
                    logger.info(f"   删除计划: {len(duplicate_campaigns)} 个")
                    
                    if not dry_run:
                        # 删除重复计划
                        for dup_campaign in duplicate_campaigns:
                            try:
                                # 删除关联关系
                                db.execute(
                                    campaign_platform_creative_association.delete().where(
                                        campaign_platform_creative_association.c.campaign_id == dup_campaign.id
                                    )
                                )
                                
                                # 删除计划
                                db.delete(dup_campaign)
                                
                                logger.info(f"   ✅ 已删除重复计划: {dup_campaign.campaign_id_qc}")
                                self.fixed_count += 1
                                
                            except Exception as e:
                                logger.error(f"   ❌ 删除计划失败 {dup_campaign.campaign_id_qc}: {e}")
                        
                        # 确保素材状态正确
                        local_creative = db.query(LocalCreative).filter(
                            LocalCreative.file_hash == item['file_hash']
                        ).first()
                        
                        if local_creative and local_creative.status != 'already_tested':
                            local_creative.status = 'already_tested'
                            logger.info(f"   ✅ 更新素材状态为 already_tested")
                
                if not dry_run:
                    db.commit()
                    logger.info(f"🎯 重复计划修复完成！删除了 {self.fixed_count} 个重复计划")
                else:
                    logger.info(f"📋 预览完成，将删除 {sum(item['duplicate_count'] for item in self.duplicate_plans)} 个重复计划")
                
                return True
                
        except Exception as e:
            logger.error(f"❌ 修复重复计划失败: {e}")
            return False
    
    def strengthen_duplicate_prevention(self):
        """加强重复创建防护"""
        logger.info("🔧 加强重复创建防护机制...")
        
        try:
            # 创建数据库唯一约束（如果不存在）
            constraint_sql = """
            -- 为 local_creatives 表的 file_hash 添加唯一索引（如果不存在）
            CREATE UNIQUE INDEX IF NOT EXISTS idx_local_creatives_file_hash_unique 
            ON local_creatives(file_hash);
            
            -- 为防止同一素材创建多个计划，添加复合唯一约束
            CREATE UNIQUE INDEX IF NOT EXISTS idx_campaign_creative_unique
            ON campaign_platform_creative_association(platform_creative_id);
            """
            
            with database_session() as db:
                # 注意：这些约束可能会因为现有重复数据而失败
                # 需要先清理重复数据
                logger.info("💡 建议：先清理重复数据，再添加数据库约束")
                
            return True
            
        except Exception as e:
            logger.error(f"❌ 加强防护机制失败: {e}")
            return False
    
    def verify_appeal_rules(self):
        """验证申诉规则遵循情况"""
        logger.info("🔍 验证申诉规则遵循情况...")
        
        try:
            with database_session() as db:
                # 检查申诉状态分布
                appeal_stats = db.execute("""
                    SELECT 
                        appeal_status,
                        COUNT(*) as count,
                        COUNT(CASE WHEN appeal_started_at IS NOT NULL THEN 1 END) as started,
                        COUNT(CASE WHEN appeal_completed_at IS NOT NULL THEN 1 END) as completed
                    FROM campaigns 
                    GROUP BY appeal_status
                    ORDER BY count DESC
                """).fetchall()
                
                logger.info("📊 申诉状态分布:")
                for stat in appeal_stats:
                    logger.info(f"   - {stat.appeal_status}: {stat.count} 个 (已开始: {stat.started}, 已完成: {stat.completed})")
                
                # 检查是否有重复申诉的情况
                duplicate_appeals = db.execute("""
                    SELECT campaign_id_qc, COUNT(*) as appeal_count
                    FROM campaigns 
                    WHERE appeal_started_at IS NOT NULL
                    GROUP BY campaign_id_qc
                    HAVING COUNT(*) > 1
                """).fetchall()
                
                if duplicate_appeals:
                    logger.warning(f"⚠️ 发现 {len(duplicate_appeals)} 个计划可能有重复申诉")
                    for dup in duplicate_appeals[:5]:
                        logger.warning(f"   - 计划 {dup.campaign_id_qc}: {dup.appeal_count} 次申诉")
                else:
                    logger.info("✅ 未发现重复申诉问题")
                
                return len(duplicate_appeals) == 0
                
        except Exception as e:
            logger.error(f"❌ 验证申诉规则失败: {e}")
            return False
    
    def verify_harvest_rules(self):
        """验证收割规则遵循情况"""
        logger.info("🔍 验证收割规则遵循情况...")
        
        try:
            with database_session() as db:
                # 检查收割状态
                harvest_stats = db.execute("""
                    SELECT 
                        harvest_status,
                        COUNT(*) as count
                    FROM local_creatives 
                    WHERE status = 'already_tested'
                    GROUP BY harvest_status
                """).fetchall()
                
                logger.info("📊 收割状态分布:")
                for stat in harvest_stats:
                    logger.info(f"   - {stat.harvest_status}: {stat.count} 个")
                
                # 检查是否有素材被重复收割到同一目录
                approved_base_dir = os.path.join('G:', 'workflow_assets', '03_materials_approved', '缇萃百货')
                
                if os.path.exists(approved_base_dir):
                    file_locations = defaultdict(list)
                    
                    for date_dir in os.listdir(approved_base_dir):
                        date_path = os.path.join(approved_base_dir, date_dir)
                        if os.path.isdir(date_path) and date_dir.startswith('2025-'):
                            try:
                                for filename in os.listdir(date_path):
                                    if filename.endswith('.mp4'):
                                        file_locations[filename].append(date_dir)
                            except:
                                continue
                    
                    # 检查重复收割
                    duplicates = {f: dates for f, dates in file_locations.items() if len(dates) > 1}
                    
                    if duplicates:
                        logger.warning(f"⚠️ 发现 {len(duplicates)} 个文件被重复收割")
                        for filename, dates in list(duplicates.items())[:5]:
                            logger.warning(f"   - {filename}: {', '.join(dates)}")
                        return False
                    else:
                        logger.info("✅ 未发现重复收割问题")
                        return True
                else:
                    logger.warning("⚠️ 收割目录不存在")
                    return False
                
        except Exception as e:
            logger.error(f"❌ 验证收割规则失败: {e}")
            return False
    
    def generate_fix_report(self):
        """生成修复报告"""
        report = f"""
业务规则违反修复报告
==================

修复时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🚨 发现的业务规则违反:

1. 计划创建规则违反:
   - 违反素材数量: {len(self.duplicate_plans)}
   - 重复计划总数: {sum(item['duplicate_count'] for item in self.duplicate_plans)}
   - 最严重案例: {self.duplicate_plans[0]['filename'] if self.duplicate_plans else 'N/A'} ({self.duplicate_plans[0]['plan_count'] if self.duplicate_plans else 0} 个计划)

2. 申诉规则检查: {'✅ 通过' if self.verify_appeal_rules() else '❌ 有问题'}

3. 收割规则检查: {'✅ 通过' if self.verify_harvest_rules() else '❌ 有问题'}

🔧 修复措施:
1. ✅ 分析了重复计划问题
2. {'✅ 清理了重复计划' if self.fixed_count > 0 else '⏳ 待执行重复计划清理'}
3. ✅ 验证了申诉和收割规则

📋 业务规则重申:
1. 一个素材只能创建一次测试计划 - 绝对不能重复
2. 申诉发送成功后不再重复申诉 - 只查询结果
3. 同一视频只收割一次 - 但可从多计划收割

🎯 下一步操作:
1. 执行重复计划清理（如未执行）
2. 加强数据库约束防止重复
3. 监控业务规则遵循情况
4. 定期审计数据一致性

⚠️ 重要提醒:
这些业务规则是系统稳定运行的基础，必须严格遵循！
"""
        
        return report


def main():
    """主函数"""
    logger.info("🚨 开始业务规则违反紧急修复...")
    
    fixer = BusinessRulesFixer()
    
    try:
        # 1. 分析重复计划问题
        logger.info("=" * 60)
        has_duplicates = fixer.analyze_duplicate_plans()
        
        if has_duplicates:
            # 2. 预览修复效果
            logger.info("=" * 60)
            fixer.fix_duplicate_plans(dry_run=True)
            
            # 询问是否执行修复
            print("\n" + "="*60)
            execute_fix = input("发现重复计划问题，是否执行修复？(y/n): ").strip().lower()
            
            if execute_fix == 'y':
                logger.info("=" * 60)
                fixer.fix_duplicate_plans(dry_run=False)
        
        # 3. 验证其他规则
        logger.info("=" * 60)
        fixer.verify_appeal_rules()
        
        logger.info("=" * 60)
        fixer.verify_harvest_rules()
        
        # 4. 生成报告
        logger.info("=" * 60)
        report = fixer.generate_fix_report()
        
        # 保存报告
        report_file = os.path.join(project_root, 'ai_temp', f'business_rules_fix_{int(__import__("time").time())}.txt')
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"✅ 修复报告已保存: {report_file}")
        print(report)
        
        logger.info("🎯 业务规则修复完成！")
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ 修复过程中发生错误: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
