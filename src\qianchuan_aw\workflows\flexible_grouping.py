#!/usr/bin/env python3
"""
灵活分组模块
支持6-9个视频创建计划，带超时机制
"""

from datetime import datetime, timedelta, timezone
from collections import defaultdict
from typing import List, Dict, Any
from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.models import LocalCreative, AdAccount

def create_flexible_groups(account_videos: List[LocalCreative], 
                          min_size: int = 6, 
                          max_size: int = 9) -> List[List[LocalCreative]]:
    """
    灵活分组机制：支持6-9个视频创建计划
    
    Args:
        account_videos: 账户下的视频列表
        min_size: 最小组大小
        max_size: 最大组大小
    
    Returns:
        分组后的视频列表
    """
    if not account_videos:
        return []
    
    groups = []
    current_group = []
    
    for video in account_videos:
        current_group.append(video)
        
        # 达到最大组大小或者是最后的视频
        if len(current_group) >= max_size or video == account_videos[-1]:
            if len(current_group) >= min_size:
                groups.append(current_group)
                current_group = []
            elif len(current_group) < min_size and video == account_videos[-1]:
                # 最后一组不足最小大小，合并到前一组
                if groups:
                    groups[-1].extend(current_group)
                    logger.info(f"最后一组不足{min_size}个，合并到前一组，最终组大小: {len(groups[-1])}")
                else:
                    # 如果只有一组且不足最小大小，仍然创建
                    if len(current_group) >= 3:  # 至少3个视频
                        groups.append(current_group)
                        logger.warning(f"仅有一组且不足{min_size}个，但超过3个，仍然创建: {len(current_group)}个视频")
                current_group = []
    
    logger.info(f"灵活分组完成: {len(account_videos)}个视频分为{len(groups)}组")
    for i, group in enumerate(groups):
        logger.info(f"  第{i+1}组: {len(group)}个视频")
    
    return groups

def check_grouping_timeout(db, timeout_hours: int = 1) -> List[LocalCreative]:
    """
    检查分组超时，返回需要强制创建计划的视频
    
    Args:
        db: 数据库会话
        timeout_hours: 超时小时数
    
    Returns:
        超时的视频列表
    """
    timeout_threshold = datetime.now(timezone.utc) - timedelta(hours=timeout_hours)
    
    stuck_videos = db.query(LocalCreative).filter(
        LocalCreative.status.in_(['pending_grouping', 'uploaded_pending_plan']),
        LocalCreative.updated_at < timeout_threshold
    ).all()
    
    if stuck_videos:
        logger.warning(f"发现 {len(stuck_videos)} 个视频分组超时，需要强制处理")
        
        # 按账户分组统计
        account_stats = defaultdict(int)
        for video in stuck_videos:
            # 这里需要通过platform_creatives表关联到账户
            # 简化处理，直接返回超时视频
            account_stats['timeout'] += 1
        
        logger.warning(f"超时视频统计: {dict(account_stats)}")
    
    return stuck_videos

def should_force_create_plan(videos: List[LocalCreative], 
                           min_threshold: int = 3,
                           timeout_hours: int = 1) -> bool:
    """
    判断是否应该强制创建计划
    
    Args:
        videos: 视频列表
        min_threshold: 最小强制创建阈值
        timeout_hours: 超时小时数
    
    Returns:
        是否应该强制创建
    """
    if len(videos) < min_threshold:
        return False
    
    # 检查是否有视频超时
    timeout_threshold = datetime.now(timezone.utc) - timedelta(hours=timeout_hours)
    
    for video in videos:
        # 处理不同类型的视频对象
        video_updated_at = video.updated_at
        video_name = getattr(video, 'filename', None) or getattr(video.local_creative, 'filename', 'Unknown') if hasattr(video, 'local_creative') else 'Unknown'

        if video_updated_at < timeout_threshold:
            logger.warning(f"视频 {video_name} 超时 {timeout_hours} 小时，触发强制创建")
            return True
    
    return False

def get_flexible_grouping_config(app_settings: Dict[str, Any]) -> Dict[str, int]:
    """
    获取灵活分组配置
    
    Args:
        app_settings: 应用配置
    
    Returns:
        分组配置字典
    """
    flexible_config = app_settings.get('flexible_grouping', {})
    
    return {
        'enabled': flexible_config.get('enabled', True),
        'min_creative_count': flexible_config.get('min_creative_count', 9),  # 默认9个素材
        'max_creative_count': flexible_config.get('max_creative_count', 9),
        'timeout_hours': flexible_config.get('timeout_hours', 0.5),  # 30分钟超时
        'force_create_threshold': flexible_config.get('force_create_threshold', 3)  # 超时后最少3个素材
    }
