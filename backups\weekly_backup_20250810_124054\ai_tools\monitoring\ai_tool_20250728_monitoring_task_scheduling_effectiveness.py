#!/usr/bin/env python3
"""任务调度效果监控脚本"""

import sys
import time
from pathlib import Path
from datetime import datetime, timedelta

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.database.connection import database_session
from qianchuan_aw.database.models import LocalCreative

def monitor_task_effectiveness(duration_minutes=10):
    """监控任务调度效果"""
    print(f"📊 开始监控任务调度效果 ({duration_minutes} 分钟)")
    print("=" * 60)
    
    start_time = datetime.now()
    end_time = start_time + timedelta(minutes=duration_minutes)
    
    # 记录初始状态
    with database_session() as db:
        initial_stats = {}
        for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:
            count = db.query(LocalCreative).filter(LocalCreative.status == status).count()
            initial_stats[status] = count
    
    print(f"⏰ 开始时间: {start_time.strftime('%H:%M:%S')}")
    print("📈 初始状态:")
    for status, count in initial_stats.items():
        print(f"   {status}: {count}")
    
    # 定期检查状态变化
    check_count = 0
    while datetime.now() < end_time:
        time.sleep(60)  # 每分钟检查一次
        check_count += 1
        
        with database_session() as db:
            current_stats = {}
            for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:
                count = db.query(LocalCreative).filter(LocalCreative.status == status).count()
                current_stats[status] = count
        
        print(f"\n📊 检查 {check_count} ({datetime.now().strftime('%H:%M:%S')}):")
        for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:
            initial = initial_stats[status]
            current = current_stats[status]
            change = current - initial
            
            if change != 0:
                direction = "📈" if change > 0 else "📉"
                print(f"   {direction} {status}: {initial} → {current} ({change:+d})")
            else:
                print(f"   ➡️ {status}: {current}")
    
    # 最终统计
    with database_session() as db:
        final_stats = {}
        for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:
            count = db.query(LocalCreative).filter(LocalCreative.status == status).count()
            final_stats[status] = count
    
    print(f"\n🎯 监控完成 ({datetime.now().strftime('%H:%M:%S')})")
    print("=" * 60)
    print("📊 总体变化:")
    
    total_activity = 0
    for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:
        initial = initial_stats[status]
        final = final_stats[status]
        change = final - initial
        total_activity += abs(change)
        
        if change < 0:
            print(f"✅ {status}: {initial} → {final} (减少 {abs(change)})")
        elif change > 0:
            print(f"📈 {status}: {initial} → {final} (增加 {change})")
        else:
            print(f"➡️ {status}: {final} (无变化)")
    
    print(f"\n📈 总活动量: {total_activity} (状态变化总数)")
    
    if total_activity > 10:
        print("✅ 任务调度效果良好，工作流活跃")
    elif total_activity > 5:
        print("⚠️ 任务调度效果一般，可能需要进一步优化")
    else:
        print("❌ 任务调度效果较差，建议检查配置")

if __name__ == "__main__":
    try:
        monitor_task_effectiveness(10)
    except KeyboardInterrupt:
        print("\n⏹️ 监控已取消")
    except Exception as e:
        print(f"❌ 监控失败: {e}")
