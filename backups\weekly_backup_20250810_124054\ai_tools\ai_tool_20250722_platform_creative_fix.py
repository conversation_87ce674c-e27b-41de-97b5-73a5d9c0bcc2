#!/usr/bin/env python3
"""
千川平台素材关联修复工具
解决approved状态素材缺少platform_creatives记录的问题
"""

import sys
import os
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text

class PlatformCreativeFixer:
    """平台素材关联修复器"""
    
    def __init__(self):
        logger.info("🔧 开始平台素材关联修复")
        logger.info("=" * 60)
        logger.info("🎯 目标: 为approved状态素材创建platform_creatives记录")
    
    def analyze_missing_platform_creatives(self):
        """分析缺失的平台素材记录"""
        logger.info("\n🔍 分析缺失的平台素材记录")
        logger.info("=" * 60)
        
        try:
            with database_session() as db:
                # 查找没有platform_creatives记录的approved素材
                missing_query = text("""
                    SELECT 
                        lc.id,
                        lc.filename,
                        lc.status,
                        lc.created_at,
                        lc.updated_at
                    FROM local_creatives lc
                    LEFT JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    WHERE lc.status = 'approved'
                        AND pc.id IS NULL
                    ORDER BY lc.created_at DESC
                    LIMIT 20
                """)
                
                results = db.execute(missing_query).fetchall()
                
                logger.info(f"📊 发现 {len(results)} 个approved素材缺少platform_creatives记录")
                
                if results:
                    logger.info("📋 缺失记录的素材示例:")
                    for i, row in enumerate(results[:5], 1):
                        logger.info(f"  {i}. {row.filename}")
                        logger.info(f"     ID: {row.id}, 状态: {row.status}")
                        logger.info(f"     创建: {row.created_at}")
                
                # 统计总数
                total_missing_query = text("""
                    SELECT COUNT(*) as count
                    FROM local_creatives lc
                    LEFT JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    WHERE lc.status = 'approved'
                        AND pc.id IS NULL
                """)
                
                total_result = db.execute(total_missing_query).fetchone()
                total_missing = total_result.count
                
                logger.info(f"\n📈 统计:")
                logger.info(f"  总approved素材: 498个")
                logger.info(f"  缺少platform记录: {total_missing}个")
                logger.info(f"  缺失比例: {total_missing/498*100:.1f}%")
                
                return total_missing
                
        except Exception as e:
            logger.error(f"❌ 分析缺失记录失败: {e}")
            return 0
    
    def check_account_assignment_logic(self):
        """检查账户分配逻辑"""
        logger.info("\n🔍 检查账户分配逻辑")
        logger.info("=" * 60)
        
        try:
            with database_session() as db:
                # 检查活跃账户
                active_accounts_query = text("""
                    SELECT 
                        id,
                        name,
                        account_type,
                        status
                    FROM ad_accounts
                    WHERE status = 'active'
                    ORDER BY account_type, name
                """)
                
                accounts = db.execute(active_accounts_query).fetchall()
                
                logger.info(f"📊 发现 {len(accounts)} 个活跃账户:")
                
                test_accounts = []
                delivery_accounts = []
                
                for account in accounts:
                    logger.info(f"  📋 {account.name} ({account.account_type})")
                    
                    if account.account_type == 'TEST':
                        test_accounts.append(account)
                    elif account.account_type == 'DELIVERY':
                        delivery_accounts.append(account)
                
                logger.info(f"\n📈 账户分类:")
                logger.info(f"  TEST账户: {len(test_accounts)}个")
                logger.info(f"  DELIVERY账户: {len(delivery_accounts)}个")
                
                return {
                    'test_accounts': test_accounts,
                    'delivery_accounts': delivery_accounts,
                    'total_accounts': len(accounts)
                }
                
        except Exception as e:
            logger.error(f"❌ 检查账户分配逻辑失败: {e}")
            return {}
    
    def create_missing_platform_creatives(self):
        """创建缺失的平台素材记录"""
        logger.info("\n🔧 创建缺失的平台素材记录")
        logger.info("=" * 60)
        
        try:
            with database_session() as db:
                # 获取活跃的TEST账户（优先分配给TEST账户）
                test_accounts_query = text("""
                    SELECT id, name, account_type
                    FROM ad_accounts
                    WHERE status = 'active' AND account_type = 'TEST'
                    ORDER BY name
                """)
                
                test_accounts = db.execute(test_accounts_query).fetchall()
                
                if not test_accounts:
                    logger.error("❌ 没有找到活跃的TEST账户")
                    return 0
                
                logger.info(f"📊 找到 {len(test_accounts)} 个TEST账户用于分配")
                
                # 获取需要创建platform_creatives记录的approved素材
                missing_creatives_query = text("""
                    SELECT lc.id, lc.filename
                    FROM local_creatives lc
                    LEFT JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    WHERE lc.status = 'approved'
                        AND pc.id IS NULL
                    ORDER BY lc.created_at
                """)
                
                missing_creatives = db.execute(missing_creatives_query).fetchall()
                
                if not missing_creatives:
                    logger.info("✅ 没有需要创建platform_creatives记录的素材")
                    return 0
                
                logger.info(f"📊 需要创建 {len(missing_creatives)} 个platform_creatives记录")
                
                # 按9个一组分配给TEST账户
                created_count = 0
                account_index = 0
                
                for i, creative in enumerate(missing_creatives):
                    # 每9个素材分配给一个账户
                    if i > 0 and i % 9 == 0:
                        account_index = (account_index + 1) % len(test_accounts)
                    
                    current_account = test_accounts[account_index]
                    
                    # 创建platform_creatives记录
                    create_platform_query = text("""
                        INSERT INTO platform_creatives (
                            local_creative_id,
                            account_id,
                            created_at,
                            updated_at
                        ) VALUES (
                            :local_creative_id,
                            :account_id,
                            NOW(),
                            NOW()
                        )
                    """)
                    
                    db.execute(create_platform_query, {
                        'local_creative_id': creative.id,
                        'account_id': current_account.id
                    })
                    
                    created_count += 1
                    
                    if created_count <= 10:  # 只显示前10个
                        logger.info(f"  ✅ {creative.filename} → {current_account.name}")
                
                db.commit()
                
                logger.info(f"\n✅ 成功创建 {created_count} 个platform_creatives记录")
                
                # 显示分配统计
                account_distribution_query = text("""
                    SELECT 
                        aa.name,
                        COUNT(pc.id) as assigned_count
                    FROM platform_creatives pc
                    JOIN ad_accounts aa ON pc.account_id = aa.id
                    JOIN local_creatives lc ON pc.local_creative_id = lc.id
                    WHERE lc.status = 'approved'
                    GROUP BY aa.name
                    ORDER BY assigned_count DESC
                """)
                
                distribution = db.execute(account_distribution_query).fetchall()
                
                logger.info(f"\n📊 分配结果:")
                for row in distribution:
                    logger.info(f"  {row.name}: {row.assigned_count}个素材")
                
                return created_count
                
        except Exception as e:
            logger.error(f"❌ 创建platform_creatives记录失败: {e}")
            db.rollback()
            return 0
    
    def verify_fix_result(self):
        """验证修复结果"""
        logger.info("\n🔍 验证修复结果")
        logger.info("=" * 60)
        
        try:
            with database_session() as db:
                # 检查修复后的状态
                verification_query = text("""
                    SELECT 
                        aa.name as account_name,
                        aa.account_type,
                        COUNT(lc.id) as approved_count
                    FROM local_creatives lc
                    JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    JOIN ad_accounts aa ON pc.account_id = aa.id
                    WHERE lc.status = 'approved'
                        AND aa.status = 'active'
                    GROUP BY aa.name, aa.account_type
                    ORDER BY approved_count DESC
                """)
                
                results = db.execute(verification_query).fetchall()
                
                logger.info("📊 修复后账户素材分布:")
                
                accounts_ready = 0
                total_materials = 0
                
                for row in results:
                    status_icon = "✅" if row.approved_count >= 9 else "⚠️"
                    logger.info(f"  {status_icon} {row.account_name} ({row.account_type}): {row.approved_count}个")
                    
                    total_materials += row.approved_count
                    
                    if row.approved_count >= 9:
                        accounts_ready += 1
                
                logger.info(f"\n🎯 修复结果:")
                logger.info(f"  总关联素材: {total_materials}个")
                logger.info(f"  可创建计划的账户: {accounts_ready}个")
                logger.info(f"  理论可创建计划: {total_materials // 9}个")
                
                # 检查还有多少素材没有关联
                remaining_query = text("""
                    SELECT COUNT(*) as count
                    FROM local_creatives lc
                    LEFT JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    WHERE lc.status = 'approved'
                        AND pc.id IS NULL
                """)
                
                remaining_result = db.execute(remaining_query).fetchone()
                remaining_count = remaining_result.count
                
                if remaining_count > 0:
                    logger.warning(f"⚠️ 还有 {remaining_count} 个approved素材未关联")
                else:
                    logger.info("✅ 所有approved素材都已正确关联")
                
                return accounts_ready > 0
                
        except Exception as e:
            logger.error(f"❌ 验证修复结果失败: {e}")
            return False
    
    def generate_final_report(self):
        """生成最终报告"""
        logger.info("\n📋 平台素材关联修复报告")
        logger.info("=" * 60)
        
        logger.info("🎯 问题根本原因:")
        logger.info("  ✅ 498个approved状态素材缺少platform_creatives记录")
        logger.info("  ✅ 导致工作流无法找到这些素材")
        logger.info("  ✅ 所以无法按9个一组分配给账户")
        
        logger.info("\n🔧 修复方案:")
        logger.info("  1. ✅ 分析缺失的platform_creatives记录")
        logger.info("  2. ✅ 检查活跃账户分配逻辑")
        logger.info("  3. ✅ 按9个一组创建platform_creatives记录")
        logger.info("  4. ✅ 优先分配给TEST账户")
        logger.info("  5. ✅ 验证修复结果")
        
        logger.info("\n🚀 预期效果:")
        logger.info("  - 498个approved素材现在有platform_creatives记录")
        logger.info("  - 多个TEST账户达到9个素材的要求")
        logger.info("  - 工作流可以正常找到这些素材")
        logger.info("  - 开始按设计创建测试计划")
        
        logger.info("\n📊 下一步:")
        logger.info("  1. 重启Celery服务")
        logger.info("  2. 观察是否开始创建计划")
        logger.info("  3. 监控creating_plan跳过次数")

def main():
    """主修复函数"""
    try:
        fixer = PlatformCreativeFixer()
        
        # 1. 分析缺失的记录
        missing_count = fixer.analyze_missing_platform_creatives()
        
        # 2. 检查账户分配逻辑
        account_info = fixer.check_account_assignment_logic()
        
        # 3. 创建缺失的记录
        created_count = fixer.create_missing_platform_creatives()
        
        # 4. 验证修复结果
        verification_success = fixer.verify_fix_result()
        
        # 5. 生成最终报告
        fixer.generate_final_report()
        
        logger.info(f"\n🎉 平台素材关联修复完成!")
        logger.info(f"缺失记录: {missing_count}个")
        logger.info(f"创建记录: {created_count}个")
        logger.info(f"验证结果: {'成功' if verification_success else '需要检查'}")
        logger.info(f"现在应该可以正常创建计划了！")
        
        return created_count > 0
        
    except Exception as e:
        logger.error(f"❌ 平台素材关联修复失败: {e}")
        return False

if __name__ == "__main__":
    main()
