# -*- coding: utf-8 -*-
"""
千川自动化项目工作流质量控制工具

基于2025-07-24业务逻辑修正，提供素材质量检查、重复检测和文件删除功能。
"""

import os
import hashlib
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional, Tuple, List
from sqlalchemy.orm import Session
from sqlalchemy import text

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.models import LocalCreative, PlatformCreative, Campaign


class MaterialQualityError(Exception):
    """素材质量异常"""
    pass


class WorkflowQualityController:
    """工作流质量控制器"""
    
    # 素材质量标准
    MIN_VIDEO_DURATION = 5      # 最小时长5秒
    MAX_VIDEO_DURATION = 300    # 最大时长300秒
    MIN_FILE_SIZE = 100 * 1024  # 最小文件大小100KB
    MAX_FILE_SIZE = 500 * 1024 * 1024  # 最大文件大小500MB
    
    SUPPORTED_FORMATS = {'.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm'}
    
    def __init__(self, db_session: Session):
        self.db = db_session
    
    def check_material_quality(self, file_path: str) -> Dict[str, Any]:
        """
        检查素材质量

        🛡️ 业务铁律修复：质量问题不再直接删除文件，而是标记为失败

        Returns:
            Dict: {
                'valid': bool,
                'mark_failed': bool,  # 🛡️ 修复：替代delete_required
                'failure_reason': str,  # 🛡️ 修复：替代delete_reason
                'errors': List[str]
            }
        """
        # 🛡️ 业务铁律修复：质量问题不再直接删除文件，而是标记为失败
        result = {
            'valid': True,
            'mark_failed': False,  # 🛡️ 修复：不再要求删除，而是标记失败
            'failure_reason': '',  # 🛡️ 修复：失败原因而非删除原因
            'errors': []
        }
        
        if not os.path.exists(file_path):
            result['valid'] = False
            result['mark_failed'] = True  # 🛡️ 修复：标记失败而非删除
            result['failure_reason'] = '文件不存在'
            result['errors'].append('文件不存在')
            return result
        
        # 检查文件格式
        file_ext = Path(file_path).suffix.lower()
        if file_ext not in self.SUPPORTED_FORMATS:
            result['valid'] = False
            result['mark_failed'] = True  # 🛡️ 修复：标记失败而非删除
            result['failure_reason'] = f'不支持的文件格式: {file_ext}'
            result['errors'].append(f'不支持的文件格式: {file_ext}')
        
        # 检查文件大小
        try:
            file_size = os.path.getsize(file_path)
            if file_size < self.MIN_FILE_SIZE:
                result['valid'] = False
                result['mark_failed'] = True  # 🛡️ 修复：标记失败而非删除
                result['failure_reason'] = f'文件过小: {file_size/1024:.1f}KB < {self.MIN_FILE_SIZE/1024}KB'
                result['errors'].append(result['failure_reason'])
            elif file_size > self.MAX_FILE_SIZE:
                result['valid'] = False
                result['mark_failed'] = True  # 🛡️ 修复：标记失败而非删除
                result['failure_reason'] = f'文件过大: {file_size/(1024*1024):.1f}MB > {self.MAX_FILE_SIZE/(1024*1024)}MB'
                result['errors'].append(result['failure_reason'])
        except OSError as e:
            result['valid'] = False
            result['mark_failed'] = True  # 🛡️ 修复：标记失败而非删除
            result['failure_reason'] = f'文件访问错误: {e}'
            result['errors'].append(result['failure_reason'])
        
        # 检查视频时长（如果有视频处理库）
        try:
            duration = self._get_video_duration(file_path)
            if duration is not None:
                if duration < self.MIN_VIDEO_DURATION:
                    result['valid'] = False
                    result['mark_failed'] = True  # 🛡️ 修复：标记失败而非删除
                    result['failure_reason'] = f'视频时长过短: {duration}秒 < {self.MIN_VIDEO_DURATION}秒'
                    result['errors'].append(result['failure_reason'])
                elif duration > self.MAX_VIDEO_DURATION:
                    result['valid'] = False
                    result['mark_failed'] = True  # 🛡️ 修复：标记失败而非删除
                    result['failure_reason'] = f'视频时长过长: {duration}秒 > {self.MAX_VIDEO_DURATION}秒'
                    result['errors'].append(result['failure_reason'])
        except Exception as e:
            logger.warning(f"无法检查视频时长: {e}")
        
        # 检查文件是否损坏
        try:
            with open(file_path, 'rb') as f:
                # 尝试读取文件头部
                header = f.read(1024)
                if len(header) < 100:  # 文件头部太短，可能损坏
                    result['valid'] = False
                    result['mark_failed'] = True  # 🛡️ 修复：标记失败而非删除
                    result['failure_reason'] = '文件可能损坏（头部数据不足）'
                    result['errors'].append(result['failure_reason'])
        except Exception as e:
            result['valid'] = False
            result['mark_failed'] = True  # 🛡️ 修复：标记失败而非删除
            result['failure_reason'] = f'文件读取失败，可能损坏: {e}'
            result['errors'].append(result['failure_reason'])
        
        return result
    
    def _get_video_duration(self, file_path: str) -> Optional[float]:
        """获取视频时长（秒）"""
        try:
            # 尝试使用ffprobe获取视频信息
            import subprocess
            import json

            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', file_path
            ]

            # 修复编码问题：使用utf-8编码，忽略错误
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=10,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                data = json.loads(result.stdout)
                if 'format' in data and 'duration' in data['format']:
                    duration = float(data['format']['duration'])
                    logger.info(f"检测到视频时长: {duration:.2f}秒 - {os.path.basename(file_path)}")
                    return duration

            # 如果ffprobe失败，尝试其他方法
            logger.warning(f"ffprobe检测失败，尝试其他方法: {file_path}")
            return self._get_duration_fallback(file_path)

        except Exception as e:
            logger.warning(f"视频时长检测失败: {e}")
            return self._get_duration_fallback(file_path)

    def _get_duration_fallback(self, file_path: str) -> Optional[float]:
        """备用的视频时长检测方法"""
        try:
            # 尝试使用cv2（如果可用）
            import cv2
            cap = cv2.VideoCapture(file_path)
            if cap.isOpened():
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
                if fps > 0 and frame_count > 0:
                    duration = frame_count / fps
                    cap.release()
                    logger.info(f"使用cv2检测到视频时长: {duration:.2f}秒 - {os.path.basename(file_path)}")
                    return duration
                cap.release()
        except ImportError:
            logger.debug("cv2不可用，跳过备用检测")
        except Exception as e:
            logger.warning(f"cv2检测失败: {e}")

        return None
    
    def check_already_tested(self, file_hash: str, exclude_creative_id: Optional[int] = None) -> Dict[str, Any]:
        """
        检查视频是否已经测试过（基于file_hash全主体唯一性）
        
        Args:
            file_hash: 文件哈希值
            exclude_creative_id: 排除的素材ID（用于更新时检查）
            
        Returns:
            Dict: {
                'already_tested': bool,
                'existing_creative': Optional[LocalCreative],
                'test_details': Dict
            }
        """
        query = self.db.query(LocalCreative).filter(
            LocalCreative.file_hash == file_hash
        )
        
        if exclude_creative_id:
            query = query.filter(LocalCreative.id != exclude_creative_id)
        
        existing_creative = query.first()
        
        if existing_creative:
            # 检查是否有关联的测试计划
            has_campaign = self.db.query(Campaign).join(PlatformCreative).filter(
                PlatformCreative.local_creative_id == existing_creative.id
            ).first() is not None
            
            return {
                'already_tested': True,
                'existing_creative': existing_creative,
                'test_details': {
                    'creative_id': existing_creative.id,
                    'filename': existing_creative.filename,
                    'principal_name': existing_creative.principal.name if existing_creative.principal else 'Unknown',
                    'status': existing_creative.status,
                    'created_at': existing_creative.created_at.isoformat(),
                    'has_campaign': has_campaign
                }
            }
        
        return {
            'already_tested': False,
            'existing_creative': None,
            'test_details': {}
        }
    
    def check_harvest_uniqueness(self, material_id_qc: str) -> Dict[str, Any]:
        """
        检查收割唯一性（同一个material_id只能收割一次）
        
        Args:
            material_id_qc: 千川素材ID
            
        Returns:
            Dict: {
                'already_harvested': bool,
                'harvest_details': Dict
            }
        """
        # 查找已收割的素材
        harvested_creative = self.db.query(LocalCreative).filter(
            LocalCreative.material_id_qc == material_id_qc,
            LocalCreative.status == 'harvested'
        ).first()
        
        if harvested_creative:
            return {
                'already_harvested': True,
                'harvest_details': {
                    'creative_id': harvested_creative.id,
                    'filename': harvested_creative.filename,
                    'principal_name': harvested_creative.principal.name if harvested_creative.principal else 'Unknown',
                    'harvested_at': harvested_creative.updated_at.isoformat(),
                    'file_path': harvested_creative.file_path
                }
            }
        
        return {
            'already_harvested': False,
            'harvest_details': {}
        }

    def mark_quality_issue_without_deletion(self, file_path: str, reason: str, creative_id: Optional[int] = None) -> Dict[str, Any]:
        """
        🛡️ 业务铁律修复：标记质量问题但不删除文件

        这是符合业务铁律的处理方式：质量问题应该标记为失败，而不是删除文件

        Args:
            file_path: 文件路径
            reason: 质量问题原因
            creative_id: 关联的素材ID

        Returns:
            Dict: 处理结果
        """
        try:
            logger.warning(f"🔍 质量问题标记: {file_path}")
            logger.info(f"质量问题原因: {reason}")
            logger.info(f"🛡️ 业务铁律保护: 文件未删除，仅标记为质量问题")

            # 如果有关联的数据库记录，标记为失败状态
            if creative_id:
                from qianchuan_aw.database.models import LocalCreative
                creative = self.db.get(LocalCreative, creative_id)
                if creative:
                    creative.status = 'quality_failed'
                    creative.error_message = f'质量问题: {reason}'
                    creative.last_updated = datetime.now()
                    self.db.commit()

                    logger.info(f"已标记数据库记录为质量失败: LocalCreative ID {creative_id}")

            return {
                'success': True,
                'deleted': False,
                'marked_failed': True,
                'reason': reason,
                'file_path': file_path,
                'timestamp': datetime.now().isoformat(),
                'creative_id': creative_id,
                'message': f'✅ 质量问题已标记，文件保留: {os.path.basename(file_path)}'
            }

        except Exception as e:
            logger.error(f"标记质量问题失败: {e}")
            return {
                'success': False,
                'deleted': False,
                'marked_failed': False,
                'reason': f'标记失败: {e}',
                'file_path': file_path,
                'timestamp': datetime.now().isoformat(),
                'creative_id': creative_id,
                'message': f'❌ 标记质量问题失败: {e}'
            }
    
    def delete_material_with_reason(self, file_path: str, reason: str, creative_id: Optional[int] = None) -> Dict[str, Any]:
        """
        🛡️ 业务铁律修复：安全删除素材文件

        业务铁律：只有计划提审完成且该计划下的视频审核不通过时，才能删除视频文件

        Args:
            file_path: 文件路径
            reason: 删除原因
            creative_id: 关联的素材ID（如果有）

        Returns:
            Dict: 删除操作结果
        """
        # 🛡️ 业务铁律检查：使用安全删除检查器
        try:
            # 获取关联的计划ID
            campaign_id_qc = None
            if creative_id:
                from qianchuan_aw.database.models import LocalCreative
                creative = self.db.get(LocalCreative, creative_id)
                if creative and hasattr(creative, 'campaign_id_qc'):
                    campaign_id_qc = creative.campaign_id_qc

            # 使用安全删除检查器
            from qianchuan_aw.utils.safe_deletion_checker import get_safe_deletion_checker
            safe_checker = get_safe_deletion_checker(self.db)

            # 执行安全删除
            delete_result = safe_checker.safe_delete_video_file(file_path, campaign_id_qc)

            if delete_result['violation']:
                # 违反业务铁律，删除被阻止
                logger.warning(f"🛡️ 删除被业务铁律阻止: {file_path}")
                logger.warning(f"🛡️ 阻止原因: {delete_result['reason']}")

                return {
                    'deleted': False,
                    'reason': delete_result['reason'],
                    'file_path': file_path,
                    'timestamp': datetime.now().isoformat(),
                    'creative_id': creative_id,
                    'violation': True,
                    'message': delete_result['message']
                }

            # 删除被允许，处理数据库记录
            if delete_result['deleted'] and creative_id:
                creative = self.db.get(LocalCreative, creative_id)
                if creative:
                    # 删除关联的平台素材记录
                    from qianchuan_aw.database.models import PlatformCreative
                    self.db.query(PlatformCreative).filter(
                        PlatformCreative.local_creative_id == creative_id
                    ).delete()

                    # 删除本地素材记录
                    self.db.delete(creative)
                    self.db.commit()

                    logger.info(f"已删除数据库记录: LocalCreative ID {creative_id}")

            return {
                'deleted': delete_result['deleted'],
                'reason': delete_result['reason'],
                'file_path': file_path,
                'timestamp': datetime.now().isoformat(),
                'creative_id': creative_id,
                'violation': False,
                'message': delete_result['message']
            }

        except Exception as e:
            logger.error(f"安全删除检查失败: {file_path}, 错误: {e}")
            return {
                'deleted': False,
                'reason': f'安全检查失败: {e}',
                'file_path': file_path,
                'timestamp': datetime.now().isoformat(),
                'creative_id': creative_id,
                'violation': True,
                'message': f'❌ 安全检查失败: {e}'
            }
    
    def calculate_file_hash(self, file_path: str) -> str:
        """计算文件哈希值"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败: {file_path}, 错误: {e}")
            return ""
    
    def get_quality_statistics(self) -> Dict[str, Any]:
        """获取质量控制统计信息"""
        try:
            # 统计各种状态的素材数量
            status_stats = self.db.execute(text("""
                SELECT status, COUNT(*) as count
                FROM local_creatives
                GROUP BY status
                ORDER BY count DESC
            """)).fetchall()
            
            # 统计已测试的素材数量（基于file_hash）
            tested_count = self.db.execute(text("""
                SELECT COUNT(DISTINCT file_hash) as count
                FROM local_creatives
                WHERE file_hash IS NOT NULL AND file_hash != ''
            """)).scalar()
            
            # 统计已收割的素材数量
            harvested_count = self.db.execute(text("""
                SELECT COUNT(*) as count
                FROM local_creatives
                WHERE status = 'harvested'
            """)).scalar()
            
            return {
                'status_distribution': [{'status': s, 'count': c} for s, c in status_stats],
                'total_tested_videos': tested_count or 0,
                'total_harvested_videos': harvested_count or 0,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取质量统计失败: {e}")
            return {
                'status_distribution': [],
                'total_tested_videos': 0,
                'total_harvested_videos': 0,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }


def get_quality_controller(db_session: Session) -> WorkflowQualityController:
    """获取质量控制器实例"""
    return WorkflowQualityController(db_session)
