#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 处理千川自动化系统工作流堆积问题
清理条件: 系统运行期间需要持续使用，建议永久保留
"""

import sys
from pathlib import Path
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import LocalCreative, Campaign, AdAccount
from qianchuan_aw.utils.atomic_state_manager import AtomicStateManager


class WorkflowBacklogProcessor:
    """工作流堆积处理器"""
    
    def __init__(self):
        self.processed_count = 0
        self.failed_count = 0
        self.results = []
        
    def process_workflow_backlog(self) -> Dict[str, Any]:
        """处理工作流堆积"""
        logger.info("🔄 开始处理工作流堆积...")
        
        results = {
            'total_processed': 0,
            'successful_transitions': 0,
            'failed_transitions': 0,
            'skipped_items': 0,
            'details': []
        }
        
        try:
            with database_session() as db:
                state_manager = AtomicStateManager(db)
                
                # 处理1: 长期未处理的testing_pending_review素材
                testing_results = self._process_testing_pending_review(db, state_manager)
                results['details'].append(testing_results)
                
                # 处理2: uploaded_pending_plan素材（如果Celery任务不运行）
                pending_plan_results = self._process_uploaded_pending_plan(db, state_manager)
                results['details'].append(pending_plan_results)
                
                # 汇总结果
                for detail in results['details']:
                    results['total_processed'] += detail['processed_count']
                    results['successful_transitions'] += detail['successful_count']
                    results['failed_transitions'] += detail['failed_count']
                    results['skipped_items'] += detail['skipped_count']
                
                logger.info(f"✅ 工作流堆积处理完成: 处理{results['total_processed']}个素材")
                
        except Exception as e:
            logger.error(f"❌ 工作流堆积处理失败: {e}")
            results['error'] = str(e)
        
        return results
    
    def _process_testing_pending_review(self, db, state_manager) -> Dict[str, Any]:
        """处理testing_pending_review状态的素材"""
        logger.info("🧪 处理testing_pending_review状态素材...")
        
        result = {
            'stage': 'testing_pending_review',
            'processed_count': 0,
            'successful_count': 0,
            'failed_count': 0,
            'skipped_count': 0,
            'items': []
        }
        
        try:
            # 查找超过72小时未处理的testing_pending_review素材
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=72)
            
            stuck_creatives = db.query(LocalCreative).filter(
                LocalCreative.status == 'testing_pending_review',
                LocalCreative.updated_at < cutoff_time
            ).limit(20).all()  # 限制批量处理数量
            
            logger.info(f"找到{len(stuck_creatives)}个长期未处理的testing_pending_review素材")
            
            for creative in stuck_creatives:
                result['processed_count'] += 1
                
                try:
                    # 检查是否有关联的计划
                    has_campaign = db.query(Campaign).join(
                        Campaign.platform_creatives
                    ).filter(
                        Campaign.platform_creatives.any(local_creative_id=creative.id)
                    ).first()
                    
                    if has_campaign:
                        # 如果有计划，推进到approved状态
                        with state_manager.atomic_state_transition(
                            creative.id, 'testing_pending_review', 'approved'
                        ) as updated_creative:
                            logger.info(f"素材 {creative.id} 推进到approved状态")
                            result['successful_count'] += 1
                            result['items'].append({
                                'creative_id': creative.id,
                                'filename': creative.filename,
                                'action': 'promoted_to_approved',
                                'reason': 'has_campaign'
                            })
                    else:
                        # 如果没有计划，回退到uploaded_pending_plan状态
                        with state_manager.atomic_state_transition(
                            creative.id, 'testing_pending_review', 'uploaded_pending_plan'
                        ) as updated_creative:
                            logger.info(f"素材 {creative.id} 回退到uploaded_pending_plan状态")
                            result['successful_count'] += 1
                            result['items'].append({
                                'creative_id': creative.id,
                                'filename': creative.filename,
                                'action': 'reverted_to_pending_plan',
                                'reason': 'no_campaign'
                            })
                            
                except Exception as e:
                    logger.error(f"处理素材 {creative.id} 失败: {e}")
                    result['failed_count'] += 1
                    result['items'].append({
                        'creative_id': creative.id,
                        'filename': creative.filename,
                        'action': 'failed',
                        'error': str(e)
                    })
            
        except Exception as e:
            logger.error(f"处理testing_pending_review阶段失败: {e}")
            result['error'] = str(e)
        
        return result
    
    def _process_uploaded_pending_plan(self, db, state_manager) -> Dict[str, Any]:
        """处理uploaded_pending_plan状态的素材"""
        logger.info("📋 处理uploaded_pending_plan状态素材...")
        
        result = {
            'stage': 'uploaded_pending_plan',
            'processed_count': 0,
            'successful_count': 0,
            'failed_count': 0,
            'skipped_count': 0,
            'items': []
        }
        
        try:
            # 查找超过24小时未处理的uploaded_pending_plan素材
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)
            
            pending_creatives = db.query(LocalCreative).filter(
                LocalCreative.status == 'uploaded_pending_plan',
                LocalCreative.updated_at < cutoff_time
            ).limit(10).all()  # 限制批量处理数量
            
            logger.info(f"找到{len(pending_creatives)}个长期未处理的uploaded_pending_plan素材")
            
            for creative in pending_creatives:
                result['processed_count'] += 1
                
                try:
                    # 检查账户类型和状态
                    from qianchuan_aw.database.models import PlatformCreative

                    platform_creative = db.query(PlatformCreative).filter(
                        PlatformCreative.local_creative_id == creative.id
                    ).first()

                    if platform_creative:
                        account = platform_creative.account
                        
                        # 检查账户是否可以创建计划
                        if account.account_type == 'TEST' and account.status == 'active':
                            # 推进到processing状态，等待计划创建
                            with state_manager.atomic_state_transition(
                                creative.id, 'uploaded_pending_plan', 'processing'
                            ) as updated_creative:
                                logger.info(f"素材 {creative.id} 推进到processing状态")
                                result['successful_count'] += 1
                                result['items'].append({
                                    'creative_id': creative.id,
                                    'filename': creative.filename,
                                    'action': 'promoted_to_processing',
                                    'account_type': account.account_type
                                })
                        else:
                            logger.info(f"素材 {creative.id} 跳过：账户类型{account.account_type}或状态{account.status}不符合")
                            result['skipped_count'] += 1
                            result['items'].append({
                                'creative_id': creative.id,
                                'filename': creative.filename,
                                'action': 'skipped',
                                'reason': f'account_type_{account.account_type}_status_{account.status}'
                            })
                    else:
                        logger.warning(f"素材 {creative.id} 没有关联的平台素材或账户")
                        result['skipped_count'] += 1
                        
                except Exception as e:
                    logger.error(f"处理素材 {creative.id} 失败: {e}")
                    result['failed_count'] += 1
                    result['items'].append({
                        'creative_id': creative.id,
                        'filename': creative.filename,
                        'action': 'failed',
                        'error': str(e)
                    })
            
        except Exception as e:
            logger.error(f"处理uploaded_pending_plan阶段失败: {e}")
            result['error'] = str(e)
        
        return result
    
    def get_workflow_statistics(self) -> Dict[str, Any]:
        """获取工作流统计信息"""
        
        try:
            with database_session() as db:
                stats = {}
                
                # 各状态素材数量
                from sqlalchemy import func
                status_counts = db.query(
                    LocalCreative.status,
                    func.count(LocalCreative.id).label('count')
                ).group_by(LocalCreative.status).all()
                
                for status, count in status_counts:
                    stats[status] = count
                
                # 长期未处理的素材
                cutoff_72h = datetime.now(timezone.utc) - timedelta(hours=72)
                cutoff_24h = datetime.now(timezone.utc) - timedelta(hours=24)
                
                stats['stuck_over_72h'] = db.query(LocalCreative).filter(
                    LocalCreative.status.in_(['testing_pending_review', 'uploaded_pending_plan']),
                    LocalCreative.updated_at < cutoff_72h
                ).count()
                
                stats['stuck_over_24h'] = db.query(LocalCreative).filter(
                    LocalCreative.status.in_(['uploaded_pending_plan', 'processing']),
                    LocalCreative.updated_at < cutoff_24h
                ).count()
                
                return stats
                
        except Exception as e:
            logger.error(f"获取工作流统计失败: {e}")
            return {'error': str(e)}


def main():
    """主函数"""
    processor = WorkflowBacklogProcessor()
    
    try:
        # 获取当前统计
        print("📊 当前工作流统计:")
        stats = processor.get_workflow_statistics()
        for status, count in stats.items():
            if isinstance(count, int):
                print(f"  {status}: {count}")
        
        print(f"\n⏰ 长期未处理素材:")
        print(f"  超过72小时: {stats.get('stuck_over_72h', 0)}")
        print(f"  超过24小时: {stats.get('stuck_over_24h', 0)}")
        
        # 处理工作流堆积
        print("\n🔄 开始处理工作流堆积...")
        results = processor.process_workflow_backlog()
        
        print("\n" + "="*60)
        print("🔄 工作流堆积处理结果")
        print("="*60)
        print(f"总处理数量: {results['total_processed']}")
        print(f"成功转换: {results['successful_transitions']}")
        print(f"失败转换: {results['failed_transitions']}")
        print(f"跳过项目: {results['skipped_items']}")
        
        for detail in results['details']:
            print(f"\n📋 {detail['stage']} 阶段:")
            print(f"  处理: {detail['processed_count']}")
            print(f"  成功: {detail['successful_count']}")
            print(f"  失败: {detail['failed_count']}")
            print(f"  跳过: {detail['skipped_count']}")
        
        # 显示处理后的统计
        print("\n📊 处理后工作流统计:")
        new_stats = processor.get_workflow_statistics()
        for status, count in new_stats.items():
            if isinstance(count, int):
                print(f"  {status}: {count}")
        
        return 0 if results['failed_transitions'] == 0 else 1
        
    except Exception as e:
        logger.error(f"工作流堆积处理过程发生错误: {e}", exc_info=True)
        return 1


if __name__ == "__main__":
    exit(main())
