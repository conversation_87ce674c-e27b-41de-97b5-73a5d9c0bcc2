#!/usr/bin/env python3
"""
千川系统全面诊断工具
检查系统中卡住的任务、异常数据和流程问题
"""

import sys
import os
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import json

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text

class SystemDiagnosisAnalyzer:
    """系统诊断分析器"""
    
    def __init__(self):
        self.diagnosis_results = {
            'material_status_issues': {},
            'stuck_workflows': {},
            'data_inconsistencies': {},
            'performance_bottlenecks': {},
            'recommendations': []
        }
    
    def analyze_material_status_flow(self):
        """分析素材状态流转问题"""
        logger.info("🔍 1. 分析素材状态流转问题")
        logger.info("=" * 60)
        
        try:
            with database_session() as db:
                # 查询所有素材状态分布
                status_query = text("""
                    SELECT status, COUNT(*) as count, 
                           MIN(updated_at) as oldest_update,
                           MAX(updated_at) as latest_update
                    FROM materials 
                    GROUP BY status 
                    ORDER BY count DESC
                """)
                
                status_results = db.execute(status_query).fetchall()
                
                logger.info("📊 素材状态分布:")
                total_materials = sum(row.count for row in status_results)
                
                for row in status_results:
                    percentage = (row.count / total_materials * 100) if total_materials > 0 else 0
                    logger.info(f"  {row.status}: {row.count}个 ({percentage:.1f}%)")
                    logger.info(f"    最早更新: {row.oldest_update}")
                    logger.info(f"    最新更新: {row.latest_update}")
                
                # 查找长时间卡在某状态的素材
                stuck_query = text("""
                    SELECT status, COUNT(*) as count,
                           AVG(EXTRACT(EPOCH FROM (NOW() - updated_at))/3600) as avg_hours_stuck
                    FROM materials 
                    WHERE updated_at < NOW() - INTERVAL '2 hours'
                    GROUP BY status
                    HAVING COUNT(*) > 0
                    ORDER BY avg_hours_stuck DESC
                """)
                
                stuck_results = db.execute(stuck_query).fetchall()
                
                if stuck_results:
                    logger.warning("🚨 发现长时间卡住的素材:")
                    for row in stuck_results:
                        logger.warning(f"  {row.status}: {row.count}个素材，平均卡住 {row.avg_hours_stuck:.1f} 小时")
                
                # 查找creating_plan状态的详细信息
                creating_plan_query = text("""
                    SELECT m.filename, m.updated_at, m.account_id,
                           EXTRACT(EPOCH FROM (NOW() - m.updated_at))/3600 as hours_stuck
                    FROM materials m
                    WHERE m.status = 'creating_plan'
                    ORDER BY m.updated_at ASC
                    LIMIT 20
                """)
                
                creating_plan_results = db.execute(creating_plan_query).fetchall()
                
                if creating_plan_results:
                    logger.warning("🔍 creating_plan状态素材详情:")
                    for row in creating_plan_results:
                        logger.warning(f"  {row.filename}: 卡住 {row.hours_stuck:.1f}小时 (账户:{row.account_id})")
                
                self.diagnosis_results['material_status_issues'] = {
                    'total_materials': total_materials,
                    'status_distribution': [(r.status, r.count) for r in status_results],
                    'stuck_materials': [(r.status, r.count, r.avg_hours_stuck) for r in stuck_results],
                    'creating_plan_count': len(creating_plan_results)
                }
                
        except Exception as e:
            logger.error(f"❌ 分析素材状态失败: {e}")
    
    def analyze_campaign_status_flow(self):
        """分析计划状态流转问题"""
        logger.info("\n🔍 2. 分析计划状态流转问题")
        logger.info("=" * 60)
        
        try:
            with database_session() as db:
                # 查询计划状态分布
                campaign_status_query = text("""
                    SELECT status, COUNT(*) as count,
                           MIN(updated_at) as oldest_update,
                           MAX(updated_at) as latest_update
                    FROM campaigns 
                    GROUP BY status 
                    ORDER BY count DESC
                """)
                
                campaign_results = db.execute(campaign_status_query).fetchall()
                
                logger.info("📊 计划状态分布:")
                total_campaigns = sum(row.count for row in campaign_results)
                
                for row in campaign_results:
                    percentage = (row.count / total_campaigns * 100) if total_campaigns > 0 else 0
                    logger.info(f"  {row.status}: {row.count}个 ({percentage:.1f}%)")
                
                # 查找需要提审但长时间未处理的计划
                appeal_needed_query = text("""
                    SELECT c.status, COUNT(*) as count,
                           AVG(EXTRACT(EPOCH FROM (NOW() - c.updated_at))/3600) as avg_hours_waiting
                    FROM campaigns c
                    WHERE c.status IN ('AUDITING', 'REJECTED') 
                        AND (c.first_appeal_at IS NULL OR c.first_appeal_at < NOW() - INTERVAL '24 hours')
                    GROUP BY c.status
                """)
                
                appeal_results = db.execute(appeal_needed_query).fetchall()
                
                if appeal_results:
                    logger.warning("🚨 发现需要提审的计划:")
                    for row in appeal_results:
                        logger.warning(f"  {row.status}: {row.count}个计划，等待 {row.avg_hours_waiting:.1f} 小时")
                
                self.diagnosis_results['stuck_workflows']['campaigns'] = {
                    'total_campaigns': total_campaigns,
                    'status_distribution': [(r.status, r.count) for r in campaign_results],
                    'appeal_needed': [(r.status, r.count, r.avg_hours_waiting) for r in appeal_results]
                }
                
        except Exception as e:
            logger.error(f"❌ 分析计划状态失败: {e}")
    
    def analyze_workflow_bottlenecks(self):
        """分析工作流瓶颈"""
        logger.info("\n🔍 3. 分析工作流瓶颈")
        logger.info("=" * 60)
        
        try:
            with database_session() as db:
                # 检查账户素材分布
                account_materials_query = text("""
                    SELECT a.account_id_qc, a.account_type, 
                           COUNT(m.id) as material_count,
                           COUNT(CASE WHEN m.status = 'creating_plan' THEN 1 END) as creating_plan_count,
                           COUNT(CASE WHEN m.status = 'uploaded_pending_plan' THEN 1 END) as pending_plan_count
                    FROM ad_accounts a
                    LEFT JOIN materials m ON a.id = m.account_id
                    WHERE a.status = 'active'
                    GROUP BY a.account_id_qc, a.account_type
                    ORDER BY creating_plan_count DESC
                """)
                
                account_results = db.execute(account_materials_query).fetchall()
                
                logger.info("📊 账户素材分布:")
                for row in account_results:
                    if row.material_count > 0:
                        logger.info(f"  账户 {row.account_id_qc} ({row.account_type}):")
                        logger.info(f"    总素材: {row.material_count}")
                        logger.info(f"    创建计划中: {row.creating_plan_count}")
                        logger.info(f"    等待计划: {row.pending_plan_count}")
                
                # 检查是否有账户达到创建限制
                bottleneck_accounts = []
                for row in account_results:
                    if row.creating_plan_count > 0:
                        bottleneck_accounts.append({
                            'account_id': row.account_id_qc,
                            'account_type': row.account_type,
                            'creating_plan_count': row.creating_plan_count
                        })
                
                if bottleneck_accounts:
                    logger.warning("🚨 发现可能的瓶颈账户:")
                    for account in bottleneck_accounts:
                        logger.warning(f"  账户 {account['account_id']} 有 {account['creating_plan_count']} 个素材在创建计划中")
                
                self.diagnosis_results['performance_bottlenecks'] = {
                    'account_distribution': [(r.account_id_qc, r.material_count, r.creating_plan_count) for r in account_results],
                    'bottleneck_accounts': bottleneck_accounts
                }
                
        except Exception as e:
            logger.error(f"❌ 分析工作流瓶颈失败: {e}")
    
    def analyze_data_inconsistencies(self):
        """分析数据一致性问题"""
        logger.info("\n🔍 4. 分析数据一致性问题")
        logger.info("=" * 60)
        
        try:
            with database_session() as db:
                # 检查孤儿素材（没有对应账户的素材）
                orphan_materials_query = text("""
                    SELECT COUNT(*) as orphan_count
                    FROM materials m
                    LEFT JOIN ad_accounts a ON m.account_id = a.id
                    WHERE a.id IS NULL
                """)
                
                orphan_result = db.execute(orphan_materials_query).fetchone()
                
                if orphan_result.orphan_count > 0:
                    logger.warning(f"🚨 发现 {orphan_result.orphan_count} 个孤儿素材（无对应账户）")
                
                # 检查重复文件名
                duplicate_files_query = text("""
                    SELECT filename, COUNT(*) as count
                    FROM materials
                    GROUP BY filename
                    HAVING COUNT(*) > 1
                    ORDER BY count DESC
                    LIMIT 10
                """)
                
                duplicate_results = db.execute(duplicate_files_query).fetchall()
                
                if duplicate_results:
                    logger.warning("🚨 发现重复文件名:")
                    for row in duplicate_results:
                        logger.warning(f"  {row.filename}: {row.count} 个重复")
                
                # 检查状态异常的素材
                invalid_status_query = text("""
                    SELECT status, COUNT(*) as count
                    FROM materials
                    WHERE status NOT IN (
                        'uploaded', 'uploaded_pending_plan', 'creating_plan', 
                        'plan_created', 'monitoring', 'approved', 'rejected'
                    )
                    GROUP BY status
                """)
                
                invalid_status_results = db.execute(invalid_status_query).fetchall()
                
                if invalid_status_results:
                    logger.warning("🚨 发现异常状态素材:")
                    for row in invalid_status_results:
                        logger.warning(f"  {row.status}: {row.count} 个")
                
                self.diagnosis_results['data_inconsistencies'] = {
                    'orphan_materials': orphan_result.orphan_count,
                    'duplicate_files': [(r.filename, r.count) for r in duplicate_results],
                    'invalid_statuses': [(r.status, r.count) for r in invalid_status_results]
                }
                
        except Exception as e:
            logger.error(f"❌ 分析数据一致性失败: {e}")
    
    def analyze_recent_logs(self):
        """分析最近的日志模式"""
        logger.info("\n🔍 5. 分析最近的日志模式")
        logger.info("=" * 60)
        
        try:
            # 分析今日日志文件
            log_file = f"logs/app_{datetime.now().strftime('%Y-%m-%d')}.log"
            
            if not os.path.exists(log_file):
                logger.warning(f"日志文件不存在: {log_file}")
                return
            
            # 统计关键日志模式
            patterns = {
                'creating_plan_skip': 0,
                'plan_creation_errors': 0,
                'material_status_changes': 0,
                'workflow_interruptions': 0
            }
            
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if '状态已变更为 creating_plan，跳过' in line:
                        patterns['creating_plan_skip'] += 1
                    elif 'ERROR' in line and 'plan' in line.lower():
                        patterns['plan_creation_errors'] += 1
                    elif '状态已变更' in line:
                        patterns['material_status_changes'] += 1
                    elif '逻辑中断' in line or '工作流结束' in line:
                        patterns['workflow_interruptions'] += 1
            
            logger.info("📊 今日日志模式统计:")
            for pattern, count in patterns.items():
                logger.info(f"  {pattern}: {count} 次")
            
            # 特别关注creating_plan跳过的情况
            if patterns['creating_plan_skip'] > 50:
                logger.warning(f"🚨 creating_plan跳过次数异常高: {patterns['creating_plan_skip']} 次")
                logger.warning("  可能原因: 素材状态更新机制有问题")
            
        except Exception as e:
            logger.error(f"❌ 分析日志模式失败: {e}")
    
    def generate_recommendations(self):
        """生成修复建议"""
        logger.info("\n📋 生成系统修复建议")
        logger.info("=" * 60)
        
        recommendations = []
        
        # 基于分析结果生成建议
        material_issues = self.diagnosis_results.get('material_status_issues', {})
        
        if material_issues.get('creating_plan_count', 0) > 10:
            recommendations.append({
                'priority': 'HIGH',
                'issue': 'creating_plan状态素材过多',
                'solution': '重置长时间卡住的creating_plan状态素材',
                'command': 'UPDATE materials SET status = \'uploaded_pending_plan\' WHERE status = \'creating_plan\' AND updated_at < NOW() - INTERVAL \'2 hours\''
            })
        
        bottlenecks = self.diagnosis_results.get('performance_bottlenecks', {})
        if bottlenecks.get('bottleneck_accounts'):
            recommendations.append({
                'priority': 'MEDIUM',
                'issue': '特定账户素材堆积',
                'solution': '检查账户配置和计划创建限制',
                'command': '检查account_type配置和creative_count设置'
            })
        
        inconsistencies = self.diagnosis_results.get('data_inconsistencies', {})
        if inconsistencies.get('orphan_materials', 0) > 0:
            recommendations.append({
                'priority': 'MEDIUM',
                'issue': '孤儿素材存在',
                'solution': '清理无效的素材记录',
                'command': 'DELETE FROM materials WHERE account_id NOT IN (SELECT id FROM ad_accounts)'
            })
        
        logger.info("🎯 修复建议:")
        for i, rec in enumerate(recommendations, 1):
            priority_icon = "🔴" if rec['priority'] == 'HIGH' else "🟡" if rec['priority'] == 'MEDIUM' else "🟢"
            logger.info(f"  {priority_icon} {i}. {rec['issue']}")
            logger.info(f"     解决方案: {rec['solution']}")
            logger.info(f"     执行命令: {rec['command']}")
        
        self.diagnosis_results['recommendations'] = recommendations
        
        return recommendations
    
    def generate_summary_report(self):
        """生成诊断总结报告"""
        logger.info("\n📋 系统诊断总结报告")
        logger.info("=" * 60)
        
        logger.info("🎯 核心发现:")
        
        # 素材状态问题
        material_issues = self.diagnosis_results.get('material_status_issues', {})
        if material_issues:
            logger.info(f"  📊 总素材数: {material_issues.get('total_materials', 0)}")
            creating_plan_count = material_issues.get('creating_plan_count', 0)
            if creating_plan_count > 0:
                logger.warning(f"  🚨 creating_plan状态素材: {creating_plan_count} 个")
        
        # 工作流瓶颈
        bottlenecks = self.diagnosis_results.get('performance_bottlenecks', {})
        if bottlenecks.get('bottleneck_accounts'):
            logger.warning(f"  🚨 瓶颈账户数: {len(bottlenecks['bottleneck_accounts'])}")
        
        # 数据一致性
        inconsistencies = self.diagnosis_results.get('data_inconsistencies', {})
        if inconsistencies.get('orphan_materials', 0) > 0:
            logger.warning(f"  🚨 孤儿素材: {inconsistencies['orphan_materials']} 个")
        
        logger.info("\n💡 立即行动建议:")
        recommendations = self.diagnosis_results.get('recommendations', [])
        high_priority = [r for r in recommendations if r['priority'] == 'HIGH']
        
        if high_priority:
            logger.info("  🔴 高优先级问题需要立即处理:")
            for rec in high_priority:
                logger.info(f"    - {rec['issue']}")
        else:
            logger.info("  ✅ 未发现高优先级问题")
        
        return self.diagnosis_results

def main():
    """主诊断函数"""
    try:
        analyzer = SystemDiagnosisAnalyzer()
        
        logger.info("🚀 开始千川系统全面诊断")
        logger.info("=" * 60)
        
        # 执行各项诊断
        analyzer.analyze_material_status_flow()
        analyzer.analyze_campaign_status_flow()
        analyzer.analyze_workflow_bottlenecks()
        analyzer.analyze_data_inconsistencies()
        analyzer.analyze_recent_logs()
        
        # 生成建议和报告
        recommendations = analyzer.generate_recommendations()
        summary = analyzer.generate_summary_report()
        
        logger.info(f"\n✅ 系统诊断完成")
        logger.info(f"发现 {len(recommendations)} 个需要处理的问题")
        
        return summary
        
    except Exception as e:
        logger.error(f"❌ 系统诊断失败: {e}")
        return None

if __name__ == "__main__":
    main()
