#!/usr/bin/env python3
"""
千川工作流紧急诊断和修复工具
全面检查和修复工作流中的严重问题
"""

import sys
import os
from datetime import datetime, timedelta
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text

class EmergencyWorkflowDiagnostic:
    """紧急工作流诊断器"""
    
    def __init__(self):
        logger.critical("🚨 千川工作流紧急诊断和修复")
        logger.critical("=" * 60)
        logger.critical("⚠️ 工作流当前状态：CRITICAL")
        logger.critical("⚠️ 已确认问题：重复计划创建、低完成率、缺失提审流程")
    
    def step1_emergency_status_check(self):
        """第一步：紧急状态确认"""
        logger.critical("\n🔍 第一步：紧急状态确认")
        logger.critical("=" * 60)
        
        try:
            with database_session() as db:
                # 1. 检查计划状态分布
                campaign_status_query = text("""
                    SELECT 
                        c.status,
                        COUNT(*) as count,
                        COUNT(CASE WHEN c.created_at::date = CURRENT_DATE THEN 1 END) as today_count
                    FROM campaigns c
                    GROUP BY c.status
                    ORDER BY count DESC
                """)
                
                campaign_status = db.execute(campaign_status_query).fetchall()
                
                logger.critical("📊 计划状态分布:")
                total_campaigns = sum(row.count for row in campaign_status)
                today_total = sum(row.today_count for row in campaign_status)
                
                for row in campaign_status:
                    percentage = (row.count / total_campaigns * 100) if total_campaigns > 0 else 0
                    logger.critical(f"  {row.status}: {row.count} 个 ({percentage:.1f}%) - 今天: {row.today_count}")
                
                logger.critical(f"  总计划数: {total_campaigns}")
                logger.critical(f"  今天创建: {today_total}")
                
                # 2. 检查"已创建但未提审"状态的计划
                pending_review_query = text("""
                    SELECT 
                        c.id,
                        c.campaign_id_qc,
                        c.status,
                        c.created_at,
                        aa.name as account_name,
                        aa.account_type,
                        lc.filename
                    FROM campaigns c
                    JOIN ad_accounts aa ON c.account_id = aa.id
                    LEFT JOIN campaign_platform_creative_association cpca ON c.id = cpca.campaign_id
                    LEFT JOIN platform_creatives pc ON cpca.platform_creative_id = pc.id
                    LEFT JOIN local_creatives lc ON pc.local_creative_id = lc.id
                    WHERE c.status IN ('created', 'pending_review', 'draft')
                    AND aa.account_type = 'TEST'
                    ORDER BY c.created_at DESC
                """)
                
                pending_campaigns = db.execute(pending_review_query).fetchall()
                
                logger.critical(f"\n📊 已创建但未提审的测试计划:")
                logger.critical(f"  数量: {len(pending_campaigns)} 个")
                
                if pending_campaigns:
                    logger.critical("  详细列表:")
                    for campaign in pending_campaigns[:10]:  # 显示前10个
                        logger.critical(f"    📋 {campaign.campaign_id_qc}:")
                        logger.critical(f"      状态: {campaign.status}")
                        logger.critical(f"      账户: {campaign.account_name}")
                        logger.critical(f"      视频: {campaign.filename}")
                        logger.critical(f"      创建时间: {campaign.created_at}")
                
                # 3. 检查今天的重复计划创建
                today_duplicates_query = text("""
                    WITH today_campaigns AS (
                        SELECT 
                            lc.filename,
                            lc.file_hash,
                            c.campaign_id_qc,
                            c.created_at,
                            aa.name as account_name
                        FROM campaigns c
                        JOIN ad_accounts aa ON c.account_id = aa.id
                        JOIN campaign_platform_creative_association cpca ON c.id = cpca.campaign_id
                        JOIN platform_creatives pc ON cpca.platform_creative_id = pc.id
                        JOIN local_creatives lc ON pc.local_creative_id = lc.id
                        WHERE c.created_at::date = CURRENT_DATE
                        AND aa.account_type = 'TEST'
                    )
                    SELECT 
                        filename,
                        file_hash,
                        COUNT(*) as campaign_count,
                        STRING_AGG(campaign_id_qc, ', ') as campaign_ids,
                        STRING_AGG(account_name, ', ') as accounts
                    FROM today_campaigns
                    GROUP BY filename, file_hash
                    HAVING COUNT(*) > 1
                    ORDER BY campaign_count DESC
                """)
                
                today_duplicates = db.execute(today_duplicates_query).fetchall()
                
                logger.critical(f"\n🚨 今天的重复计划创建:")
                logger.critical(f"  重复视频数: {len(today_duplicates)} 个")
                
                if today_duplicates:
                    logger.critical("  违规详情:")
                    for dup in today_duplicates:
                        logger.critical(f"    📋 {dup.filename}:")
                        logger.critical(f"      重复计划数: {dup.campaign_count}")
                        logger.critical(f"      计划ID: {dup.campaign_ids}")
                        logger.critical(f"      涉及账户: {dup.accounts}")
                
                return {
                    'total_campaigns': total_campaigns,
                    'today_campaigns': today_total,
                    'pending_review_count': len(pending_campaigns),
                    'duplicate_violations': len(today_duplicates),
                    'campaign_status': campaign_status,
                    'pending_campaigns': pending_campaigns,
                    'duplicate_details': today_duplicates
                }
                
        except Exception as e:
            logger.error(f"❌ 紧急状态确认失败: {e}")
            return None
    
    def step2_duplicate_violation_fix(self, status_info):
        """第二步：重复计划违规修复"""
        logger.critical("\n🔧 第二步：重复计划违规修复")
        logger.critical("=" * 60)
        
        if not status_info or not status_info['duplicate_details']:
            logger.critical("✅ 没有发现重复计划违规")
            return True
        
        try:
            with database_session() as db:
                logger.critical(f"🚨 发现 {len(status_info['duplicate_details'])} 个重复违规")
                
                # 清理重复计划（保留最早创建的）
                cleaned_count = 0
                
                for duplicate in status_info['duplicate_details']:
                    filename = duplicate.filename
                    file_hash = duplicate.file_hash
                    
                    logger.critical(f"\n🔧 处理重复视频: {filename}")
                    
                    # 查找该视频的所有计划，按创建时间排序
                    duplicate_campaigns_query = text("""
                        SELECT 
                            c.id,
                            c.campaign_id_qc,
                            c.created_at,
                            aa.name as account_name
                        FROM campaigns c
                        JOIN ad_accounts aa ON c.account_id = aa.id
                        JOIN campaign_platform_creative_association cpca ON c.id = cpca.campaign_id
                        JOIN platform_creatives pc ON cpca.platform_creative_id = pc.id
                        JOIN local_creatives lc ON pc.local_creative_id = lc.id
                        WHERE lc.file_hash = :file_hash
                        AND aa.account_type = 'TEST'
                        ORDER BY c.created_at ASC
                    """)
                    
                    campaigns = db.execute(duplicate_campaigns_query, {'file_hash': file_hash}).fetchall()
                    
                    if len(campaigns) > 1:
                        # 保留第一个（最早创建的），删除其他的
                        keep_campaign = campaigns[0]
                        delete_campaigns = campaigns[1:]
                        
                        logger.critical(f"  保留计划: {keep_campaign.campaign_id_qc} (最早创建)")
                        logger.critical(f"  删除计划: {len(delete_campaigns)} 个")
                        
                        for del_campaign in delete_campaigns:
                            logger.critical(f"    删除: {del_campaign.campaign_id_qc}")
                            
                            # 删除关联关系
                            delete_association_query = text("""
                                DELETE FROM campaign_platform_creative_association
                                WHERE campaign_id = :campaign_id
                            """)
                            db.execute(delete_association_query, {'campaign_id': del_campaign.id})
                            
                            # 删除计划
                            delete_campaign_query = text("""
                                DELETE FROM campaigns
                                WHERE id = :campaign_id
                            """)
                            db.execute(delete_campaign_query, {'campaign_id': del_campaign.id})
                            
                            cleaned_count += 1
                
                db.commit()
                
                logger.critical(f"\n✅ 重复计划清理完成:")
                logger.critical(f"  清理的重复计划: {cleaned_count} 个")
                logger.critical(f"  保留的唯一计划: {len(status_info['duplicate_details'])} 个")
                
                return True
                
        except Exception as e:
            logger.error(f"❌ 重复计划修复失败: {e}")
            return False
    
    def step3_submission_process_check(self):
        """第三步：计划提审流程检查"""
        logger.critical("\n🔍 第三步：计划提审流程检查")
        logger.critical("=" * 60)
        
        # 检查工作流代码中的提审逻辑
        scheduler_file = "src/qianchuan_aw/workflows/scheduler.py"
        
        try:
            with open(scheduler_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 搜索提审相关的代码
            submission_patterns = [
                '提审',
                'submit',
                'review',
                'approve',
                'audit',
                'pending_review'
            ]
            
            found_submission_logic = []
            lines = content.split('\n')
            
            for i, line in enumerate(lines):
                for pattern in submission_patterns:
                    if pattern in line.lower():
                        found_submission_logic.append(f"第{i+1}行: {line.strip()}")
            
            logger.critical(f"📊 提审流程代码检查:")
            logger.critical(f"  找到相关代码: {len(found_submission_logic)} 处")
            
            if found_submission_logic:
                logger.critical("  相关代码片段:")
                for logic in found_submission_logic[:10]:  # 显示前10个
                    logger.critical(f"    {logic}")
            else:
                logger.critical("  ❌ 未找到提审相关代码")
            
            # 检查是否有提审任务
            tasks_file = "src/qianchuan_aw/workflows/tasks.py"
            
            if Path(tasks_file).exists():
                with open(tasks_file, 'r', encoding='utf-8') as f:
                    tasks_content = f.read()
                
                submission_tasks = []
                for pattern in submission_patterns:
                    if pattern in tasks_content.lower():
                        submission_tasks.append(pattern)
                
                logger.critical(f"\n📊 提审任务检查:")
                logger.critical(f"  找到提审任务: {len(submission_tasks)} 个")
                
                if submission_tasks:
                    logger.critical(f"  任务类型: {', '.join(submission_tasks)}")
                else:
                    logger.critical("  ❌ 未找到提审任务")
            
            # 检查配置文件中的提审设置
            config_file = "config/settings.yml"
            
            if Path(config_file).exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_content = f.read()
                
                submission_config = []
                for pattern in submission_patterns:
                    if pattern in config_content.lower():
                        submission_config.append(pattern)
                
                logger.critical(f"\n📊 提审配置检查:")
                logger.critical(f"  找到提审配置: {len(submission_config)} 个")
                
                if 'TEST' in config_content and ('提审' in config_content or 'submit' in config_content.lower()):
                    logger.critical("  ✅ 发现测试账户提审配置")
                else:
                    logger.critical("  ❌ 未发现测试账户提审配置")
            
            return {
                'scheduler_logic': len(found_submission_logic) > 0,
                'tasks_logic': len(submission_tasks) > 0 if 'submission_tasks' in locals() else False,
                'config_logic': len(submission_config) > 0 if 'submission_config' in locals() else False
            }
            
        except Exception as e:
            logger.error(f"❌ 提审流程检查失败: {e}")
            return None
    
    def step4_workflow_integrity_check(self):
        """第四步：工作流完整性验证"""
        logger.critical("\n🔍 第四步：工作流完整性验证")
        logger.critical("=" * 60)
        
        try:
            with database_session() as db:
                # 检查完整的工作流状态分布
                workflow_status_query = text("""
                    SELECT 
                        lc.status,
                        COUNT(*) as count,
                        COUNT(CASE WHEN lc.updated_at > NOW() - INTERVAL '1 day' THEN 1 END) as recent_count
                    FROM local_creatives lc
                    GROUP BY lc.status
                    ORDER BY count DESC
                """)
                
                workflow_status = db.execute(workflow_status_query).fetchall()
                
                logger.critical("📊 素材工作流状态分布:")
                total_materials = sum(row.count for row in workflow_status)
                
                for row in workflow_status:
                    percentage = (row.count / total_materials * 100) if total_materials > 0 else 0
                    logger.critical(f"  {row.status}: {row.count} 个 ({percentage:.1f}%) - 最近24h: {row.recent_count}")
                
                # 检查状态流转异常
                status_flow_check_query = text("""
                    -- 检查有计划但状态不正确的素材
                    SELECT 
                        lc.status,
                        COUNT(*) as count
                    FROM local_creatives lc
                    JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                    JOIN campaigns c ON cpca.campaign_id = c.id
                    WHERE lc.status NOT IN ('testing_pending_review', 'approved', 'rejected', 'already_tested')
                    GROUP BY lc.status
                """)
                
                flow_anomalies = db.execute(status_flow_check_query).fetchall()
                
                logger.critical(f"\n📊 状态流转异常检查:")
                if flow_anomalies:
                    logger.critical("  发现异常:")
                    for anomaly in flow_anomalies:
                        logger.critical(f"    {anomaly.status}: {anomaly.count} 个素材有计划但状态不正确")
                else:
                    logger.critical("  ✅ 状态流转正常")
                
                # 检查数据库与平台状态同步
                sync_check_query = text("""
                    SELECT 
                        COUNT(CASE WHEN lc.status = 'new' AND pc.id IS NOT NULL THEN 1 END) as new_but_uploaded,
                        COUNT(CASE WHEN lc.status = 'uploaded_pending_plan' AND c.id IS NOT NULL THEN 1 END) as pending_but_has_plan,
                        COUNT(CASE WHEN lc.status = 'creating_plan' AND c.id IS NOT NULL THEN 1 END) as creating_but_has_plan
                    FROM local_creatives lc
                    LEFT JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    LEFT JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                    LEFT JOIN campaigns c ON cpca.campaign_id = c.id
                """)
                
                sync_result = db.execute(sync_check_query).fetchone()
                
                logger.critical(f"\n📊 数据同步状态检查:")
                logger.critical(f"  new状态但已上传: {sync_result.new_but_uploaded} 个")
                logger.critical(f"  pending状态但已有计划: {sync_result.pending_but_has_plan} 个")
                logger.critical(f"  creating状态但已有计划: {sync_result.creating_but_has_plan} 个")
                
                sync_issues = sync_result.new_but_uploaded + sync_result.pending_but_has_plan + sync_result.creating_but_has_plan
                
                if sync_issues > 0:
                    logger.critical(f"  ⚠️ 发现 {sync_issues} 个同步问题")
                else:
                    logger.critical("  ✅ 数据同步正常")
                
                return {
                    'workflow_status': workflow_status,
                    'flow_anomalies': flow_anomalies,
                    'sync_issues': sync_issues,
                    'total_materials': total_materials
                }
                
        except Exception as e:
            logger.error(f"❌ 工作流完整性检查失败: {e}")
            return None
    
    def step5_business_rules_audit(self):
        """第五步：业务规则合规性审计"""
        logger.critical("\n🔍 第五步：业务规则合规性审计")
        logger.critical("=" * 60)
        
        try:
            with database_session() as db:
                # 审计测试阶段的唯一性规则
                test_uniqueness_query = text("""
                    WITH test_campaigns AS (
                        SELECT 
                            lc.filename,
                            lc.file_hash,
                            COUNT(c.id) as campaign_count,
                            STRING_AGG(c.campaign_id_qc, ', ') as campaign_ids,
                            STRING_AGG(aa.name, ', ') as account_names
                        FROM local_creatives lc
                        JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                        JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                        JOIN campaigns c ON cpca.campaign_id = c.id
                        JOIN ad_accounts aa ON c.account_id = aa.id
                        WHERE aa.account_type = 'TEST'
                        GROUP BY lc.filename, lc.file_hash
                        HAVING COUNT(c.id) > 1
                    )
                    SELECT * FROM test_campaigns
                    ORDER BY campaign_count DESC
                """)
                
                test_violations = db.execute(test_uniqueness_query).fetchall()
                
                logger.critical("📊 测试阶段唯一性规则审计:")
                logger.critical(f"  违规视频数: {len(test_violations)} 个")
                
                if test_violations:
                    logger.critical("  违规详情:")
                    for violation in test_violations:
                        logger.critical(f"    📋 {violation.filename}:")
                        logger.critical(f"      测试计划数: {violation.campaign_count} (应该为1)")
                        logger.critical(f"      计划ID: {violation.campaign_ids}")
                        logger.critical(f"      测试账户: {violation.account_names}")
                
                # 审计正式投放阶段的规则
                formal_campaigns_query = text("""
                    SELECT 
                        lc.filename,
                        COUNT(c.id) as formal_campaign_count
                    FROM local_creatives lc
                    JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                    JOIN campaigns c ON cpca.campaign_id = c.id
                    JOIN ad_accounts aa ON c.account_id = aa.id
                    WHERE aa.account_type = 'FORMAL'
                    GROUP BY lc.filename, lc.file_hash
                    HAVING COUNT(c.id) > 0
                    ORDER BY formal_campaign_count DESC
                    LIMIT 10
                """)
                
                formal_campaigns = db.execute(formal_campaigns_query).fetchall()
                
                logger.critical(f"\n📊 正式投放阶段规则审计:")
                logger.critical(f"  有正式计划的视频: {len(formal_campaigns)} 个")
                
                if formal_campaigns:
                    logger.critical("  正式投放示例:")
                    for campaign in formal_campaigns:
                        logger.critical(f"    {campaign.filename}: {campaign.formal_campaign_count} 个正式计划")
                
                # 审计账户类型区分逻辑
                account_type_query = text("""
                    SELECT 
                        aa.account_type,
                        COUNT(DISTINCT aa.id) as account_count,
                        COUNT(c.id) as campaign_count,
                        aa.status
                    FROM ad_accounts aa
                    LEFT JOIN campaigns c ON aa.id = c.account_id
                    GROUP BY aa.account_type, aa.status
                    ORDER BY aa.account_type, aa.status
                """)
                
                account_types = db.execute(account_type_query).fetchall()
                
                logger.critical(f"\n📊 账户类型区分审计:")
                for account in account_types:
                    logger.critical(f"  {account.account_type} ({account.status}): {account.account_count} 个账户, {account.campaign_count} 个计划")
                
                return {
                    'test_violations': len(test_violations),
                    'formal_campaigns': len(formal_campaigns),
                    'account_types': account_types,
                    'compliance_score': 100 - (len(test_violations) * 10)  # 每个违规扣10分
                }
                
        except Exception as e:
            logger.error(f"❌ 业务规则审计失败: {e}")
            return None
    
    def generate_comprehensive_diagnosis_report(self, results):
        """生成综合诊断报告"""
        logger.critical("\n📋 千川工作流综合诊断报告")
        logger.critical("=" * 60)
        
        status_info = results.get('status_info')
        submission_check = results.get('submission_check')
        integrity_check = results.get('integrity_check')
        compliance_audit = results.get('compliance_audit')
        
        logger.critical("🎯 诊断概要:")
        logger.critical(f"  诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.critical(f"  工作流状态: CRITICAL")
        
        if status_info:
            logger.critical(f"  总计划数: {status_info['total_campaigns']}")
            logger.critical(f"  今天创建: {status_info['today_campaigns']}")
            logger.critical(f"  未提审计划: {status_info['pending_review_count']}")
            logger.critical(f"  重复违规: {status_info['duplicate_violations']}")
        
        logger.critical("\n🚨 发现的严重问题:")
        
        # 问题1：重复计划创建
        if status_info and status_info['duplicate_violations'] > 0:
            logger.critical(f"  1. 重复计划创建违规: {status_info['duplicate_violations']} 个视频")
            logger.critical(f"     严重程度: CRITICAL")
            logger.critical(f"     影响: 违反核心业务规则")
        
        # 问题2：缺失提审流程
        if submission_check:
            missing_components = []
            if not submission_check['scheduler_logic']:
                missing_components.append('调度器提审逻辑')
            if not submission_check['tasks_logic']:
                missing_components.append('提审任务')
            if not submission_check['config_logic']:
                missing_components.append('提审配置')
            
            if missing_components:
                logger.critical(f"  2. 缺失提审流程组件: {', '.join(missing_components)}")
                logger.critical(f"     严重程度: HIGH")
                logger.critical(f"     影响: 计划无法自动提审")
        
        # 问题3：工作流完整性问题
        if integrity_check and integrity_check['sync_issues'] > 0:
            logger.critical(f"  3. 数据同步问题: {integrity_check['sync_issues']} 个")
            logger.critical(f"     严重程度: MEDIUM")
            logger.critical(f"     影响: 状态不一致")
        
        # 问题4：业务规则合规性
        if compliance_audit and compliance_audit['test_violations'] > 0:
            logger.critical(f"  4. 业务规则违规: {compliance_audit['test_violations']} 个")
            logger.critical(f"     严重程度: CRITICAL")
            logger.critical(f"     合规评分: {compliance_audit['compliance_score']}/100")
        
        logger.critical("\n💡 修复优先级:")
        logger.critical("  🚨 CRITICAL: 立即清理重复计划")
        logger.critical("  ⚠️ HIGH: 实现计划提审流程")
        logger.critical("  🟡 MEDIUM: 修复数据同步问题")
        logger.critical("  🔵 LOW: 优化错误处理机制")
        
        logger.critical("\n⚠️ 重要提醒:")
        logger.critical("  - 在所有问题修复前，工作流不得重启")
        logger.critical("  - 必须进行小批量测试验证")
        logger.critical("  - 严格遵循业务规则")

def main():
    """主诊断函数"""
    try:
        diagnostic = EmergencyWorkflowDiagnostic()
        
        results = {}
        
        # 第一步：紧急状态确认
        results['status_info'] = diagnostic.step1_emergency_status_check()
        
        # 第二步：重复计划违规修复
        if results['status_info']:
            diagnostic.step2_duplicate_violation_fix(results['status_info'])
        
        # 第三步：计划提审流程检查
        results['submission_check'] = diagnostic.step3_submission_process_check()
        
        # 第四步：工作流完整性验证
        results['integrity_check'] = diagnostic.step4_workflow_integrity_check()
        
        # 第五步：业务规则合规性审计
        results['compliance_audit'] = diagnostic.step5_business_rules_audit()
        
        # 生成综合诊断报告
        diagnostic.generate_comprehensive_diagnosis_report(results)
        
        logger.critical(f"\n🎉 千川工作流紧急诊断完成!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 紧急诊断失败: {e}")
        return False

if __name__ == "__main__":
    main()
