#!/usr/bin/env python3
"""
导出506个new状态素材的Excel清单
用于手动核查和重新入库处理
"""

import sys
import os
import pandas as pd
from datetime import datetime
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text

class NewMaterialsExcelExporter:
    """new状态素材Excel导出器"""
    
    def __init__(self):
        logger.critical("📋 导出506个new状态素材Excel清单")
        logger.critical("=" * 60)
        self.export_dir = Path("ai_reports/material_management")
        self.export_dir.mkdir(parents=True, exist_ok=True)
    
    def export_new_materials_to_excel(self):
        """导出new状态素材到Excel"""
        logger.critical("📊 查询new状态素材详细信息")
        
        try:
            with database_session() as db:
                # 查询所有new状态素材的详细信息
                query = text("""
                    SELECT
                        lc.id as 素材ID,
                        lc.filename as 文件名,
                        lc.file_hash as 文件哈希,
                        lc.file_path as 文件路径,
                        lc.status as 当前状态,
                        lc.created_at as 创建时间,
                        lc.updated_at as 更新时间,
                        lc.material_id_qc as 千川素材ID,
                        lc.harvest_status as 收获状态,
                        p.name as 主体名称,
                        p.id as 主体ID,
                        -- 检查是否有关联的平台素材
                        CASE
                            WHEN pc.id IS NOT NULL THEN '是'
                            ELSE '否'
                        END as 是否已上传平台,
                        -- 检查是否有关联的计划
                        CASE
                            WHEN c.id IS NOT NULL THEN '是'
                            ELSE '否'
                        END as 是否有关联计划,
                        COUNT(c.id) as 关联计划数量
                    FROM local_creatives lc
                    LEFT JOIN principals p ON lc.principal_id = p.id
                    LEFT JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    LEFT JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                    LEFT JOIN campaigns c ON cpca.campaign_id = c.id
                    WHERE lc.status = 'new'
                    GROUP BY lc.id, lc.filename, lc.file_hash, lc.file_path,
                             lc.status, lc.created_at, lc.updated_at, lc.material_id_qc, lc.harvest_status,
                             p.name, p.id, pc.id, c.id
                    ORDER BY lc.created_at DESC
                """)
                
                results = db.execute(query).fetchall()
                
                logger.critical(f"📊 查询到 {len(results)} 个new状态素材")
                
                # 转换为DataFrame
                df = pd.DataFrame([dict(row._mapping) for row in results])

                logger.critical(f"📊 DataFrame列名: {list(df.columns)}")

                # 添加额外的分析列 - 通过文件路径获取文件大小
                def get_file_size_mb(file_path):
                    try:
                        if file_path and Path(file_path).exists():
                            return round(Path(file_path).stat().st_size / 1024 / 1024, 2)
                        return 0
                    except:
                        return 0

                df['文件大小MB'] = df['文件路径'].apply(get_file_size_mb)
                
                # 文件路径分析
                df['文件存在'] = df['文件路径'].apply(lambda x: '是' if x and Path(x).exists() else '否')
                df['文件目录'] = df['文件路径'].apply(lambda x: str(Path(x).parent) if x else '')
                
                # 按主体统计
                principal_stats = df.groupby('主体名称').size().to_dict()
                df['同主体素材数'] = df['主体名称'].map(principal_stats)

                # 重新排列列顺序 - 使用实际的列名
                columns_order = [
                    '素材id', '文件名', '主体名称', '主体id', '当前状态',
                    '文件大小MB', '创建时间', '更新时间', '千川素材id', '收获状态',
                    '文件路径', '文件存在', '文件目录',
                    '是否已上传平台', '是否有关联计划', '关联计划数量',
                    '同主体素材数', '文件哈希'
                ]
                
                df = df[columns_order]
                
                # 导出Excel文件
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                excel_file = self.export_dir / f"new_materials_清单_{timestamp}.xlsx"
                
                with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                    # 主表
                    df.to_excel(writer, sheet_name='素材清单', index=False)
                    
                    # 按主体分组统计表
                    principal_summary = df.groupby('主体名称').agg({
                        '素材id': 'count',
                        '文件大小MB': 'sum',
                        '文件存在': lambda x: (x == '是').sum()
                    }).rename(columns={
                        '素材id': '素材数量',
                        '文件大小MB': '总文件大小MB',
                        '文件存在': '文件存在数量'
                    })
                    principal_summary.to_excel(writer, sheet_name='按主体统计')
                    
                    # 文件状态统计表
                    status_summary = pd.DataFrame({
                        '统计项': [
                            '总素材数量',
                            '文件存在数量',
                            '文件缺失数量',
                            '已上传平台数量',
                            '有关联计划数量',
                            '总文件大小MB',
                            '平均文件大小MB'
                        ],
                        '数值': [
                            len(df),
                            (df['文件存在'] == '是').sum(),
                            (df['文件存在'] == '否').sum(),
                            (df['是否已上传平台'] == '是').sum(),
                            (df['是否有关联计划'] == '是').sum(),
                            df['文件大小MB'].sum(),
                            df['文件大小MB'].mean()
                        ]
                    })
                    status_summary.to_excel(writer, sheet_name='状态统计', index=False)
                
                logger.critical(f"✅ Excel文件导出完成: {excel_file}")
                
                # 显示统计信息
                logger.critical(f"\n📊 素材清单统计:")
                logger.critical(f"  总素材数量: {len(df)}")
                logger.critical(f"  文件存在: {(df['文件存在'] == '是').sum()}")
                logger.critical(f"  文件缺失: {(df['文件存在'] == '否').sum()}")
                logger.critical(f"  已上传平台: {(df['是否已上传平台'] == '是').sum()}")
                logger.critical(f"  有关联计划: {(df['是否有关联计划'] == '是').sum()}")
                
                logger.critical(f"\n📋 按主体分布:")
                for principal, count in principal_stats.items():
                    logger.critical(f"  {principal}: {count}个")
                
                return {
                    'excel_file': str(excel_file),
                    'total_count': len(df),
                    'file_exists_count': (df['文件存在'] == '是').sum(),
                    'file_missing_count': (df['文件存在'] == '否').sum(),
                    'uploaded_count': (df['是否已上传平台'] == '是').sum(),
                    'has_campaign_count': (df['是否有关联计划'] == '是').sum(),
                    'principal_stats': principal_stats,
                    'dataframe': df
                }
                
        except Exception as e:
            logger.error(f"❌ 导出Excel失败: {e}")
            return None
    
    def generate_file_move_guide(self, export_result):
        """生成文件移动和入库操作指南"""
        logger.critical("\n📋 生成文件移动和入库操作指南")
        logger.critical("=" * 60)
        
        if not export_result:
            logger.error("❌ 没有导出结果，无法生成操作指南")
            return None
        
        df = export_result['dataframe']
        
        # 筛选文件存在的素材
        existing_files = df[df['文件存在'] == '是'].copy()
        missing_files = df[df['文件存在'] == '否'].copy()
        
        logger.critical(f"📊 文件状态分析:")
        logger.critical(f"  文件存在: {len(existing_files)} 个")
        logger.critical(f"  文件缺失: {len(missing_files)} 个")
        
        # 生成移动脚本
        move_script_content = '''@echo off
REM 506个new状态素材文件移动脚本
REM 目标目录: G:\\workflow_assets\\01_materials_to_process\\缇萃百货
REM 执行前请确保目标目录存在

echo 开始移动506个new状态素材文件...
echo 目标目录: G:\\workflow_assets\\01_materials_to_process\\缇萃百货
echo.

REM 创建目标目录（如果不存在）
if not exist "G:\\workflow_assets\\01_materials_to_process\\缇萃百货" (
    mkdir "G:\\workflow_assets\\01_materials_to_process\\缇萃百货"
    echo 创建目标目录: G:\\workflow_assets\\01_materials_to_process\\缇萃百货
)

set /a success_count=0
set /a error_count=0

'''
        
        # 为每个存在的文件生成移动命令
        for _, row in existing_files.iterrows():
            source_path = row['文件路径']
            filename = row['文件名']
            
            move_script_content += f'''
REM 移动文件: {filename}
if exist "{source_path}" (
    move "{source_path}" "G:\\workflow_assets\\01_materials_to_process\\缇萃百货\\{filename}"
    if errorlevel 1 (
        echo 错误: 移动失败 - {filename}
        set /a error_count+=1
    ) else (
        echo 成功: {filename}
        set /a success_count+=1
    )
) else (
    echo 警告: 源文件不存在 - {filename}
    set /a error_count+=1
)
'''
        
        move_script_content += '''
echo.
echo 移动完成统计:
echo 成功移动: %success_count% 个文件
echo 失败/缺失: %error_count% 个文件
echo.
pause
'''
        
        # 保存移动脚本
        script_file = self.export_dir / f"move_new_materials_{datetime.now().strftime('%Y%m%d_%H%M%S')}.bat"
        with open(script_file, 'w', encoding='gbk') as f:
            f.write(move_script_content)
        
        logger.critical(f"✅ 文件移动脚本生成: {script_file}")
        
        # 生成数据库更新SQL
        sql_content = '''-- 更新506个new状态素材的文件路径
-- 执行前请确保文件已成功移动到目标目录

BEGIN;

'''
        
        for _, row in existing_files.iterrows():
            material_id = row['素材id']
            filename = row['文件名']
            new_path = f"G:\\workflow_assets\\01_materials_to_process\\缇萃百货\\{filename}"
            
            sql_content += f'''-- 更新素材ID {material_id} 的文件路径
UPDATE local_creatives 
SET file_path = '{new_path}',
    updated_at = NOW()
WHERE id = {material_id};

'''
        
        sql_content += '''
-- 提交事务
COMMIT;

-- 验证更新结果
SELECT 
    COUNT(*) as 更新数量,
    COUNT(CASE WHEN file_path LIKE 'G:\\workflow_assets\\01_materials_to_process\\缇萃百货\\%' THEN 1 END) as 新路径数量
FROM local_creatives 
WHERE status = 'new';
'''
        
        # 保存SQL脚本
        sql_file = self.export_dir / f"update_file_paths_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql"
        with open(sql_file, 'w', encoding='utf-8') as f:
            f.write(sql_content)
        
        logger.critical(f"✅ 数据库更新SQL生成: {sql_file}")
        
        # 生成操作指南文档
        guide_content = f'''# 506个new状态素材重新入库操作指南

## 📊 素材统计
- 总素材数量: {export_result['total_count']}
- 文件存在: {export_result['file_exists_count']}
- 文件缺失: {export_result['file_missing_count']}
- 已上传平台: {export_result['uploaded_count']}
- 有关联计划: {export_result['has_campaign_count']}

## 🎯 业务规则确认
- **测试阶段**: 每个视频文件只能创建唯一的一个测试计划
- **正式投放**: 同一个视频可以创建多个计划
- **程序约束**: 通过file_hash强制执行测试阶段唯一性

## 📋 操作步骤

### 第一步: 手动核查Excel清单
1. 打开Excel文件: `{export_result['excel_file']}`
2. 核查"素材清单"工作表中的所有素材
3. 确认这些素材确实没有在任何测试账户中创建过计划
4. 验证文件完整性和可用性

### 第二步: 执行文件移动
1. 运行批处理脚本: `{script_file}`
2. 脚本将自动移动{export_result['file_exists_count']}个存在的文件到目标目录
3. 目标目录: `G:\\workflow_assets\\01_materials_to_process\\缇萃百货`
4. 检查移动结果，确保所有文件都成功移动

### 第三步: 更新数据库路径
1. 执行SQL脚本: `{sql_file}`
2. 更新数据库中的文件路径信息
3. 验证更新结果

### 第四步: 重启工作流
1. 确保Celery服务已停止
2. 重新启动工作流程序:
   ```bash
   python run_celery_beat.py &
   python run_celery_worker.py &
   ```
3. 监控工作流运行，确保按照修复后的逻辑处理

## ⚠️ 注意事项
- 移动文件前请备份重要数据
- 确保目标目录有足够的存储空间
- 移动过程中不要中断操作
- 数据库更新前请创建备份

## 🔍 监控要点
- 确保每个视频只创建一个测试计划
- 监控是否有重复计划创建
- 检查工作流处理速度和成功率

## 📞 问题处理
如果遇到问题，请检查:
1. 文件路径是否正确
2. 权限是否足够
3. 磁盘空间是否充足
4. 数据库连接是否正常
'''
        
        guide_file = self.export_dir / f"操作指南_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        logger.critical(f"✅ 操作指南生成: {guide_file}")
        
        return {
            'script_file': str(script_file),
            'sql_file': str(sql_file),
            'guide_file': str(guide_file),
            'existing_files_count': len(existing_files),
            'missing_files_count': len(missing_files)
        }

def main():
    """主导出函数"""
    try:
        exporter = NewMaterialsExcelExporter()
        
        # 1. 导出Excel清单
        export_result = exporter.export_new_materials_to_excel()
        
        if not export_result:
            logger.error("❌ Excel导出失败")
            return False
        
        # 2. 生成操作指南
        guide_result = exporter.generate_file_move_guide(export_result)
        
        if not guide_result:
            logger.error("❌ 操作指南生成失败")
            return False
        
        logger.critical(f"\n🎉 506个素材Excel清单导出完成!")
        logger.critical(f"Excel文件: {export_result['excel_file']}")
        logger.critical(f"移动脚本: {guide_result['script_file']}")
        logger.critical(f"SQL脚本: {guide_result['sql_file']}")
        logger.critical(f"操作指南: {guide_result['guide_file']}")
        
        logger.critical(f"\n📊 处理统计:")
        logger.critical(f"总素材: {export_result['total_count']}")
        logger.critical(f"可移动文件: {guide_result['existing_files_count']}")
        logger.critical(f"缺失文件: {guide_result['missing_files_count']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 导出失败: {e}")
        return False

if __name__ == "__main__":
    main()
