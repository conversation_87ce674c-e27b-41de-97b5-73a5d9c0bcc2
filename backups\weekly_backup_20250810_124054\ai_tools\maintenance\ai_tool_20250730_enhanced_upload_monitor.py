#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期监控工具
生命周期: 永久保留
创建目的: 增强的上传监控和智能重试系统
清理条件: 成为核心功能后可移至正式目录
"""

import os
import sys
import time
import json
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
from enum import Enum

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger

class UploadStatus(Enum):
    """上传状态枚举"""
    PENDING = "pending"
    UPLOADING = "uploading"
    SUCCESS = "success"
    FAILED = "failed"
    RETRY_PENDING = "retry_pending"
    PERMANENTLY_FAILED = "permanently_failed"

class FailureType(Enum):
    """失败类型枚举"""
    NETWORK_ERROR = "network_error"
    BINDING_FAILED = "binding_failed"
    FILE_ERROR = "file_error"
    API_ERROR = "api_error"
    PLATFORM_ERROR = "platform_error"
    UNKNOWN_ERROR = "unknown_error"

@dataclass
class UploadAttempt:
    """上传尝试记录"""
    attempt_number: int
    timestamp: datetime
    error_message: str
    failure_type: FailureType
    duration_seconds: float
    
    def to_dict(self):
        return {
            'attempt_number': self.attempt_number,
            'timestamp': self.timestamp.isoformat(),
            'error_message': self.error_message,
            'failure_type': self.failure_type.value,
            'duration_seconds': self.duration_seconds
        }

@dataclass
class UploadTask:
    """上传任务"""
    video_path: str
    advertiser_id: int
    file_hash: str
    status: UploadStatus
    created_at: datetime
    attempts: List[UploadAttempt]
    max_retries: int = 5
    next_retry_at: Optional[datetime] = None
    platform_creative_id: Optional[int] = None
    
    def to_dict(self):
        return {
            'video_path': self.video_path,
            'advertiser_id': self.advertiser_id,
            'file_hash': self.file_hash,
            'status': self.status.value,
            'created_at': self.created_at.isoformat(),
            'attempts': [attempt.to_dict() for attempt in self.attempts],
            'max_retries': self.max_retries,
            'next_retry_at': self.next_retry_at.isoformat() if self.next_retry_at else None,
            'platform_creative_id': self.platform_creative_id
        }

class EnhancedUploadMonitor:
    """增强的上传监控系统"""
    
    def __init__(self):
        self.tasks_file = project_root / "logs" / "upload_tasks.json"
        self.monitor_log = project_root / "logs" / "upload_monitor.log"
        self.ensure_files()
        
        # 重试策略配置
        self.retry_delays = [30, 60, 300, 900, 1800]  # 30秒, 1分钟, 5分钟, 15分钟, 30分钟
        self.max_retries = 5
    
    def ensure_files(self):
        """确保文件存在"""
        self.tasks_file.parent.mkdir(exist_ok=True)
        if not self.tasks_file.exists():
            self.save_tasks([])
    
    def load_tasks(self) -> List[UploadTask]:
        """加载上传任务"""
        try:
            with open(self.tasks_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            tasks = []
            for item in data:
                attempts = []
                for attempt_data in item.get('attempts', []):
                    attempt = UploadAttempt(
                        attempt_number=attempt_data['attempt_number'],
                        timestamp=datetime.fromisoformat(attempt_data['timestamp']),
                        error_message=attempt_data['error_message'],
                        failure_type=FailureType(attempt_data['failure_type']),
                        duration_seconds=attempt_data['duration_seconds']
                    )
                    attempts.append(attempt)
                
                task = UploadTask(
                    video_path=item['video_path'],
                    advertiser_id=item['advertiser_id'],
                    file_hash=item['file_hash'],
                    status=UploadStatus(item['status']),
                    created_at=datetime.fromisoformat(item['created_at']),
                    attempts=attempts,
                    max_retries=item.get('max_retries', 5),
                    next_retry_at=datetime.fromisoformat(item['next_retry_at']) if item.get('next_retry_at') else None,
                    platform_creative_id=item.get('platform_creative_id')
                )
                tasks.append(task)
            
            return tasks
        except (FileNotFoundError, json.JSONDecodeError, KeyError):
            return []
    
    def save_tasks(self, tasks: List[UploadTask]):
        """保存上传任务"""
        data = [task.to_dict() for task in tasks]
        with open(self.tasks_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def create_upload_task(self, video_path: str, advertiser_id: int, file_hash: str) -> UploadTask:
        """创建上传任务"""
        task = UploadTask(
            video_path=video_path,
            advertiser_id=advertiser_id,
            file_hash=file_hash,
            status=UploadStatus.PENDING,
            created_at=datetime.now(),
            attempts=[]
        )
        
        tasks = self.load_tasks()
        tasks.append(task)
        self.save_tasks(tasks)
        
        logger.info(f"📝 创建上传任务: {os.path.basename(video_path)}")
        return task
    
    def record_upload_attempt(self, video_path: str, advertiser_id: int, 
                            error_message: str, failure_type: FailureType, 
                            duration_seconds: float):
        """记录上传尝试"""
        tasks = self.load_tasks()
        
        for task in tasks:
            if task.video_path == video_path and task.advertiser_id == advertiser_id:
                attempt_number = len(task.attempts) + 1
                attempt = UploadAttempt(
                    attempt_number=attempt_number,
                    timestamp=datetime.now(),
                    error_message=error_message,
                    failure_type=failure_type,
                    duration_seconds=duration_seconds
                )
                task.attempts.append(attempt)
                
                # 更新任务状态
                if attempt_number >= task.max_retries:
                    task.status = UploadStatus.PERMANENTLY_FAILED
                    task.next_retry_at = None
                    logger.error(f"❌ 任务永久失败: {os.path.basename(video_path)} (尝试 {attempt_number} 次)")
                else:
                    task.status = UploadStatus.RETRY_PENDING
                    # 计算下次重试时间（指数退避）
                    delay_index = min(attempt_number - 1, len(self.retry_delays) - 1)
                    delay_seconds = self.retry_delays[delay_index]
                    task.next_retry_at = datetime.now() + timedelta(seconds=delay_seconds)
                    logger.warning(f"⏰ 安排重试: {os.path.basename(video_path)} (第 {attempt_number} 次失败，{delay_seconds}秒后重试)")
                
                break
        
        self.save_tasks(tasks)
    
    def record_upload_success(self, video_path: str, advertiser_id: int, platform_creative_id: int):
        """记录上传成功"""
        tasks = self.load_tasks()
        
        for task in tasks:
            if task.video_path == video_path and task.advertiser_id == advertiser_id:
                task.status = UploadStatus.SUCCESS
                task.platform_creative_id = platform_creative_id
                task.next_retry_at = None
                logger.success(f"✅ 上传成功: {os.path.basename(video_path)} (ID: {platform_creative_id})")
                break
        
        self.save_tasks(tasks)
    
    def get_retry_pending_tasks(self) -> List[UploadTask]:
        """获取待重试的任务"""
        tasks = self.load_tasks()
        now = datetime.now()
        
        retry_tasks = []
        for task in tasks:
            if (task.status == UploadStatus.RETRY_PENDING and 
                task.next_retry_at and 
                task.next_retry_at <= now):
                retry_tasks.append(task)
        
        return retry_tasks
    
    def classify_failure_type(self, error_message: str) -> FailureType:
        """根据错误消息分类失败类型"""
        error_lower = error_message.lower()
        
        if any(keyword in error_lower for keyword in ['network', '网络', 'timeout', '超时', 'connection']):
            return FailureType.NETWORK_ERROR
        elif any(keyword in error_lower for keyword in ['binding', '绑定', 'bind']):
            return FailureType.BINDING_FAILED
        elif any(keyword in error_lower for keyword in ['file', '文件', 'not found', '找不到']):
            return FailureType.FILE_ERROR
        elif any(keyword in error_lower for keyword in ['api', 'response', '响应']):
            return FailureType.API_ERROR
        elif any(keyword in error_lower for keyword in ['platform', '平台', '服务', 'service']):
            return FailureType.PLATFORM_ERROR
        else:
            return FailureType.UNKNOWN_ERROR
    
    def generate_monitoring_report(self) -> Dict:
        """生成监控报告"""
        tasks = self.load_tasks()
        now = datetime.now()
        
        # 基本统计
        total_tasks = len(tasks)
        success_count = len([t for t in tasks if t.status == UploadStatus.SUCCESS])
        failed_count = len([t for t in tasks if t.status == UploadStatus.PERMANENTLY_FAILED])
        retry_pending = len([t for t in tasks if t.status == UploadStatus.RETRY_PENDING])
        
        # 成功率计算
        completed_tasks = success_count + failed_count
        success_rate = (success_count / completed_tasks * 100) if completed_tasks > 0 else 0
        
        # 失败类型统计
        failure_types = {}
        for task in tasks:
            for attempt in task.attempts:
                failure_type = attempt.failure_type.value
                failure_types[failure_type] = failure_types.get(failure_type, 0) + 1
        
        # 最近24小时统计
        cutoff_time = now - timedelta(hours=24)
        recent_tasks = [t for t in tasks if t.created_at > cutoff_time]
        recent_failures = []
        
        for task in recent_tasks:
            if task.attempts:
                recent_failures.extend([
                    {
                        'filename': os.path.basename(task.video_path),
                        'error': attempt.error_message,
                        'type': attempt.failure_type.value,
                        'time': attempt.timestamp.isoformat()
                    }
                    for attempt in task.attempts
                ])
        
        # 待重试任务
        retry_tasks = self.get_retry_pending_tasks()
        
        report = {
            'summary': {
                'total_tasks': total_tasks,
                'success_count': success_count,
                'failed_count': failed_count,
                'retry_pending': retry_pending,
                'success_rate': round(success_rate, 2),
                'ready_for_retry': len(retry_tasks)
            },
            'failure_types': failure_types,
            'recent_failures_24h': recent_failures[-10:],  # 最近10个
            'retry_queue': [
                {
                    'filename': os.path.basename(task.video_path),
                    'attempts': len(task.attempts),
                    'next_retry': task.next_retry_at.isoformat() if task.next_retry_at else None
                }
                for task in retry_tasks[:5]  # 前5个待重试
            ],
            'generated_at': now.isoformat()
        }
        
        return report
    
    def print_monitoring_report(self):
        """打印监控报告"""
        report = self.generate_monitoring_report()
        
        logger.info("📊 上传监控报告")
        logger.info("=" * 60)
        
        summary = report['summary']
        logger.info(f"📈 总任务数: {summary['total_tasks']}")
        logger.info(f"✅ 成功数量: {summary['success_count']}")
        logger.info(f"❌ 失败数量: {summary['failed_count']}")
        logger.info(f"⏳ 待重试数: {summary['retry_pending']}")
        logger.info(f"🎯 成功率: {summary['success_rate']}%")
        logger.info(f"🔄 可立即重试: {summary['ready_for_retry']}")
        
        if report['failure_types']:
            logger.info("\n📋 失败类型分布:")
            for failure_type, count in report['failure_types'].items():
                logger.info(f"  {failure_type}: {count} 次")
        
        if report['retry_queue']:
            logger.info("\n⏰ 重试队列 (前5个):")
            for item in report['retry_queue']:
                logger.info(f"  {item['filename']} - 已尝试 {item['attempts']} 次")
        
        if report['recent_failures_24h']:
            logger.info("\n⚠️ 最近失败记录 (最新10个):")
            for failure in report['recent_failures_24h']:
                logger.info(f"  {failure['filename']} - {failure['error']} ({failure['type']})")

def main():
    """主函数"""
    logger.info("📊 增强上传监控系统")
    logger.info("=" * 60)
    
    monitor = EnhancedUploadMonitor()
    
    # 生成并显示监控报告
    monitor.print_monitoring_report()
    
    # 检查待重试任务
    retry_tasks = monitor.get_retry_pending_tasks()
    if retry_tasks:
        logger.info(f"\n🔄 发现 {len(retry_tasks)} 个可立即重试的任务")
        logger.info("💡 建议运行重试恢复程序")
    else:
        logger.info("\n✅ 当前没有待重试的任务")
    
    logger.info("\n💡 集成建议:")
    logger.info("1. 在 upload_and_register_task 函数中集成监控")
    logger.info("2. 设置定时任务运行重试恢复")
    logger.info("3. 配置告警阈值监控成功率")
    logger.info("4. 定期清理过期的成功任务记录")
    
    logger.info("\n" + "=" * 60)
    logger.info("🎯 监控系统运行完成！")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
