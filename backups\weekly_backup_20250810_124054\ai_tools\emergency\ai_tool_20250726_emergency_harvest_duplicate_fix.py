#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 修复重复收割问题，确保收割铁律
清理条件: 问题解决后可删除

收割重复问题紧急修复工具
======================

修复违反"一个素材只能被收割一次"铁律的问题。
"""

import os
import sys
import shutil
from datetime import datetime
from collections import defaultdict

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from src.qianchuan_aw.database.models import LocalCreative


class HarvestDuplicateFixer:
    """收割重复问题修复器"""
    
    def __init__(self):
        self.approved_base_dir = os.path.join('G:', 'workflow_assets', '03_materials_approved', '缇萃百货')
        self.today = datetime.now().strftime('%Y-%m-%d')
        
    def scan_duplicate_harvests(self):
        """扫描重复收割的文件"""
        logger.info("🔍 扫描重复收割的文件...")
        
        file_locations = defaultdict(list)  # filename -> [date1, date2, ...]
        
        try:
            # 扫描所有日期目录
            for date_dir in os.listdir(self.approved_base_dir):
                date_path = os.path.join(self.approved_base_dir, date_dir)
                
                if os.path.isdir(date_path) and date_dir.startswith('2025-'):
                    logger.info(f"📁 扫描目录: {date_dir}")
                    
                    try:
                        for filename in os.listdir(date_path):
                            if filename.endswith('.mp4'):
                                file_locations[filename].append(date_dir)
                    except Exception as e:
                        logger.warning(f"⚠️ 无法读取目录 {date_dir}: {e}")
            
            # 找出重复的文件
            duplicates = {filename: dates for filename, dates in file_locations.items() if len(dates) > 1}
            
            logger.info(f"📊 扫描结果:")
            logger.info(f"   - 总文件数: {len(file_locations)}")
            logger.info(f"   - 重复文件数: {len(duplicates)}")
            
            if duplicates:
                logger.warning("⚠️ 发现重复收割的文件:")
                for filename, dates in list(duplicates.items())[:10]:  # 只显示前10个
                    logger.warning(f"   - {filename}: {', '.join(dates)}")
                
                if len(duplicates) > 10:
                    logger.warning(f"   ... 还有 {len(duplicates) - 10} 个重复文件")
            
            return duplicates
            
        except Exception as e:
            logger.error(f"❌ 扫描重复收割失败: {e}")
            return {}
    
    def fix_duplicate_harvests(self, duplicates):
        """修复重复收割问题"""
        logger.info("🔧 开始修复重复收割问题...")
        
        if not duplicates:
            logger.info("✅ 没有发现重复收割问题")
            return True
        
        fixed_count = 0
        
        try:
            for filename, dates in duplicates.items():
                logger.info(f"🔄 处理重复文件: {filename}")
                logger.info(f"   出现在日期: {', '.join(dates)}")
                
                # 确定保留哪个版本（保留最早的，删除后续的）
                dates.sort()
                keep_date = dates[0]
                remove_dates = dates[1:]
                
                logger.info(f"   保留版本: {keep_date}")
                logger.info(f"   删除版本: {', '.join(remove_dates)}")
                
                # 删除重复的文件
                for remove_date in remove_dates:
                    duplicate_path = os.path.join(self.approved_base_dir, remove_date, filename)
                    
                    try:
                        if os.path.exists(duplicate_path):
                            os.remove(duplicate_path)
                            logger.info(f"   ✅ 已删除: {remove_date}/{filename}")
                            fixed_count += 1
                        else:
                            logger.warning(f"   ⚠️ 文件不存在: {duplicate_path}")
                    except Exception as e:
                        logger.error(f"   ❌ 删除失败 {duplicate_path}: {e}")
            
            logger.info(f"🎯 重复收割修复完成！删除了 {fixed_count} 个重复文件")
            return True
            
        except Exception as e:
            logger.error(f"❌ 修复重复收割失败: {e}")
            return False
    
    def correct_database_status(self):
        """修正数据库中的收割状态"""
        logger.info("🔧 修正数据库中的收割状态...")
        
        try:
            with database_session() as db:
                # 获取所有已收割的文件名
                harvested_files = set()
                
                for date_dir in os.listdir(self.approved_base_dir):
                    date_path = os.path.join(self.approved_base_dir, date_dir)
                    
                    if os.path.isdir(date_path) and date_dir.startswith('2025-'):
                        try:
                            for filename in os.listdir(date_path):
                                if filename.endswith('.mp4'):
                                    harvested_files.add(filename)
                        except Exception as e:
                            logger.warning(f"⚠️ 无法读取目录 {date_dir}: {e}")
                
                logger.info(f"📊 发现 {len(harvested_files)} 个已收割文件")
                
                # 更新数据库状态
                updated_count = 0
                
                for filename in harvested_files:
                    materials = db.query(LocalCreative).filter(
                        LocalCreative.filename == filename
                    ).all()
                    
                    for material in materials:
                        if material.harvest_status != 'harvested':
                            material.harvest_status = 'harvested'
                            material.updated_at = datetime.now()
                            updated_count += 1
                
                db.commit()
                
                logger.info(f"✅ 已更新 {updated_count} 个素材的收割状态")
                return True
                
        except Exception as e:
            logger.error(f"❌ 修正数据库状态失败: {e}")
            return False
    
    def create_proper_harvest_tool(self):
        """创建正确的收割工具"""
        logger.info("🔧 创建符合收割铁律的收割工具...")
        
        proper_tool_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的素材收割工具
================

遵循收割铁律：一个素材只能被收割一次
"""

import os
import sys
import shutil
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from src.qianchuan_aw.database.models import LocalCreative


def proper_harvest():
    """正确的收割操作 - 遵循收割铁律"""
    logger.info("🚀 开始正确的收割操作...")
    
    today = datetime.now().strftime('%Y-%m-%d')
    approved_base_dir = os.path.join('G:', 'workflow_assets', '03_materials_approved', '缇萃百货')
    today_dir = os.path.join(approved_base_dir, today)
    
    try:
        with database_session() as db:
            # 获取所有已收割的文件名（检查所有日期目录）
            already_harvested = set()
            
            if os.path.exists(approved_base_dir):
                for date_dir in os.listdir(approved_base_dir):
                    date_path = os.path.join(approved_base_dir, date_dir)
                    if os.path.isdir(date_path) and date_dir.startswith('2025-'):
                        try:
                            for filename in os.listdir(date_path):
                                if filename.endswith('.mp4'):
                                    already_harvested.add(filename)
                        except:
                            continue
            
            logger.info(f"📊 已收割文件数量: {len(already_harvested)}")
            
            # 查找需要收割的素材（排除已收割的）
            unharvested_materials = db.query(LocalCreative).filter(
                LocalCreative.status == 'already_tested',
                LocalCreative.harvest_status == 'not_harvested',
                ~LocalCreative.filename.in_(already_harvested)  # 排除已收割的
            ).all()
            
            logger.info(f"📊 发现 {len(unharvested_materials)} 个真正需要收割的素材")
            
            if not unharvested_materials:
                logger.info("✅ 没有需要收割的素材")
                return True
            
            # 创建今天的收割目录
            os.makedirs(today_dir, exist_ok=True)
            
            # 执行收割（移动文件，不是复制）
            harvested_count = 0
            for material in unharvested_materials:
                try:
                    if not material.file_path or not os.path.exists(material.file_path):
                        logger.warning(f"⚠️ 源文件不存在: {material.filename}")
                        continue
                    
                    target_path = os.path.join(today_dir, material.filename)
                    
                    # 移动文件（不是复制！）
                    shutil.move(material.file_path, target_path)
                    
                    # 更新数据库状态
                    material.harvest_status = 'harvested'
                    material.file_path = target_path  # 更新文件路径
                    material.updated_at = datetime.now()
                    
                    harvested_count += 1
                    logger.info(f"✅ 收割完成: {material.filename}")
                    
                except Exception as e:
                    logger.error(f"❌ 收割失败 {material.filename}: {e}")
            
            db.commit()
            
            logger.info(f"🎯 收割操作完成！成功收割 {harvested_count} 个素材")
            return harvested_count > 0
            
    except Exception as e:
        logger.error(f"❌ 收割操作失败: {e}")
        return False


if __name__ == '__main__':
    proper_harvest()
'''
        
        tool_path = os.path.join(project_root, 'ai_tools', 'harvest', 'ai_tool_20250726_harvest_proper_harvest.py')
        os.makedirs(os.path.dirname(tool_path), exist_ok=True)
        
        with open(tool_path, 'w', encoding='utf-8') as f:
            f.write(proper_tool_content)
        
        logger.info(f"✅ 正确的收割工具已创建: {tool_path}")
        return tool_path
    
    def generate_fix_report(self, duplicates_count, fixed_count):
        """生成修复报告"""
        report = f"""
收割重复问题修复报告
==================

修复时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🚨 违反的收割铁律:
"一个素材只能被收割一次"

发现的问题:
- 重复收割文件数: {duplicates_count}
- 删除重复文件数: {fixed_count}

问题原因:
1. 使用了 shutil.copy2() 复制文件，而不是移动
2. 没有检查文件是否已在其他日期目录中存在
3. 数据库状态更新不准确

修复措施:
1. ✅ 删除了所有重复收割的文件
2. ✅ 修正了数据库中的收割状态
3. ✅ 创建了符合收割铁律的新工具

收割铁律重申:
1. 每个素材只能被收割一次
2. 收割时必须移动文件，不能复制
3. 收割前必须检查文件是否已存在于任何收割目录
4. 收割后必须更新数据库状态为 'harvested'

正确的收割流程:
1. 查询 status='already_tested' AND harvest_status='not_harvested'
2. 排除已在任何收割目录中存在的文件
3. 使用 shutil.move() 移动文件到今天的收割目录
4. 更新数据库状态和文件路径
5. 提交数据库事务

预防措施:
1. 使用新的正确收割工具
2. 定期检查收割目录中的重复文件
3. 监控数据库状态的一致性
4. 建立收割操作的审计日志

下一步操作:
1. 使用新的正确收割工具进行后续收割
2. 监控确保不再出现重复收割
3. 定期验证收割铁律的遵守情况
"""
        
        return report


def main():
    """主函数"""
    logger.info("🚨 开始修复收割重复问题...")
    
    fixer = HarvestDuplicateFixer()
    
    try:
        # 1. 扫描重复收割
        duplicates = fixer.scan_duplicate_harvests()
        
        # 2. 修复重复收割
        if duplicates:
            logger.info("=" * 60)
            fixer.fix_duplicate_harvests(duplicates)
        
        # 3. 修正数据库状态
        logger.info("=" * 60)
        fixer.correct_database_status()
        
        # 4. 创建正确的收割工具
        logger.info("=" * 60)
        proper_tool_path = fixer.create_proper_harvest_tool()
        
        # 5. 生成修复报告
        logger.info("=" * 60)
        report = fixer.generate_fix_report(len(duplicates), sum(len(dates)-1 for dates in duplicates.values()))
        
        # 保存报告
        report_file = os.path.join(project_root, 'ai_temp', f'harvest_duplicate_fix_{int(__import__("time").time())}.txt')
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"✅ 修复报告已保存: {report_file}")
        logger.info("🎯 收割重复问题修复完成！")
        
        print(report)
        
        logger.info("💡 重要提醒:")
        logger.info("1. 今后请使用新的正确收割工具")
        logger.info("2. 收割铁律：一个素材只能被收割一次")
        logger.info("3. 收割时必须移动文件，不能复制")
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ 修复过程中发生错误: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
