"""
AI生成文件信息
================
文件类型: 工作流修复工具
生命周期: 永久保留
创建目的: 修复视频上传工作流问题
清理条件: 作为维护工具长期保留
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Any
import hashlib
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import LocalCreative, Principal
import yaml


class WorkflowFixer:
    """工作流修复工具"""
    
    def __init__(self):
        # 加载配置
        config_path = project_root / 'config' / 'settings.yml'
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        self.workflow_dir = self.config.get('custom_workflow_assets_dir', 'D:/workflow_assets')
        self.process_dir = os.path.join(self.workflow_dir, '01_materials_to_process', '缇萃百货')
        
    def fix_all_issues(self) -> Dict[str, Any]:
        """修复所有发现的问题"""
        logger.info("🔧 开始工作流修复...")
        
        results = {
            'file_ingestion_fix': self.fix_file_ingestion(),
            'upload_verification_fix': self.fix_upload_verification(),
            'empty_files_cleanup': self.cleanup_empty_files(),
            'database_sync': self.sync_database_with_files()
        }
        
        return results
    
    def fix_file_ingestion(self) -> Dict[str, Any]:
        """修复文件摄取问题"""
        logger.info("📥 修复文件摄取问题...")
        
        result = {
            'files_found': 0,
            'files_ingested': 0,
            'files_skipped': 0,
            'errors': []
        }
        
        if not os.path.exists(self.process_dir):
            result['errors'].append(f"处理目录不存在: {self.process_dir}")
            return result
        
        try:
            with database_session() as db:
                # 获取主体ID（缇萃百货）
                principal = db.query(Principal).filter(Principal.name == '缇萃百货').first()
                if not principal:
                    result['errors'].append("未找到主体'缇萃百货'")
                    return result
                
                principal_id = principal.id
                
                # 扫描文件
                video_extensions = ['.mp4', '.mov', '.avi', '.mkv']
                for filename in os.listdir(self.process_dir):
                    if any(filename.lower().endswith(ext) for ext in video_extensions):
                        result['files_found'] += 1
                        file_path = os.path.join(self.process_dir, filename)
                        
                        # 检查文件是否为空
                        if os.path.getsize(file_path) == 0:
                            logger.warning(f"跳过空文件: {filename}")
                            result['files_skipped'] += 1
                            continue
                        
                        # 计算文件哈希
                        file_hash = self.calculate_file_hash(file_path)
                        
                        # 检查是否已存在
                        existing = db.query(LocalCreative).filter(
                            LocalCreative.file_hash == file_hash
                        ).first()
                        
                        if existing:
                            logger.debug(f"文件已存在: {filename}")
                            result['files_skipped'] += 1
                            continue
                        
                        # 创建新记录
                        new_creative = LocalCreative(
                            principal_id=principal_id,
                            file_path=file_path,
                            filename=filename,
                            file_hash=file_hash,
                            status='new'  # 设置为new状态，等待处理
                        )
                        
                        db.add(new_creative)
                        result['files_ingested'] += 1
                        logger.info(f"摄取文件: {filename}")
                
                db.commit()
                
        except Exception as e:
            logger.error(f"文件摄取失败: {e}")
            result['errors'].append(str(e))
        
        logger.info(f"📊 文件摄取结果: {result['files_ingested']} 个新文件, {result['files_skipped']} 个跳过")
        return result
    
    def fix_upload_verification(self) -> Dict[str, Any]:
        """修复上传验证逻辑"""
        logger.info("🔍 修复上传验证逻辑...")
        
        # 修改上传可靠性增强工具中的验证延迟
        enhancement_file = project_root / 'ai_tools' / 'enhancement' / 'ai_tool_20250731_enhancement_upload_reliability.py'
        
        result = {
            'verification_delay_added': False,
            'retry_logic_improved': False,
            'config_updated': False
        }
        
        try:
            # 读取增强工具文件
            with open(enhancement_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否已经有延迟逻辑
            if 'time.sleep' not in content:
                # 在验证函数中添加延迟
                old_pattern = 'def verify_upload_success(self, client, material_id: str, video_id: str) -> Dict[str, Any]:'
                if old_pattern in content:
                    new_pattern = '''def verify_upload_success(self, client, material_id: str, video_id: str) -> Dict[str, Any]:
        """验证上传成功 - 增加API同步延迟"""
        import time
        
        # 等待API同步，避免立即查询查不到的问题
        logger.info("⏳ 等待API同步...")
        time.sleep(5)  # 等待5秒让API同步'''
                    
                    content = content.replace(old_pattern, new_pattern)
                    result['verification_delay_added'] = True
            
            # 写回文件
            with open(enhancement_file, 'w', encoding='utf-8') as f:
                f.write(content)
                
        except Exception as e:
            logger.error(f"修复上传验证失败: {e}")
            result['errors'] = [str(e)]
        
        # 更新配置文件
        try:
            config_path = project_root / 'config' / 'settings.yml'
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 更新上传可靠性配置
            if 'robustness' not in config:
                config['robustness'] = {}
            if 'upload_reliability' not in config['robustness']:
                config['robustness']['upload_reliability'] = {}
            
            config['robustness']['upload_reliability'].update({
                'verification_delay_seconds': 5,  # 新增：验证延迟
                'max_verification_retries': 3,    # 新增：验证重试次数
                'verification_retry_delay': 2     # 新增：验证重试间隔
            })
            
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            
            result['config_updated'] = True
            
        except Exception as e:
            logger.error(f"更新配置失败: {e}")
        
        return result
    
    def cleanup_empty_files(self) -> Dict[str, Any]:
        """清理空文件"""
        logger.info("🧹 清理空文件...")
        
        result = {
            'empty_files_found': 0,
            'empty_files_removed': 0,
            'errors': []
        }
        
        if not os.path.exists(self.process_dir):
            return result
        
        try:
            video_extensions = ['.mp4', '.mov', '.avi', '.mkv']
            for filename in os.listdir(self.process_dir):
                if any(filename.lower().endswith(ext) for ext in video_extensions):
                    file_path = os.path.join(self.process_dir, filename)
                    
                    if os.path.getsize(file_path) == 0:
                        result['empty_files_found'] += 1
                        try:
                            os.remove(file_path)
                            result['empty_files_removed'] += 1
                            logger.info(f"删除空文件: {filename}")
                        except Exception as e:
                            result['errors'].append(f"删除文件失败 {filename}: {e}")
            
        except Exception as e:
            logger.error(f"清理空文件失败: {e}")
            result['errors'].append(str(e))
        
        logger.info(f"📊 空文件清理: 发现 {result['empty_files_found']} 个, 删除 {result['empty_files_removed']} 个")
        return result
    
    def sync_database_with_files(self) -> Dict[str, Any]:
        """同步数据库与文件系统"""
        logger.info("🔄 同步数据库与文件系统...")
        
        result = {
            'db_records_checked': 0,
            'missing_files_found': 0,
            'status_updated': 0,
            'errors': []
        }
        
        try:
            with database_session() as db:
                # 检查数据库中的记录
                records = db.query(LocalCreative).all()
                result['db_records_checked'] = len(records)
                
                for record in records:
                    # 检查文件是否存在
                    if not os.path.exists(record.file_path):
                        result['missing_files_found'] += 1
                        logger.warning(f"文件不存在: {record.filename}")
                        
                        # 更新状态为missing
                        record.status = 'missing'
                        result['status_updated'] += 1
                
                db.commit()
                
        except Exception as e:
            logger.error(f"数据库同步失败: {e}")
            result['errors'].append(str(e))
        
        logger.info(f"📊 数据库同步: 检查 {result['db_records_checked']} 条记录, 发现 {result['missing_files_found']} 个缺失文件")
        return result
    
    def calculate_file_hash(self, file_path: str) -> str:
        """计算文件哈希"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def trigger_file_ingestion_task(self) -> bool:
        """手动触发文件摄取任务"""
        logger.info("🚀 手动触发文件摄取任务...")
        
        try:
            # 导入并执行文件摄取任务
            from qianchuan_aw.workflows import scheduler
            scheduler.handle_file_ingestion()
            logger.success("✅ 文件摄取任务执行完成")
            return True
        except Exception as e:
            logger.error(f"触发文件摄取任务失败: {e}")
            return False


def main():
    """主函数"""
    logger.info("🔧 启动工作流修复工具...")
    logger.info("=" * 60)
    
    fixer = WorkflowFixer()
    results = fixer.fix_all_issues()
    
    # 输出修复结果
    logger.info("\n📋 修复结果总结:")
    logger.info("=" * 60)
    
    # 文件摄取修复
    ingestion = results['file_ingestion_fix']
    logger.info(f"📥 文件摄取: {ingestion['files_ingested']} 个新文件摄取")
    
    # 上传验证修复
    verification = results['upload_verification_fix']
    if verification.get('verification_delay_added'):
        logger.info("🔍 上传验证: 已添加API同步延迟")
    
    # 空文件清理
    cleanup = results['empty_files_cleanup']
    if cleanup['empty_files_removed'] > 0:
        logger.info(f"🧹 空文件清理: 删除 {cleanup['empty_files_removed']} 个空文件")
    
    # 数据库同步
    sync = results['database_sync']
    logger.info(f"🔄 数据库同步: 检查 {sync['db_records_checked']} 条记录")
    
    logger.info("\n💡 下一步建议:")
    logger.info("1. 重启Celery服务以应用修复")
    logger.info("2. 监控文件摄取任务是否正常运行")
    logger.info("3. 观察上传验证是否成功")
    
    # 手动触发文件摄取
    logger.info("\n🚀 手动触发文件摄取任务...")
    fixer.trigger_file_ingestion_task()


if __name__ == "__main__":
    main()
