#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 永久工具
生命周期: 永久
创建目的: 全局账户选择器 - 左侧栏账户管理组件
依赖关系: Streamlit, 数据库模型, 统一账户选择器
清理条件: 项目核心组件，不可删除
"""

import streamlit as st
import pandas as pd
from typing import List, Dict, Optional, Union, Tuple
from sqlalchemy.orm import joinedload
from loguru import logger

def get_global_selected_account():
    """获取全局选中的账户"""
    return st.session_state.get('global_selected_account', None)

def set_global_selected_account(account):
    """设置全局选中的账户"""
    st.session_state['global_selected_account'] = account

def get_global_selected_accounts():
    """获取全局选中的多个账户（用于特殊场景）"""
    return st.session_state.get('global_selected_accounts', [])

def set_global_selected_accounts(accounts):
    """设置全局选中的多个账户（用于特殊场景）"""
    st.session_state['global_selected_accounts'] = accounts

def get_accounts_with_favorites():
    """获取包含收藏状态的账户列表"""
    try:
        from src.qianchuan_aw.utils.db_utils import database_session
        from src.qianchuan_aw.database.models import AdAccount
        
        with database_session() as db:
            accounts = db.query(AdAccount).options(
                joinedload(AdAccount.principal)
            ).filter(AdAccount.status == 'active').all()
            
            # 确保对象与会话分离，避免会话问题
            db.expunge_all()
            
            return accounts
            
    except Exception as e:
        if 'st' in globals():
            st.error(f"获取账户列表失败: {e}")
        else:
            print(f"获取账户列表失败: {e}")
        return []

def _on_account_change():
    """账户选择变更回调函数（已弃用，保留用于调试）"""
    # 注意：此函数已不再使用，改为同步更新模式
    # 保留此函数用于调试和备用
    try:
        if st.session_state.get("debug_account_selection", False):
            st.sidebar.info("🔧 回调函数被触发（调试模式）")

        # 获取当前选择的显示名称
        selected_display = st.session_state.get("global_account_selector", None)
        if not selected_display:
            return

        # 获取账户选项映射
        account_options = st.session_state.get("_account_options_cache", {})
        if selected_display in account_options:
            selected_account = account_options[selected_display]

            if st.session_state.get("debug_account_selection", False):
                st.sidebar.info(f"🔧 回调函数检测到选择: {selected_account.name}")

    except Exception as e:
        if st.session_state.get("debug_account_selection", False):
            st.sidebar.error(f"❌ 回调函数异常: {e}")

def render_global_account_selector():
    """渲染全局账户选择器 - 基于按钮的全新实现，彻底解决异步问题"""

    # 处理收藏按钮点击状态
    if st.session_state.get('favorites_clicked', False):
        st.session_state.show_favorites_filter = True
        st.session_state.show_authorized_filter = False
        st.session_state.account_search = ""
        st.session_state.favorites_clicked = False

    # 获取所有账户
    all_accounts = get_accounts_with_favorites()
    
    if not all_accounts:
        st.sidebar.warning("⚠️ 没有找到任何账户")
        return None
    
    # 账户筛选功能
    st.sidebar.subheader("🏢 账户选择")
    
    # 搜索框
    search_term = st.sidebar.text_input(
        "搜索账户",
        placeholder="输入账户名称或ID搜索...",
        label_visibility="collapsed",
        key="account_search"
    )
    
    # 筛选选项
    filter_col1, filter_col2 = st.sidebar.columns(2)
    with filter_col1:
        show_favorites_only = st.checkbox("仅收藏", key="show_favorites_filter")
    with filter_col2:
        show_authorized_only = st.checkbox("仅已授权", key="show_authorized_filter")
    
    # 筛选账户
    filtered_accounts = []
    for account in all_accounts:
        # 搜索筛选
        if search_term:
            if (search_term.lower() not in account.name.lower() and 
                search_term not in str(account.account_id_qc)):
                continue
        
        # 收藏筛选
        if show_favorites_only and not getattr(account, 'is_favorite', False):
            continue
            
        # 授权筛选
        if show_authorized_only and not getattr(account, 'douyin_id', None):
            continue
            
        filtered_accounts.append(account)
    
    if not filtered_accounts:
        st.sidebar.info("🔍 没有找到符合条件的账户")
        return get_global_selected_account()
    
    # 显示筛选统计
    total_count = len(all_accounts)
    filtered_count = len(filtered_accounts)
    favorite_count = len([acc for acc in filtered_accounts if getattr(acc, 'is_favorite', False)])
    
    st.sidebar.info(f"📊 显示 {filtered_count}/{total_count} 个账户 (收藏: {favorite_count})")
    
    # 获取当前选中的账户
    current_selected = get_global_selected_account()
    current_selected_id = current_selected.account_id_qc if current_selected else None
    
    # 分页设置
    accounts_per_page = 8  # 每页显示的账户数
    total_pages = (len(filtered_accounts) + accounts_per_page - 1) // accounts_per_page
    
    if 'account_selector_page' not in st.session_state:
        st.session_state.account_selector_page = 0
    
    # 确保页码在有效范围内
    if st.session_state.account_selector_page >= total_pages:
        st.session_state.account_selector_page = 0
    
    # 分页控制
    if total_pages > 1:
        page_col1, page_col2, page_col3 = st.sidebar.columns([1, 2, 1])
        with page_col1:
            if st.button("⬅️", disabled=st.session_state.account_selector_page == 0, key="prev_page"):
                st.session_state.account_selector_page -= 1
        with page_col2:
            st.write(f"第 {st.session_state.account_selector_page + 1}/{total_pages} 页")
        with page_col3:
            if st.button("➡️", disabled=st.session_state.account_selector_page >= total_pages - 1, key="next_page"):
                st.session_state.account_selector_page += 1
    
    # 获取当前页的账户
    start_idx = st.session_state.account_selector_page * accounts_per_page
    end_idx = min(start_idx + accounts_per_page, len(filtered_accounts))
    current_page_accounts = filtered_accounts[start_idx:end_idx]
    
    # 使用按钮网格显示账户
    accounts_changed = False
    
    for i in range(0, len(current_page_accounts), 2):
        col1, col2 = st.sidebar.columns(2)
        
        # 第一个账户
        account1 = current_page_accounts[i]
        is_favorite1 = getattr(account1, 'is_favorite', False)
        has_douyin1 = getattr(account1, 'douyin_id', None) is not None

        # 动态获取当前选中状态，确保按钮颜色实时更新
        current_account = get_global_selected_account()
        is_selected1 = (current_account and
                       current_account.account_id_qc == account1.account_id_qc)

        # 构建按钮文本 - 智能显示名称
        star1 = "⭐" if is_favorite1 else ""
        phone1 = "📱" if has_douyin1 else ""

        # 智能截断账户名称，保留重要信息
        name1 = account1.name
        if len(name1) > 16:
            # 如果有连字符，保留前后重要部分
            if '-' in name1:
                parts = name1.split('-')
                if len(parts) >= 2:
                    name1 = f"{parts[0][:8]}-{parts[-1][:6]}"
                else:
                    name1 = name1[:16] + "..."
            else:
                name1 = name1[:16] + "..."

        button_text1 = f"{star1}{phone1}\n{name1}"

        with col1:
            if st.button(
                button_text1,
                key=f"account_btn_{account1.account_id_qc}",
                type="primary" if is_selected1 else "secondary",
                use_container_width=True,
                help=f"完整名称: {account1.name}\n千川ID: {account1.account_id_qc}\n点击切换到此账户"
            ):
                if not is_selected1:
                    set_global_selected_account(account1)
                    accounts_changed = True
                    logger.info(f"账户选择变化: {current_selected.name if current_selected else 'None'} → {account1.name}")
                    # 强制重新渲染以更新按钮颜色
                    st.rerun()
        
        # 第二个账户（如果存在）
        if i + 1 < len(current_page_accounts):
            account2 = current_page_accounts[i + 1]
            is_favorite2 = getattr(account2, 'is_favorite', False)
            has_douyin2 = getattr(account2, 'douyin_id', None) is not None

            # 动态获取当前选中状态，确保按钮颜色实时更新
            current_account = get_global_selected_account()
            is_selected2 = (current_account and
                           current_account.account_id_qc == account2.account_id_qc)

            star2 = "⭐" if is_favorite2 else ""
            phone2 = "📱" if has_douyin2 else ""

            # 智能截断账户名称，保留重要信息
            name2 = account2.name
            if len(name2) > 16:
                # 如果有连字符，保留前后重要部分
                if '-' in name2:
                    parts = name2.split('-')
                    if len(parts) >= 2:
                        name2 = f"{parts[0][:8]}-{parts[-1][:6]}"
                    else:
                        name2 = name2[:16] + "..."
                else:
                    name2 = name2[:16] + "..."

            button_text2 = f"{star2}{phone2}\n{name2}"

            with col2:
                if st.button(
                    button_text2,
                    key=f"account_btn_{account2.account_id_qc}",
                    type="primary" if is_selected2 else "secondary",
                    use_container_width=True,
                    help=f"完整名称: {account2.name}\n千川ID: {account2.account_id_qc}\n点击切换到此账户"
                ):
                    if not is_selected2:
                        set_global_selected_account(account2)
                        accounts_changed = True
                        logger.info(f"账户选择变化: {current_selected.name if current_selected else 'None'} → {account2.name}")
                        # 强制重新渲染以更新按钮颜色
                        st.rerun()
    
    # 显示当前选中的账户状态（无需st.rerun()）
    current_selected = get_global_selected_account()
    if current_selected:
        is_favorite = getattr(current_selected, 'is_favorite', False)
        has_douyin = getattr(current_selected, 'douyin_id', None) is not None
        
        star = "⭐ " if is_favorite else ""
        phone = " 📱" if has_douyin else ""
        
        st.sidebar.success(f"✅ {star}{current_selected.name}{phone}")
        
        # 显示详细信息
        with st.sidebar.expander("📋 账户详情", expanded=False):
            st.write(f"**账户名称**: {current_selected.name}")
            st.write(f"**千川ID**: {current_selected.account_id_qc}")
            if has_douyin:
                st.write(f"**抖音号**: 已授权")
            if is_favorite:
                st.write(f"**状态**: ⭐ 收藏账户")
    else:
        st.sidebar.warning("⚠️ 请选择一个广告账户")
    
    # 快速操作按钮
    st.sidebar.divider()
    quick_col1, quick_col2 = st.sidebar.columns(2)
    
    with quick_col1:
        if st.button("🔄 刷新", key="refresh_accounts", help="刷新账户列表"):
            # 清除缓存，重新加载账户
            if hasattr(st.session_state, 'accounts_cache'):
                del st.session_state.accounts_cache
    
    with quick_col2:
        if st.button("⭐ 收藏", key="show_favorites", help="快速显示收藏账户"):
            # 使用rerun避免session state修改冲突
            st.session_state.favorites_clicked = True
            st.rerun()
    
    return current_selected
def render_global_favorite_manager():
    """渲染全局收藏管理界面"""
    if not st.session_state.get('show_global_favorite_manager', False):
        return
    
    accounts = get_accounts_with_favorites()
    if not accounts:
        return
    
    st.markdown("### 🛠️ 全局收藏管理")
    
    # 关闭按钮
    col1, col2 = st.columns([6, 1])
    with col2:
        if st.button("❌ 关闭", key="close_global_favorite_manager"):
            st.session_state['show_global_favorite_manager'] = False
            st.rerun()
    
    # 当前收藏状态
    favorite_accounts = [acc for acc in accounts if getattr(acc, 'is_favorite', False)]
    unfavorite_accounts = [acc for acc in accounts if not getattr(acc, 'is_favorite', False)]
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### ⭐ 已收藏账户")
        if favorite_accounts:
            selected_to_unfavorite = st.multiselect(
                "选择要取消收藏的账户",
                options=favorite_accounts,
                format_func=lambda x: f"{x.name} ({x.account_id_qc})",
                key="global_unfavorite_selection"
            )
            
            if st.button("取消收藏", key="global_unfavorite_btn") and selected_to_unfavorite:
                update_favorite_status(selected_to_unfavorite, False)
                st.success(f"已取消收藏 {len(selected_to_unfavorite)} 个账户")
                st.rerun()
        else:
            st.info("暂无收藏账户")
    
    with col2:
        st.markdown("#### 📋 未收藏账户")
        if unfavorite_accounts:
            selected_to_favorite = st.multiselect(
                "选择要添加收藏的账户",
                options=unfavorite_accounts,
                format_func=lambda x: f"{x.name} ({x.account_id_qc})",
                key="global_favorite_selection"
            )
            
            if st.button("添加收藏", key="global_favorite_btn") and selected_to_favorite:
                update_favorite_status(selected_to_favorite, True)
                st.success(f"已添加收藏 {len(selected_to_favorite)} 个账户")
                st.rerun()
        else:
            st.info("所有账户都已收藏")

def update_favorite_status(accounts: List, is_favorite: bool):
    """更新账户收藏状态"""
    try:
        from src.qianchuan_aw.utils.db_utils import database_session
        from src.qianchuan_aw.database.models import AdAccount
        
        with database_session() as db:
            for account in accounts:
                # 重新查询账户对象以确保会话绑定
                db_account = db.query(AdAccount).filter(
                    AdAccount.id == account.id
                ).first()
                if db_account:
                    db_account.is_favorite = is_favorite
            db.commit()
            
        # 清除缓存
        if 'st' in globals():
            st.cache_data.clear()
        
    except Exception as e:
        if 'st' in globals():
            st.error(f"更新收藏状态失败: {e}")
        else:
            print(f"更新收藏状态失败: {e}")

def get_current_account_info():
    """获取当前账户的基本信息（用于页面显示）"""
    account = get_global_selected_account()
    if not account:
        return None
    
    return {
        'name': account.name,
        'account_id': account.account_id_qc,
        'account_type': account.account_type,
        'is_favorite': getattr(account, 'is_favorite', False),
        'aweme_id': getattr(account, 'aweme_id', None),
        'principal_name': account.principal.name if hasattr(account, 'principal') and account.principal else None
    }

def require_account_selection(message: str = "请先在左侧栏选择一个广告账户"):
    """检查是否已选择账户，如果没有则显示提示信息并停止执行"""
    account = get_global_selected_account()
    if not account:
        st.warning(f"⚠️ {message}")
        st.stop()
    return account
