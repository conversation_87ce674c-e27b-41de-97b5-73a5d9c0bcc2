#!/usr/bin/env python3
"""
日志分析验证工具 - 第一阶段紧急修复
通过分析应用日志快速识别已上传素材和创建的计划
"""

import sys
import os
import re
import json
from datetime import datetime
from pathlib import Path
from collections import defaultdict

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text

class LogAnalysisVerifier:
    """日志分析验证器"""
    
    def __init__(self):
        logger.critical("🔍 日志分析验证 - 第一阶段紧急修复")
        logger.critical("=" * 60)
        self.log_dir = Path("logs")
        self.today = datetime.now().strftime("%Y-%m-%d")
        self.results = {
            'uploaded_materials': [],
            'created_campaigns': [],
            'sync_issues': [],
            'verification_summary': {}
        }
    
    def analyze_upload_logs(self):
        """分析视频上传日志"""
        logger.critical("📊 分析视频上传日志")
        logger.critical("=" * 60)
        
        upload_patterns = [
            r'视频上传成功.*?material_id[\":\s]*(\d+)',
            r'上传视频成功.*?(\d+)',
            r'Video upload success.*?material_id[\":\s]*(\d+)',
            r'成功上传.*?material_id[\":\s]*(\d+)',
            r'upload.*?success.*?(\d+)',
        ]
        
        filename_patterns = [
            r'上传.*?([^/\\]+\.mp4)',
            r'视频.*?([^/\\]+\.mp4)',
            r'文件.*?([^/\\]+\.mp4)',
        ]
        
        try:
            log_files = [
                self.log_dir / f"app_{self.today}.log",
                self.log_dir / f"app_2025-07-21.log",  # 检查昨天的日志
            ]
            
            uploaded_materials = []
            
            for log_file in log_files:
                if not log_file.exists():
                    logger.warning(f"日志文件不存在: {log_file}")
                    continue
                
                logger.critical(f"📋 分析日志文件: {log_file}")
                
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 搜索上传成功的记录
                    for pattern in upload_patterns:
                        matches = re.findall(pattern, content, re.IGNORECASE)
                        for match in matches:
                            uploaded_materials.append({
                                'material_id': match,
                                'source': 'upload_log',
                                'log_file': str(log_file)
                            })
                    
                    # 搜索文件名信息
                    filename_matches = re.findall(r'|'.join(filename_patterns), content, re.IGNORECASE)
                    
                    logger.critical(f"  发现上传记录: {len(matches)} 个")
                    logger.critical(f"  发现文件名: {len(filename_matches)} 个")
                    
                except Exception as e:
                    logger.error(f"读取日志文件失败 {log_file}: {e}")
            
            # 去重
            unique_materials = {}
            for material in uploaded_materials:
                material_id = material['material_id']
                if material_id not in unique_materials:
                    unique_materials[material_id] = material
            
            self.results['uploaded_materials'] = list(unique_materials.values())
            
            logger.critical(f"📊 上传日志分析结果:")
            logger.critical(f"  发现上传记录: {len(self.results['uploaded_materials'])} 个")
            
            return self.results['uploaded_materials']
            
        except Exception as e:
            logger.error(f"❌ 分析上传日志失败: {e}")
            return []
    
    def analyze_campaign_logs(self):
        """分析计划创建日志"""
        logger.critical("\n📊 分析计划创建日志")
        logger.critical("=" * 60)
        
        campaign_patterns = [
            r'成功创建计划.*?(\d+)',
            r'Campaign created.*?(\d+)',
            r'广告计划创建成功.*?(\d+)',
            r'创建广告计划.*?成功.*?(\d+)',
            r'campaign_id[\":\s]*(\d+)',
        ]
        
        try:
            log_files = [
                self.log_dir / f"app_{self.today}.log",
                self.log_dir / f"app_2025-07-21.log",
            ]
            
            created_campaigns = []
            
            for log_file in log_files:
                if not log_file.exists():
                    continue
                
                logger.critical(f"📋 分析计划创建日志: {log_file}")
                
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 搜索计划创建记录
                    for pattern in campaign_patterns:
                        matches = re.findall(pattern, content, re.IGNORECASE)
                        for match in matches:
                            created_campaigns.append({
                                'campaign_id': match,
                                'source': 'campaign_log',
                                'log_file': str(log_file)
                            })
                    
                    logger.critical(f"  发现计划创建记录: {len(matches)} 个")
                    
                except Exception as e:
                    logger.error(f"读取日志文件失败 {log_file}: {e}")
            
            # 去重
            unique_campaigns = {}
            for campaign in created_campaigns:
                campaign_id = campaign['campaign_id']
                if campaign_id not in unique_campaigns:
                    unique_campaigns[campaign_id] = campaign
            
            self.results['created_campaigns'] = list(unique_campaigns.values())
            
            logger.critical(f"📊 计划创建日志分析结果:")
            logger.critical(f"  发现计划创建记录: {len(self.results['created_campaigns'])} 个")
            
            return self.results['created_campaigns']
            
        except Exception as e:
            logger.error(f"❌ 分析计划创建日志失败: {e}")
            return []
    
    def cross_verify_with_database(self):
        """与数据库交叉验证"""
        logger.critical("\n🔍 与数据库交叉验证")
        logger.critical("=" * 60)
        
        try:
            with database_session() as db:
                # 获取所有new状态的素材
                new_materials_query = text("""
                    SELECT id, filename, file_hash, status, created_at, updated_at
                    FROM local_creatives
                    WHERE status = 'new'
                    ORDER BY created_at DESC
                """)
                
                new_materials = db.execute(new_materials_query).fetchall()
                
                logger.critical(f"📊 数据库中new状态素材: {len(new_materials)} 个")
                
                # 检查今天创建的计划
                today_campaigns_query = text("""
                    SELECT campaign_id_qc, created_at
                    FROM campaigns
                    WHERE created_at::date = CURRENT_DATE
                    ORDER BY created_at DESC
                """)
                
                today_campaigns = db.execute(today_campaigns_query).fetchall()
                
                logger.critical(f"📊 今天创建的计划: {len(today_campaigns)} 个")
                
                # 识别同步问题
                sync_issues = []
                
                # 检查是否有new状态但实际已有计划的素材
                new_with_campaigns_query = text("""
                    SELECT 
                        lc.id,
                        lc.filename,
                        lc.status,
                        COUNT(c.id) as campaign_count
                    FROM local_creatives lc
                    LEFT JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    LEFT JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                    LEFT JOIN campaigns c ON cpca.campaign_id = c.id
                    WHERE lc.status = 'new'
                    GROUP BY lc.id, lc.filename, lc.status
                    HAVING COUNT(c.id) > 0
                """)
                
                new_with_campaigns = db.execute(new_with_campaigns_query).fetchall()
                
                for material in new_with_campaigns:
                    sync_issues.append({
                        'type': 'status_inconsistency',
                        'material_id': material.id,
                        'filename': material.filename,
                        'current_status': material.status,
                        'campaign_count': material.campaign_count,
                        'issue': 'new状态但已有计划'
                    })
                
                self.results['sync_issues'] = sync_issues
                
                logger.critical(f"🚨 发现同步问题: {len(sync_issues)} 个")
                for issue in sync_issues:
                    logger.critical(f"  - {issue['filename']}: {issue['issue']}")
                
                # 生成验证摘要
                self.results['verification_summary'] = {
                    'total_new_materials': len(new_materials),
                    'today_campaigns': len(today_campaigns),
                    'uploaded_from_logs': len(self.results['uploaded_materials']),
                    'campaigns_from_logs': len(self.results['created_campaigns']),
                    'sync_issues_count': len(sync_issues),
                    'verification_time': datetime.now().isoformat()
                }
                
                return True
                
        except Exception as e:
            logger.error(f"❌ 数据库交叉验证失败: {e}")
            return False
    
    def fix_sync_issues(self):
        """修复同步问题"""
        logger.critical("\n🔧 修复同步问题")
        logger.critical("=" * 60)
        
        if not self.results['sync_issues']:
            logger.critical("✅ 没有发现需要修复的同步问题")
            return 0
        
        try:
            with database_session() as db:
                fixed_count = 0
                
                for issue in self.results['sync_issues']:
                    if issue['type'] == 'status_inconsistency':
                        # 将有计划但状态为new的素材改为testing_pending_review
                        fix_query = text("""
                            UPDATE local_creatives
                            SET status = 'testing_pending_review',
                                updated_at = NOW()
                            WHERE id = :material_id
                        """)
                        
                        db.execute(fix_query, {'material_id': issue['material_id']})
                        fixed_count += 1
                        
                        logger.critical(f"  ✅ 修复 {issue['filename']}: new → testing_pending_review")
                
                db.commit()
                
                logger.critical(f"🎉 修复完成: {fixed_count} 个同步问题")
                return fixed_count
                
        except Exception as e:
            logger.error(f"❌ 修复同步问题失败: {e}")
            return 0
    
    def generate_verification_report(self):
        """生成验证报告"""
        logger.critical("\n📋 生成验证报告")
        logger.critical("=" * 60)
        
        # 保存详细结果
        report_file = f"ai_reports/log_analysis_verification_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        logger.critical(f"✅ 详细报告保存: {report_file}")
        
        # 显示摘要
        summary = self.results['verification_summary']
        
        logger.critical(f"\n📊 验证摘要:")
        logger.critical(f"  数据库new状态素材: {summary['total_new_materials']}")
        logger.critical(f"  今天创建的计划: {summary['today_campaigns']}")
        logger.critical(f"  日志中的上传记录: {summary['uploaded_from_logs']}")
        logger.critical(f"  日志中的计划记录: {summary['campaigns_from_logs']}")
        logger.critical(f"  发现的同步问题: {summary['sync_issues_count']}")
        
        logger.critical(f"\n💡 关键结论:")
        if summary['sync_issues_count'] > 0:
            logger.critical(f"  🚨 发现 {summary['sync_issues_count']} 个数据同步问题")
            logger.critical(f"  🔧 已自动修复状态不一致问题")
        else:
            logger.critical(f"  ✅ 数据同步状态良好")
        
        if summary['today_campaigns'] > 0:
            logger.critical(f"  ⚠️ 今天有 {summary['today_campaigns']} 个计划被创建")
            logger.critical(f"  📊 需要进一步验证这些计划的合规性")
        
        # 计算真正的new状态素材数量
        true_new_count = summary['total_new_materials'] - summary['sync_issues_count']
        logger.critical(f"  🎯 真正的new状态素材: {true_new_count} 个")
        
        return report_file

def main():
    """主验证函数"""
    try:
        verifier = LogAnalysisVerifier()
        
        # 1. 分析上传日志
        uploaded_materials = verifier.analyze_upload_logs()
        
        # 2. 分析计划创建日志
        created_campaigns = verifier.analyze_campaign_logs()
        
        # 3. 与数据库交叉验证
        cross_verify_success = verifier.cross_verify_with_database()
        
        # 4. 修复同步问题
        fixed_count = verifier.fix_sync_issues()
        
        # 5. 生成验证报告
        report_file = verifier.generate_verification_report()
        
        logger.critical(f"\n🎉 日志分析验证完成!")
        logger.critical(f"上传记录: {len(uploaded_materials)}")
        logger.critical(f"计划记录: {len(created_campaigns)}")
        logger.critical(f"修复问题: {fixed_count}")
        logger.critical(f"报告文件: {report_file}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 日志分析验证失败: {e}")
        return False

if __name__ == "__main__":
    main()
