#!/usr/bin/env python3
"""
增强版视频验证器监控仪表板
实时显示验证器效果和系统状态
"""

import sys
import os
import time
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.db_utils import database_session
from src.qianchuan_aw.utils.logger import logger
from sqlalchemy import text

def display_dashboard():
    """显示监控仪表板"""
    while True:
        os.system('cls' if os.name == 'nt' else 'clear')
        
        print("🎯 千川增强版视频验证器监控仪表板")
        print("=" * 60)
        print(f"更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        try:
            with database_session() as db:
                # 最近24小时统计
                stats_query = text("""
                    SELECT 
                        COUNT(*) as total,
                        COUNT(CASE WHEN lc.status = 'upload_failed' THEN 1 END) as failed,
                        COUNT(CASE WHEN lc.status IN ('approved', 'rejected') THEN 1 END) as completed,
                        COUNT(CASE WHEN lc.created_at >= NOW() - INTERVAL '1 hour' THEN 1 END) as recent_hour
                    FROM local_creatives lc
                    JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
                    WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                        AND lc.created_at >= NOW() - INTERVAL '24 hours'
                """)
                
                result = db.execute(stats_query).fetchone()
                
                if result.total > 0:
                    failure_rate = (result.failed / result.total) * 100
                    completion_rate = (result.completed / result.total) * 100
                    
                    print("📊 24小时统计:")
                    print(f"  总处理: {result.total}")
                    print(f"  失败: {result.failed} ({failure_rate:.1f}%)")
                    print(f"  完成: {result.completed} ({completion_rate:.1f}%)")
                    print(f"  最近1小时: {result.recent_hour}")
                    print()
                    
                    # 状态指示器
                    if failure_rate < 1:
                        status = "🟢 优秀"
                    elif failure_rate < 5:
                        status = "🟡 良好"
                    else:
                        status = "🔴 需要改进"
                    
                    print(f"系统状态: {status}")
                    print()
                
                # 隔离统计
                quarantine_dir = "quarantine/invalid_videos"
                if os.path.exists(quarantine_dir):
                    quarantine_count = len([f for f in os.listdir(quarantine_dir) if f.endswith('.mp4')])
                    print(f"📁 隔离文件: {quarantine_count}个")
                
        except Exception as e:
            print(f"❌ 数据获取失败: {e}")
        
        print()
        print("按 Ctrl+C 退出监控")
        
        try:
            time.sleep(30)  # 30秒更新一次
        except KeyboardInterrupt:
            print("\n监控已停止")
            break

if __name__ == "__main__":
    display_dashboard()
