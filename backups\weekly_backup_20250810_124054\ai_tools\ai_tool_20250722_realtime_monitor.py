#!/usr/bin/env python3
"""
千川系统实时监控脚本
监控修复效果和系统状态
"""

import sys
import os
import time
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text

def monitor_system_status():
    """监控系统状态"""
    logger.info("🔍 千川系统实时监控")
    logger.info("=" * 60)
    
    try:
        with database_session() as db:
            # 1. 检查素材状态分布
            status_query = text("""
                SELECT status, COUNT(*) as count
                FROM local_creatives 
                WHERE status IN ('approved', 'uploaded_pending_plan', 'creating_plan')
                GROUP BY status 
                ORDER BY count DESC
            """)
            
            results = db.execute(status_query).fetchall()
            
            logger.info("📊 关键素材状态:")
            for row in results:
                logger.info(f"  {row.status}: {row.count} 个")
            
            # 2. 检查最近的计划创建
            recent_campaigns_query = text("""
                SELECT COUNT(*) as count
                FROM campaigns
                WHERE created_at > NOW() - INTERVAL '1 hour'
            """)
            
            recent_count = db.execute(recent_campaigns_query).fetchone()
            logger.info(f"📈 最近1小时创建的计划: {recent_count.count} 个")
            
            # 3. 检查账户素材分布
            account_query = text("""
                SELECT aa.name, aa.account_type,
                       COUNT(lc.id) as material_count
                FROM ad_accounts aa
                LEFT JOIN platform_creatives pc ON aa.id = pc.account_id
                LEFT JOIN local_creatives lc ON pc.local_creative_id = lc.id
                WHERE aa.status = 'active'
                    AND lc.status IN ('approved', 'uploaded_pending_plan')
                GROUP BY aa.name, aa.account_type
                HAVING COUNT(lc.id) > 0
                ORDER BY material_count DESC
                LIMIT 5
            """)
            
            account_results = db.execute(account_query).fetchall()
            
            if account_results:
                logger.info("📋 账户素材分布 (Top 5):")
                for row in account_results:
                    logger.info(f"  {row.name} ({row.account_type}): {row.material_count} 个素材")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 监控失败: {e}")
        return False

def check_log_patterns():
    """检查日志模式"""
    logger.info("\n📋 检查最近日志模式")
    logger.info("=" * 60)
    
    log_file = f"logs/app_{datetime.now().strftime('%Y-%m-%d')}.log"
    
    if not os.path.exists(log_file):
        logger.warning(f"日志文件不存在: {log_file}")
        return
    
    # 统计最近10分钟的关键模式
    patterns = {
        'creating_plan_skip': 0,
        'plan_created': 0,
        'workflow_end': 0
    }
    
    cutoff_time = datetime.now().timestamp() - 600  # 10分钟前
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            for line in f:
                # 简单的时间戳检查
                if '状态已变更为 creating_plan，跳过' in line:
                    patterns['creating_plan_skip'] += 1
                elif '成功创建计划' in line or 'Campaign created' in line:
                    patterns['plan_created'] += 1
                elif '工作流结束' in line or 'Task End' in line:
                    patterns['workflow_end'] += 1
    
        logger.info("📊 最近日志模式:")
        for pattern, count in patterns.items():
            logger.info(f"  {pattern}: {count} 次")
        
        # 评估修复效果
        if patterns['creating_plan_skip'] < 100:
            logger.info("✅ 跳过次数已显著减少，修复生效")
        else:
            logger.warning(f"⚠️ 跳过次数仍然较高: {patterns['creating_plan_skip']} 次")
        
        if patterns['plan_created'] > 0:
            logger.info(f"🎉 发现新创建的计划: {patterns['plan_created']} 个")
        
    except Exception as e:
        logger.error(f"❌ 检查日志模式失败: {e}")

def main():
    """主监控函数"""
    logger.info(f"🚀 开始实时监控 - {datetime.now()}")
    
    # 监控系统状态
    monitor_system_status()
    
    # 检查日志模式
    check_log_patterns()
    
    logger.info("\n💡 监控建议:")
    logger.info("  1. 如果跳过次数仍然很高，检查Celery服务是否重启")
    logger.info("  2. 如果没有新计划创建，检查账户素材分布")
    logger.info("  3. 定期运行此监控脚本观察趋势")

if __name__ == "__main__":
    main()
