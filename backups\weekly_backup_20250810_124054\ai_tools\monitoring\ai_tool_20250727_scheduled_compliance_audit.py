#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 定期合规性审计系统
清理条件: 集成到主系统后可删除

千川自动化定期合规性审计系统
========================

建立定期（每日/每周）的合规性审计流程，确保系统始终遵守三项铁律：
1. 素材唯一性测试铁律
2. 账户隔离铁律  
3. 数据一致性铁律
"""

import sys
import os
import schedule
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import LocalCreative, Campaign, PlatformCreative, AdAccount, Principal
from sqlalchemy import text


class ComplianceAuditResult:
    """合规性审计结果"""
    
    def __init__(self, audit_type: str, status: str, details: Dict[str, Any]):
        self.audit_type = audit_type
        self.status = status  # PASS, FAIL, WARNING
        self.details = details
        self.timestamp = datetime.now()
        self.recommendations = []


class ScheduledComplianceAuditor:
    """定期合规性审计器"""
    
    def __init__(self):
        self.audit_history = []
        self.audit_config = {
            'daily_audit_time': '02:00',  # 凌晨2点执行日常审计
            'weekly_audit_day': 'monday',  # 每周一执行深度审计
            'monthly_audit_day': 1,  # 每月1号执行全面审计
            'auto_fix_enabled': False,  # 是否启用自动修复
            'alert_threshold': {
                'critical_violations': 1,
                'total_violations': 10,
                'data_inconsistency': 5
            }
        }
    
    def audit_material_uniqueness(self) -> ComplianceAuditResult:
        """审计素材唯一性测试铁律"""
        logger.info("🔍 审计素材唯一性测试铁律...")
        
        try:
            with database_session() as db:
                # 检查素材重复使用违规
                violation_query = text("""
                    SELECT 
                        lc.id as material_id,
                        lc.filename,
                        lc.file_hash,
                        lc.status,
                        COUNT(DISTINCT c.id) as plan_count,
                        STRING_AGG(DISTINCT c.campaign_id_qc, ', ') as plan_ids,
                        STRING_AGG(DISTINCT aa.name, ', ') as account_names,
                        MIN(c.created_at) as first_violation,
                        MAX(c.created_at) as last_violation
                    FROM local_creatives lc
                    JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                    JOIN campaigns c ON cpca.campaign_id = c.id
                    JOIN ad_accounts aa ON c.account_id = aa.id
                    WHERE aa.account_type = 'TEST'
                    GROUP BY lc.id, lc.filename, lc.file_hash, lc.status
                    HAVING COUNT(DISTINCT c.id) > 1
                    ORDER BY plan_count DESC
                """)
                
                result = db.execute(violation_query)
                violations = result.fetchall()
                
                # 统计信息
                total_violations = len(violations)
                total_duplicate_plans = sum(v.plan_count - 1 for v in violations)  # 减去保留的计划
                critical_violations = [v for v in violations if v.plan_count >= 5]
                
                # 分析违规严重程度
                severity_analysis = {
                    'critical': len([v for v in violations if v.plan_count >= 5]),
                    'high': len([v for v in violations if 3 <= v.plan_count < 5]),
                    'medium': len([v for v in violations if v.plan_count == 2]),
                    'total': total_violations
                }
                
                # 确定审计状态
                if total_violations == 0:
                    status = 'PASS'
                elif len(critical_violations) > 0:
                    status = 'FAIL'
                else:
                    status = 'WARNING'
                
                audit_result = ComplianceAuditResult(
                    audit_type='素材唯一性测试铁律',
                    status=status,
                    details={
                        'total_violations': total_violations,
                        'total_duplicate_plans': total_duplicate_plans,
                        'severity_analysis': severity_analysis,
                        'violations': [
                            {
                                'material_id': v.material_id,
                                'filename': v.filename,
                                'file_hash': v.file_hash[:16] + '...',
                                'status': v.status,
                                'plan_count': v.plan_count,
                                'plan_ids': v.plan_ids.split(', ') if v.plan_ids else [],
                                'account_names': v.account_names.split(', ') if v.account_names else [],
                                'first_violation': v.first_violation,
                                'last_violation': v.last_violation,
                                'violation_duration_hours': (v.last_violation - v.first_violation).total_seconds() / 3600 if v.last_violation and v.first_violation else 0
                            }
                            for v in violations
                        ]
                    }
                )
                
                # 生成建议
                if total_violations > 0:
                    audit_result.recommendations.extend([
                        f"立即修复 {total_violations} 个素材重复使用违规",
                        f"删除 {total_duplicate_plans} 个重复测试计划",
                        "加强计划创建前的重复检查机制",
                        "实施数据库层面的唯一性约束"
                    ])
                    
                    if len(critical_violations) > 0:
                        audit_result.recommendations.insert(0, f"🚨 紧急处理 {len(critical_violations)} 个严重违规（≥5个重复计划）")
                
                return audit_result
                
        except Exception as e:
            logger.error(f"素材唯一性审计失败: {e}")
            return ComplianceAuditResult(
                audit_type='素材唯一性测试铁律',
                status='FAIL',
                details={'error': str(e)}
            )
    
    def audit_account_isolation(self) -> ComplianceAuditResult:
        """审计账户隔离铁律"""
        logger.info("🔍 审计账户隔离铁律...")
        
        try:
            with database_session() as db:
                # 检查跨账户素材使用
                cross_account_query = text("""
                    SELECT 
                        lc.id as material_id,
                        lc.filename,
                        COUNT(DISTINCT aa.id) as account_count,
                        STRING_AGG(DISTINCT aa.name, ', ') as account_names,
                        STRING_AGG(DISTINCT p.name, ', ') as principal_names
                    FROM local_creatives lc
                    JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                    JOIN campaigns c ON cpca.campaign_id = c.id
                    JOIN ad_accounts aa ON c.account_id = aa.id
                    JOIN principals p ON aa.principal_id = p.id
                    GROUP BY lc.id, lc.filename
                    HAVING COUNT(DISTINCT aa.id) > 1
                    ORDER BY account_count DESC
                """)
                
                result = db.execute(cross_account_query)
                cross_account_violations = result.fetchall()
                
                # 检查账户类型混用
                account_type_query = text("""
                    SELECT 
                        lc.id as material_id,
                        lc.filename,
                        COUNT(DISTINCT aa.account_type) as type_count,
                        STRING_AGG(DISTINCT aa.account_type, ', ') as account_types
                    FROM local_creatives lc
                    JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                    JOIN campaigns c ON cpca.campaign_id = c.id
                    JOIN ad_accounts aa ON c.account_id = aa.id
                    GROUP BY lc.id, lc.filename
                    HAVING COUNT(DISTINCT aa.account_type) > 1
                """)
                
                result = db.execute(account_type_query)
                type_mix_violations = result.fetchall()
                
                total_violations = len(cross_account_violations) + len(type_mix_violations)
                
                status = 'PASS' if total_violations == 0 else 'WARNING'
                
                return ComplianceAuditResult(
                    audit_type='账户隔离铁律',
                    status=status,
                    details={
                        'cross_account_violations': len(cross_account_violations),
                        'type_mix_violations': len(type_mix_violations),
                        'total_violations': total_violations,
                        'violations': {
                            'cross_account': [
                                {
                                    'material_id': v.material_id,
                                    'filename': v.filename,
                                    'account_count': v.account_count,
                                    'account_names': v.account_names.split(', ') if v.account_names else [],
                                    'principal_names': v.principal_names.split(', ') if v.principal_names else []
                                }
                                for v in cross_account_violations
                            ],
                            'type_mix': [
                                {
                                    'material_id': v.material_id,
                                    'filename': v.filename,
                                    'type_count': v.type_count,
                                    'account_types': v.account_types.split(', ') if v.account_types else []
                                }
                                for v in type_mix_violations
                            ]
                        }
                    }
                )
                
        except Exception as e:
            logger.error(f"账户隔离审计失败: {e}")
            return ComplianceAuditResult(
                audit_type='账户隔离铁律',
                status='FAIL',
                details={'error': str(e)}
            )
    
    def audit_data_consistency(self) -> ComplianceAuditResult:
        """审计数据一致性铁律"""
        logger.info("🔍 审计数据一致性铁律...")
        
        try:
            with database_session() as db:
                inconsistencies = []
                
                # 检查孤立的平台素材
                orphan_platform_query = text("""
                    SELECT COUNT(*) as count
                    FROM platform_creatives pc
                    LEFT JOIN local_creatives lc ON pc.local_creative_id = lc.id
                    WHERE lc.id IS NULL
                """)
                
                result = db.execute(orphan_platform_query)
                orphan_platform_count = result.fetchone().count
                
                if orphan_platform_count > 0:
                    inconsistencies.append({
                        'type': '孤立平台素材',
                        'count': orphan_platform_count,
                        'description': '存在没有对应本地素材的平台素材记录'
                    })
                
                # 检查孤立的计划关联
                orphan_association_query = text("""
                    SELECT COUNT(*) as count
                    FROM campaign_platform_creative_association cpca
                    LEFT JOIN campaigns c ON cpca.campaign_id = c.id
                    LEFT JOIN platform_creatives pc ON cpca.platform_creative_id = pc.id
                    WHERE c.id IS NULL OR pc.id IS NULL
                """)
                
                result = db.execute(orphan_association_query)
                orphan_association_count = result.fetchone().count
                
                if orphan_association_count > 0:
                    inconsistencies.append({
                        'type': '孤立关联记录',
                        'count': orphan_association_count,
                        'description': '存在无效的计划-素材关联记录'
                    })
                
                # 检查状态不一致
                status_inconsistency_query = text("""
                    SELECT 
                        lc.status as material_status,
                        COUNT(*) as count
                    FROM local_creatives lc
                    JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                    JOIN campaigns c ON cpca.campaign_id = c.id
                    WHERE lc.status NOT IN ('testing_pending_review', 'approved', 'rejected', 'already_tested')
                    GROUP BY lc.status
                """)
                
                result = db.execute(status_inconsistency_query)
                status_inconsistencies = result.fetchall()
                
                for status_inc in status_inconsistencies:
                    inconsistencies.append({
                        'type': '状态不一致',
                        'count': status_inc.count,
                        'description': f'存在 {status_inc.count} 个状态为 "{status_inc.material_status}" 的素材有测试计划'
                    })
                
                total_inconsistencies = sum(inc['count'] for inc in inconsistencies)
                
                status = 'PASS' if total_inconsistencies == 0 else 'WARNING'
                if total_inconsistencies >= self.audit_config['alert_threshold']['data_inconsistency']:
                    status = 'FAIL'
                
                return ComplianceAuditResult(
                    audit_type='数据一致性铁律',
                    status=status,
                    details={
                        'total_inconsistencies': total_inconsistencies,
                        'inconsistencies': inconsistencies
                    }
                )
                
        except Exception as e:
            logger.error(f"数据一致性审计失败: {e}")
            return ComplianceAuditResult(
                audit_type='数据一致性铁律',
                status='FAIL',
                details={'error': str(e)}
            )
    
    def perform_full_audit(self) -> Dict[str, Any]:
        """执行完整审计"""
        logger.info("🔍 开始执行完整合规性审计...")
        
        audit_results = []
        
        # 执行各项审计
        audit_results.append(self.audit_material_uniqueness())
        audit_results.append(self.audit_account_isolation())
        audit_results.append(self.audit_data_consistency())
        
        # 汇总结果
        total_audits = len(audit_results)
        passed_audits = len([r for r in audit_results if r.status == 'PASS'])
        failed_audits = len([r for r in audit_results if r.status == 'FAIL'])
        warning_audits = len([r for r in audit_results if r.status == 'WARNING'])
        
        overall_status = 'PASS'
        if failed_audits > 0:
            overall_status = 'FAIL'
        elif warning_audits > 0:
            overall_status = 'WARNING'
        
        audit_summary = {
            'timestamp': datetime.now(),
            'overall_status': overall_status,
            'total_audits': total_audits,
            'passed_audits': passed_audits,
            'failed_audits': failed_audits,
            'warning_audits': warning_audits,
            'audit_results': audit_results,
            'recommendations': []
        }
        
        # 收集所有建议
        for result in audit_results:
            audit_summary['recommendations'].extend(result.recommendations)
        
        # 保存审计历史
        self.audit_history.append(audit_summary)
        
        # 保持历史记录在合理范围内
        if len(self.audit_history) > 100:
            self.audit_history = self.audit_history[-50:]
        
        logger.info(f"✅ 完整合规性审计完成，状态: {overall_status}")
        
        return audit_summary
    
    def generate_audit_report(self, audit_summary: Dict[str, Any]) -> str:
        """生成审计报告"""
        report_time = audit_summary['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
        
        report = f"""
千川自动化定期合规性审计报告
==========================
审计时间: {report_time}
审计状态: {audit_summary['overall_status']}

📊 审计概览:
- 总审计项目: {audit_summary['total_audits']} 项
- 通过审计: {audit_summary['passed_audits']} 项
- 警告审计: {audit_summary['warning_audits']} 项  
- 失败审计: {audit_summary['failed_audits']} 项

📋 详细审计结果:
"""
        
        for result in audit_summary['audit_results']:
            status_icon = {'PASS': '✅', 'WARNING': '⚠️', 'FAIL': '❌'}[result.status]
            report += f"""
{status_icon} {result.audit_type}
   状态: {result.status}
   时间: {result.timestamp.strftime('%H:%M:%S')}
"""
            
            # 添加详细信息
            if result.audit_type == '素材唯一性测试铁律':
                details = result.details
                if 'total_violations' in details:
                    report += f"   违规素材: {details['total_violations']} 个\n"
                    report += f"   重复计划: {details['total_duplicate_plans']} 个\n"
                    
                    if details['severity_analysis']['critical'] > 0:
                        report += f"   🚨 严重违规: {details['severity_analysis']['critical']} 个\n"
            
            elif result.audit_type == '账户隔离铁律':
                details = result.details
                if 'total_violations' in details:
                    report += f"   跨账户违规: {details['cross_account_violations']} 个\n"
                    report += f"   类型混用违规: {details['type_mix_violations']} 个\n"
            
            elif result.audit_type == '数据一致性铁律':
                details = result.details
                if 'total_inconsistencies' in details:
                    report += f"   数据不一致: {details['total_inconsistencies']} 个\n"
        
        # 添加建议
        if audit_summary['recommendations']:
            report += f"""
🎯 改进建议:
"""
            for i, rec in enumerate(audit_summary['recommendations'], 1):
                report += f"{i}. {rec}\n"
        
        report += f"""
⚙️ 审计配置:
- 日常审计时间: {self.audit_config['daily_audit_time']}
- 周度审计日: {self.audit_config['weekly_audit_day']}
- 自动修复: {'启用' if self.audit_config['auto_fix_enabled'] else '禁用'}

📈 历史趋势:
- 历史审计次数: {len(self.audit_history)} 次
- 本次审计状态: {audit_summary['overall_status']}

审计报告生成完成。
"""
        
        return report
    
    def schedule_audits(self):
        """安排定期审计"""
        # 每日审计
        schedule.every().day.at(self.audit_config['daily_audit_time']).do(self.daily_audit)
        
        # 每周审计
        getattr(schedule.every(), self.audit_config['weekly_audit_day']).at("01:00").do(self.weekly_audit)
        
        # 每月审计
        schedule.every().month.do(self.monthly_audit)
        
        logger.info("📅 定期合规性审计已安排")
    
    def daily_audit(self):
        """日常审计"""
        logger.info("🌅 执行日常合规性审计...")
        audit_summary = self.perform_full_audit()
        
        # 保存日常审计报告
        report = self.generate_audit_report(audit_summary)
        self.save_audit_report(report, 'daily')
        
        # 如果有严重问题，发送警报
        if audit_summary['overall_status'] == 'FAIL':
            logger.error("🚨 日常审计发现严重合规问题！")
    
    def weekly_audit(self):
        """周度深度审计"""
        logger.info("📊 执行周度深度合规性审计...")
        # 执行更详细的审计
        audit_summary = self.perform_full_audit()
        
        report = self.generate_audit_report(audit_summary)
        self.save_audit_report(report, 'weekly')
    
    def monthly_audit(self):
        """月度全面审计"""
        logger.info("📈 执行月度全面合规性审计...")
        audit_summary = self.perform_full_audit()
        
        report = self.generate_audit_report(audit_summary)
        self.save_audit_report(report, 'monthly')
    
    def save_audit_report(self, report: str, audit_type: str):
        """保存审计报告"""
        os.makedirs('ai_reports/compliance_audits', exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"ai_reports/compliance_audits/{audit_type}_audit_{timestamp}.md"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"📄 {audit_type}审计报告已保存: {filename}")


def main():
    """主函数"""
    print("📅 千川自动化定期合规性审计系统")
    print("=" * 60)
    
    auditor = ScheduledComplianceAuditor()
    
    try:
        # 执行一次完整审计
        print("\n🔍 执行完整合规性审计...")
        audit_summary = auditor.perform_full_audit()
        
        # 生成报告
        report = auditor.generate_audit_report(audit_summary)
        
        # 保存报告
        auditor.save_audit_report(report, 'manual')
        
        print(f"📊 审计结果: {audit_summary['overall_status']}")
        print(f"📊 通过项目: {audit_summary['passed_audits']}/{audit_summary['total_audits']}")
        
        if audit_summary['recommendations']:
            print(f"📊 改进建议: {len(audit_summary['recommendations'])} 条")
        
        print("✅ 定期合规性审计系统准备完成")
        
        # 演示安排定期审计（实际使用时取消注释）
        # auditor.schedule_audits()
        # 
        # # 运行调度器
        # while True:
        #     schedule.run_pending()
        #     time.sleep(60)
        
        return 0
        
    except Exception as e:
        print(f"❌ 审计系统测试失败: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
