#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 紧急修复后的验证工具
清理条件: 成为紧急修复验证工具，长期保留
"""

import os
import sys
import time
from datetime import datetime, timezone, timedelta

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.database import SessionLocal
from qianchuan_aw.database.models import LocalCreative


class EmergencyFixVerification:
    """紧急修复验证器"""
    
    def __init__(self):
        self.verification_results = {}
    
    def run_emergency_verification(self):
        """运行紧急修复验证"""
        logger.info("🚨 紧急修复验证")
        logger.info("="*80)
        
        # 1. 检查Celery进程状态
        celery_status = self._check_celery_processes()
        
        # 2. 检查数据库状态
        database_status = self._check_database_status()
        
        # 3. 检查原子状态管理器修复
        atomic_manager_status = self._check_atomic_manager_fix()
        
        # 4. 检查防重复处理机制
        duplicate_prevention_status = self._check_duplicate_prevention()
        
        # 5. 生成验证报告
        self._generate_verification_report({
            'celery_status': celery_status,
            'database_status': database_status,
            'atomic_manager_status': atomic_manager_status,
            'duplicate_prevention_status': duplicate_prevention_status
        })
    
    def _check_celery_processes(self):
        """检查Celery进程状态"""
        logger.info("🔍 检查Celery进程状态...")
        
        try:
            import psutil
            
            celery_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = ' '.join(proc.info.get('cmdline', []))
                    if 'celery' in cmdline.lower() and ('worker' in cmdline or 'beat' in cmdline):
                        celery_processes.append({
                            'pid': proc.info['pid'],
                            'type': 'worker' if 'worker' in cmdline else 'beat',
                            'cmdline': cmdline
                        })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            if celery_processes:
                logger.warning(f"⚠️ 发现 {len(celery_processes)} 个运行中的Celery进程:")
                for proc in celery_processes:
                    logger.warning(f"   PID {proc['pid']}: {proc['type']}")
                return {'running_processes': len(celery_processes), 'processes': celery_processes, 'clean': False}
            else:
                logger.success("✅ 无运行中的Celery进程")
                return {'running_processes': 0, 'processes': [], 'clean': True}
                
        except ImportError:
            logger.warning("⚠️ psutil不可用，无法检查进程")
            return {'running_processes': -1, 'clean': None}
    
    def _check_database_status(self):
        """检查数据库状态"""
        logger.info("🗄️ 检查数据库状态...")
        
        with SessionLocal() as db:
            # 检查各状态分布
            status_counts = {}
            
            all_statuses = [
                'pending_upload', 'processing', 'uploaded_pending_plan',
                'testing_pending_review', 'approved', 'rejected', 'upload_failed'
            ]
            
            for status in all_statuses:
                count = db.query(LocalCreative).filter(
                    LocalCreative.status == status
                ).count()
                if count > 0:
                    status_counts[status] = count
            
            # 重点检查processing状态
            processing_count = status_counts.get('processing', 0)
            pending_upload_count = status_counts.get('pending_upload', 0)
            
            logger.info("📊 当前状态分布:")
            for status, count in status_counts.items():
                status_icon = {
                    'pending_upload': '📤',
                    'processing': '⏳',
                    'uploaded_pending_plan': '📋',
                    'approved': '✅',
                    'rejected': '❌'
                }.get(status, '📄')
                logger.info(f"   {status_icon} {status}: {count}")
            
            # 状态健康评估
            if processing_count == 0:
                logger.success("✅ 无processing状态积压")
                status_health = 'EXCELLENT'
            elif processing_count < 10:
                logger.warning(f"⚠️ 少量processing状态: {processing_count}个")
                status_health = 'GOOD'
            else:
                logger.error(f"❌ processing状态积压严重: {processing_count}个")
                status_health = 'CRITICAL'
            
            return {
                'status_counts': status_counts,
                'processing_count': processing_count,
                'pending_upload_count': pending_upload_count,
                'status_health': status_health
            }
    
    def _check_atomic_manager_fix(self):
        """检查原子状态管理器修复"""
        logger.info("🔧 检查原子状态管理器修复...")
        
        try:
            from qianchuan_aw.utils.atomic_state_manager import AtomicStateManager
            
            # 检查修复后的方法
            import inspect
            source = inspect.getsource(AtomicStateManager.safe_dispatch_upload_task)
            
            # 检查是否包含修复的关键逻辑
            fixes_present = [
                'with_for_update(nowait=True)' in source,  # 防竞态条件
                'status != \'pending_upload\'' in source,   # 状态验证
                'return False' in source,                   # 返回值处理
                'return True' in source                     # 成功返回
            ]
            
            fix_score = sum(fixes_present) / len(fixes_present) * 100
            
            if fix_score >= 75:
                logger.success(f"✅ 原子状态管理器修复验证通过 ({fix_score:.0f}%)")
                return {'fix_verified': True, 'fix_score': fix_score}
            else:
                logger.error(f"❌ 原子状态管理器修复不完整 ({fix_score:.0f}%)")
                return {'fix_verified': False, 'fix_score': fix_score}
                
        except Exception as e:
            logger.error(f"❌ 原子状态管理器验证失败: {e}")
            return {'fix_verified': False, 'error': str(e)}
    
    def _check_duplicate_prevention(self):
        """检查防重复处理机制"""
        logger.info("🔒 检查防重复处理机制...")
        
        try:
            # 检查scheduler.py是否包含锁机制
            scheduler_file = os.path.join(project_root, 'src/qianchuan_aw/workflows/scheduler.py')
            
            with open(scheduler_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查关键修复
            lock_features = [
                'fcntl.flock' in content,                    # 文件锁
                'LOCK_EX | LOCK_NB' in content,             # 非阻塞锁
                'qianchuan_group_dispatch.lock' in content,  # 锁文件
                'pending_grouping' in content and 'pending_upload' in content  # 状态转换
            ]
            
            prevention_score = sum(lock_features) / len(lock_features) * 100
            
            if prevention_score >= 75:
                logger.success(f"✅ 防重复处理机制验证通过 ({prevention_score:.0f}%)")
                return {'prevention_verified': True, 'prevention_score': prevention_score}
            else:
                logger.error(f"❌ 防重复处理机制不完整 ({prevention_score:.0f}%)")
                return {'prevention_verified': False, 'prevention_score': prevention_score}
                
        except Exception as e:
            logger.error(f"❌ 防重复处理机制验证失败: {e}")
            return {'prevention_verified': False, 'error': str(e)}
    
    def _generate_verification_report(self, results):
        """生成验证报告"""
        logger.info("\n📋 紧急修复验证报告")
        logger.info("="*80)
        
        # Celery进程状态
        celery_status = results['celery_status']
        if celery_status['clean']:
            logger.success("✅ Celery进程状态: 已清理")
        else:
            logger.error(f"❌ Celery进程状态: {celery_status['running_processes']} 个进程仍在运行")
        
        # 数据库状态
        db_status = results['database_status']
        processing_count = db_status['processing_count']
        
        if db_status['status_health'] == 'EXCELLENT':
            logger.success("✅ 数据库状态: 优秀")
        elif db_status['status_health'] == 'GOOD':
            logger.warning("⚠️ 数据库状态: 良好")
        else:
            logger.error(f"❌ 数据库状态: 严重问题 ({processing_count} 个processing积压)")
        
        # 原子状态管理器
        atomic_status = results['atomic_manager_status']
        if atomic_status['fix_verified']:
            logger.success("✅ 原子状态管理器: 修复验证通过")
        else:
            logger.error("❌ 原子状态管理器: 修复验证失败")
        
        # 防重复处理
        prevention_status = results['duplicate_prevention_status']
        if prevention_status['prevention_verified']:
            logger.success("✅ 防重复处理机制: 验证通过")
        else:
            logger.error("❌ 防重复处理机制: 验证失败")
        
        # 总体评估
        total_score = (
            (100 if celery_status['clean'] else 0) * 0.3 +
            (100 if db_status['status_health'] == 'EXCELLENT' else 50 if db_status['status_health'] == 'GOOD' else 0) * 0.3 +
            atomic_status.get('fix_score', 0) * 0.2 +
            prevention_status.get('prevention_score', 0) * 0.2
        )
        
        logger.info(f"\n🎯 紧急修复验证总分: {total_score:.1f}/100")
        
        if total_score >= 80:
            logger.success("✅ 紧急修复验证通过，系统可以重新启动")
            logger.success("🚀 建议：小批量测试后再全面重启")
        elif total_score >= 60:
            logger.warning("⚠️ 紧急修复基本成功，但需要关注部分问题")
        else:
            logger.error("❌ 紧急修复验证失败，需要进一步修复")
        
        # 下一步建议
        logger.info("\n💡 下一步建议:")
        if processing_count > 0:
            logger.info("   1. 立即重置processing状态积压")
            logger.info("   2. 清理Redis队列残留任务")
        
        logger.info("   3. 小批量测试原子状态管理器")
        logger.info("   4. 验证防重复处理机制")
        logger.info("   5. 逐步重启Celery服务")


def main():
    """主函数"""
    verifier = EmergencyFixVerification()
    verifier.run_emergency_verification()
    
    logger.info("🚨 紧急修复验证完成")
    logger.info("⚠️ 请根据验证结果决定下一步行动")
    
    return 0


if __name__ == "__main__":
    exit(main())
