#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: Streamlit素材搜索页面集成代码
清理条件: 功能完全集成到主系统后删除

千川自动化Streamlit素材搜索页面集成代码
===================================

用于集成到现有的Streamlit应用中，提供增强的素材搜索功能
"""

import streamlit as st
import pandas as pd
from datetime import datetime, date
from typing import Dict, List, Any, Optional
import os
import sys

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.database import SessionLocal
from qianchuan_aw.database.models import LocalCreative, Campaign, PlatformCreative, AdAccount, Principal
from contextlib import contextmanager

@contextmanager
def database_session():
    """数据库会话上下文管理器"""
    session = SessionLocal()
    try:
        yield session
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()


class StreamlitMaterialSearchEnhanced:
    """Streamlit增强版素材搜索"""
    
    def __init__(self):
        self.status_mapping = {
            'new': '新素材',
            'pending_grouping': '待分组',
            'pending_upload': '待上传',
            'upload_failed': '上传失败',
            'uploaded_pending_plan': '已上传待建计划',
            'testing_pending_review': '测试中待审核',
            'approved': '审核通过',
            'rejected': '审核拒绝',
            'harvested': '已收割',
            'already_tested': '已测试过',
            'not_harvested': '未收割'
        }
    
    def render_search_form(self):
        """渲染增强版搜索表单"""
        st.subheader("🔍 视频素材搜索（增强版）")
        st.caption("搜索结果包含千川广告户ID、测试计划ID和中文状态显示")
        
        with st.form("enhanced_material_search"):
            col1, col2, col3 = st.columns(3)
            
            with col1:
                filename_search = st.text_input("📹 素材文件名", placeholder="输入文件名关键词")
                
            with col2:
                status_options = ['全部'] + list(self.status_mapping.values())
                status_filter = st.selectbox("📊 素材状态", options=status_options)
                
            with col3:
                # 获取主体列表
                with database_session() as db:
                    principals = db.query(Principal).all()
                    principal_options = ['全部'] + [p.name for p in principals]
                
                principal_filter = st.selectbox("🏢 主体筛选", options=principal_options)
            
            col4, col5 = st.columns(2)
            with col4:
                start_date = st.date_input("📅 开始日期", value=None)
            with col5:
                end_date = st.date_input("📅 结束日期", value=None)
            
            search_submitted = st.form_submit_button("🔍 搜索素材", type="primary")
        
        if search_submitted:
            search_params = {
                'filename': filename_search if filename_search else None,
                'status': status_filter,
                'principal': principal_filter,
                'start_date': datetime.combine(start_date, datetime.min.time()) if start_date else None,
                'end_date': datetime.combine(end_date, datetime.max.time()) if end_date else None,
                'limit': 20
            }
            
            self.display_enhanced_results(search_params)
    
    def get_enhanced_material_info(self, db, material_id: int) -> Dict[str, Any]:
        """获取增强的素材信息"""
        try:
            # 查询素材基本信息
            material = db.query(LocalCreative).filter(LocalCreative.id == material_id).first()
            if not material:
                return {}
            
            # 查询关联的平台素材
            platform_creative = db.query(PlatformCreative).filter(
                PlatformCreative.local_creative_id == material.id
            ).first()
            
            # 查询关联的测试计划
            campaigns = []
            ad_account_info = None
            
            if platform_creative:
                from sqlalchemy.orm import joinedload
                campaigns = db.query(Campaign).join(
                    Campaign.platform_creatives
                ).filter(
                    PlatformCreative.id == platform_creative.id
                ).options(
                    joinedload(Campaign.account).joinedload(AdAccount.principal)
                ).all()
                
                # 获取广告账户信息
                if campaigns:
                    ad_account_info = campaigns[0].account
            
            # 构建增强信息
            enhanced_info = {
                'basic_info': {
                    'id': material.id,
                    'filename': os.path.basename(material.file_path) if material.file_path else '',
                    'file_hash': material.file_hash,
                    'status_en': material.status,
                    'status_cn': self.status_mapping.get(material.status, material.status),
                    'created_at': material.created_at,
                    'updated_at': material.updated_at
                },
                'account_info': {
                    'principal_name': ad_account_info.principal.name if ad_account_info and ad_account_info.principal else '未知主体',
                    'ad_account_id': ad_account_info.account_id_qc if ad_account_info else '未分配',
                    'account_name': ad_account_info.name if ad_account_info else '未分配'
                },
                'campaign_info': {
                    'campaign_count': len(campaigns),
                    'campaigns': [
                        {
                            'campaign_id_qc': campaign.campaign_id_qc,
                            'status': campaign.status,
                            'appeal_status': campaign.appeal_status,
                            'created_at': campaign.created_at
                        }
                        for campaign in campaigns
                    ]
                },
                'platform_info': {
                    'material_id_qc': platform_creative.material_id_qc if platform_creative else None,
                    'review_status': platform_creative.review_status if platform_creative else '未上传'
                }
            }
            
            return enhanced_info
            
        except Exception as e:
            logger.error(f"获取增强素材信息失败: {e}")
            return {}
    
    def search_materials_enhanced(self, db, search_params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """增强版素材搜索"""
        try:
            # 构建查询
            query = db.query(LocalCreative)
            
            # 应用搜索条件
            conditions = []
            
            # 文件名搜索
            if search_params.get('filename'):
                conditions.append(LocalCreative.file_path.contains(search_params['filename']))
            
            # 状态筛选
            if search_params.get('status') and search_params['status'] != '全部':
                # 支持中文状态搜索
                status_en = None
                for en_status, cn_status in self.status_mapping.items():
                    if cn_status == search_params['status']:
                        status_en = en_status
                        break
                
                if status_en:
                    conditions.append(LocalCreative.status == status_en)
                else:
                    conditions.append(LocalCreative.status == search_params['status'])
            
            # 时间范围筛选
            if search_params.get('start_date'):
                conditions.append(LocalCreative.created_at >= search_params['start_date'])
            
            if search_params.get('end_date'):
                conditions.append(LocalCreative.created_at <= search_params['end_date'])
            
            # 应用所有条件
            if conditions:
                from sqlalchemy import and_
                query = query.filter(and_(*conditions))
            
            # 执行查询
            materials = query.order_by(LocalCreative.created_at.desc()).limit(search_params.get('limit', 20)).all()
            
            # 构建增强结果
            enhanced_results = []
            for material in materials:
                enhanced_info = self.get_enhanced_material_info(db, material.id)
                if enhanced_info:
                    enhanced_results.append(enhanced_info)
            
            return enhanced_results
            
        except Exception as e:
            logger.error(f"增强版素材搜索失败: {e}")
            return []
    
    def display_enhanced_results(self, search_params: Dict[str, Any]):
        """显示增强版搜索结果"""
        with st.spinner("🔍 正在搜索素材..."):
            with database_session() as db:
                results = self.search_materials_enhanced(db, search_params)
        
        if not results:
            st.warning("🔍 未找到匹配的素材")
            return
        
        st.success(f"🎉 找到 {len(results)} 个匹配的素材")
        
        # 显示结果
        for i, result in enumerate(results, 1):
            basic = result['basic_info']
            account = result['account_info']
            campaign = result['campaign_info']
            platform = result['platform_info']
            
            with st.expander(f"📹 {basic['filename']} - {basic['status_cn']}", expanded=i <= 3):
                col1, col2 = st.columns(2)
                
                with col1:
                    st.markdown("### 📋 基本信息")
                    st.write(f"**素材ID**: {basic['id']}")
                    st.write(f"**文件名**: {basic['filename']}")
                    st.write(f"**状态**: {basic['status_cn']}")
                    st.write(f"**创建时间**: {basic['created_at'].strftime('%Y-%m-%d %H:%M:%S') if basic['created_at'] else '未知'}")
                    
                    st.markdown("### 🏢 账户信息")
                    st.write(f"**主体名称**: {account['principal_name']}")
                    st.write(f"**千川广告户ID**: `{account['ad_account_id']}`")
                    st.write(f"**账户名称**: {account['account_name']}")
                
                with col2:
                    st.markdown("### 🎯 测试计划信息")
                    if campaign['campaigns']:
                        st.write(f"**计划数量**: {campaign['campaign_count']} 个")
                        
                        # 显示计划列表
                        for j, camp in enumerate(campaign['campaigns'][:3], 1):  # 最多显示3个
                            st.write(f"**计划{j}**: `{camp['campaign_id_qc']}`")
                            st.write(f"  - 状态: {camp['status']}")
                            if camp['appeal_status']:
                                st.write(f"  - 申诉状态: {camp['appeal_status']}")
                        
                        if len(campaign['campaigns']) > 3:
                            st.write(f"... 还有 {len(campaign['campaigns']) - 3} 个计划")
                    else:
                        st.write("**暂无测试计划**")
                    
                    st.markdown("### 🔗 平台信息")
                    if platform['material_id_qc']:
                        st.write(f"**平台素材ID**: `{platform['material_id_qc']}`")
                        st.write(f"**审核状态**: {platform['review_status']}")
                    else:
                        st.write("**未上传到平台**")
                
                # 文件哈希（折叠显示）
                with st.expander("🔐 文件哈希信息"):
                    st.code(basic['file_hash'], language='text')
    
    def render_enhanced_search_page(self):
        """渲染完整的增强版搜索页面"""
        st.title("🔍 视频素材搜索（增强版）")
        
        # 显示改进说明
        with st.expander("✨ 增强功能说明", expanded=False):
            st.markdown("""
            ### 🎯 本次改进内容：
            1. **上传账户显示千川广告户ID** - 不再显示主体名称，直接显示千川平台的广告户ID
            2. **添加测试计划ID显示** - 显示所有关联的测试计划ID和状态
            3. **状态使用中文名展示** - 将英文状态转换为易懂的中文状态
            4. **增强搜索结果全面性** - 提供完整的关联信息和详细展示
            
            ### 📊 状态对照表：
            - `new` → 新素材
            - `approved` → 审核通过  
            - `rejected` → 审核拒绝
            - `harvested` → 已收割
            - `already_tested` → 已测试过
            """)
        
        # 渲染搜索表单
        self.render_search_form()


def main():
    """主函数 - 用于独立运行测试"""
    st.set_page_config(
        page_title="千川自动化 - 增强版素材搜索",
        page_icon="🔍",
        layout="wide"
    )
    
    search_enhancer = StreamlitMaterialSearchEnhanced()
    search_enhancer.render_enhanced_search_page()


if __name__ == '__main__':
    main()
