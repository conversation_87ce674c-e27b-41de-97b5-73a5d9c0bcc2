#!/usr/bin/env python3
"""
验证new状态素材是否真的没有创建过计划
确保不会重复创建已有计划的素材
"""

import sys
import os
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text

class NewMaterialVerifier:
    """new状态素材验证器"""
    
    def __init__(self):
        logger.critical("🔍 验证new状态素材是否真的没有创建过计划")
        logger.critical("=" * 60)
    
    def check_new_materials_campaigns(self):
        """检查new状态素材是否有计划"""
        logger.critical("📊 检查new状态素材的计划情况")
        logger.critical("=" * 60)
        
        try:
            with database_session() as db:
                # 检查new状态素材是否有关联的计划
                new_materials_with_campaigns_query = text("""
                    SELECT 
                        lc.id,
                        lc.filename,
                        lc.file_hash,
                        lc.status,
                        lc.created_at,
                        lc.updated_at,
                        COUNT(c.id) as campaign_count,
                        STRING_AGG(c.campaign_id_qc, ', ') as campaign_ids,
                        STRING_AGG(aa.name, ', ') as account_names
                    FROM local_creatives lc
                    LEFT JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    LEFT JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                    LEFT JOIN campaigns c ON cpca.campaign_id = c.id
                    LEFT JOIN ad_accounts aa ON c.account_id = aa.id
                    WHERE lc.status = 'new'
                    GROUP BY lc.id, lc.filename, lc.file_hash, lc.status, lc.created_at, lc.updated_at
                    HAVING COUNT(c.id) > 0
                    ORDER BY campaign_count DESC, lc.filename
                """)
                
                new_with_campaigns = db.execute(new_materials_with_campaigns_query).fetchall()
                
                logger.critical(f"🚨 发现 {len(new_with_campaigns)} 个new状态素材已有计划!")
                
                if new_with_campaigns:
                    logger.critical("📋 详细情况:")
                    for material in new_with_campaigns:
                        logger.critical(f"  📁 {material.filename}:")
                        logger.critical(f"    状态: {material.status}")
                        logger.critical(f"    计划数: {material.campaign_count}")
                        logger.critical(f"    计划ID: {material.campaign_ids}")
                        logger.critical(f"    账户: {material.account_names}")
                        logger.critical(f"    创建时间: {material.created_at}")
                        logger.critical(f"    更新时间: {material.updated_at}")
                        logger.critical(f"    file_hash: {material.file_hash[:16]}...")
                
                # 统计总的new状态素材
                total_new_query = text("""
                    SELECT COUNT(*) as count
                    FROM local_creatives
                    WHERE status = 'new'
                """)
                
                total_new = db.execute(total_new_query).fetchone()
                
                logger.critical(f"\n📊 统计:")
                logger.critical(f"  总new状态素材: {total_new.count}")
                logger.critical(f"  有计划的new素材: {len(new_with_campaigns)}")
                logger.critical(f"  真正无计划的new素材: {total_new.count - len(new_with_campaigns)}")
                
                return {
                    'total_new': total_new.count,
                    'new_with_campaigns': len(new_with_campaigns),
                    'truly_new': total_new.count - len(new_with_campaigns),
                    'problematic_materials': new_with_campaigns
                }
                
        except Exception as e:
            logger.error(f"❌ 检查new状态素材失败: {e}")
            return {}
    
    def check_by_file_hash(self):
        """通过file_hash检查是否有重复"""
        logger.critical("\n🔍 通过file_hash检查new状态素材是否有重复计划")
        logger.critical("=" * 60)
        
        try:
            with database_session() as db:
                # 通过file_hash检查new状态素材是否有计划
                hash_check_query = text("""
                    WITH new_materials AS (
                        SELECT 
                            lc.id,
                            lc.filename,
                            lc.file_hash,
                            lc.status
                        FROM local_creatives lc
                        WHERE lc.status = 'new'
                    ),
                    campaigns_by_hash AS (
                        SELECT 
                            lc.file_hash,
                            COUNT(c.id) as campaign_count,
                            STRING_AGG(c.campaign_id_qc, ', ') as campaign_ids,
                            STRING_AGG(aa.name, ', ') as account_names
                        FROM local_creatives lc
                        JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                        JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                        JOIN campaigns c ON cpca.campaign_id = c.id
                        JOIN ad_accounts aa ON c.account_id = aa.id
                        GROUP BY lc.file_hash
                    )
                    SELECT 
                        nm.id,
                        nm.filename,
                        nm.file_hash,
                        nm.status,
                        COALESCE(cbh.campaign_count, 0) as campaign_count,
                        cbh.campaign_ids,
                        cbh.account_names
                    FROM new_materials nm
                    LEFT JOIN campaigns_by_hash cbh ON nm.file_hash = cbh.file_hash
                    WHERE cbh.campaign_count > 0
                    ORDER BY cbh.campaign_count DESC, nm.filename
                """)
                
                hash_conflicts = db.execute(hash_check_query).fetchall()
                
                logger.critical(f"🚨 通过file_hash发现 {len(hash_conflicts)} 个new状态素材有重复计划!")
                
                if hash_conflicts:
                    logger.critical("📋 file_hash冲突详情:")
                    for conflict in hash_conflicts:
                        logger.critical(f"  📁 {conflict.filename}:")
                        logger.critical(f"    状态: {conflict.status}")
                        logger.critical(f"    file_hash: {conflict.file_hash[:16]}...")
                        logger.critical(f"    已有计划数: {conflict.campaign_count}")
                        logger.critical(f"    计划ID: {conflict.campaign_ids}")
                        logger.critical(f"    账户: {conflict.account_names}")
                
                return hash_conflicts
                
        except Exception as e:
            logger.error(f"❌ file_hash检查失败: {e}")
            return []
    
    def fix_problematic_new_materials(self, problematic_materials):
        """修复有问题的new状态素材"""
        logger.critical("\n🔧 修复有问题的new状态素材")
        logger.critical("=" * 60)
        
        if not problematic_materials:
            logger.critical("✅ 没有发现需要修复的new状态素材")
            return 0
        
        try:
            with database_session() as db:
                # 将有计划的new状态素材改为already_tested
                material_ids = [m.id for m in problematic_materials]
                
                fix_query = text("""
                    UPDATE local_creatives 
                    SET status = 'already_tested',
                        updated_at = NOW()
                    WHERE id = ANY(:material_ids)
                """)
                
                result = db.execute(fix_query, {'material_ids': material_ids})
                db.commit()
                
                logger.critical(f"✅ 修复了 {result.rowcount} 个有计划的new状态素材")
                logger.critical(f"   new → already_tested")
                
                return result.rowcount
                
        except Exception as e:
            logger.error(f"❌ 修复new状态素材失败: {e}")
            return 0
    
    def verify_test_accounts(self):
        """验证测试账户情况"""
        logger.critical("\n📊 验证测试账户情况")
        logger.critical("=" * 60)
        
        try:
            with database_session() as db:
                # 检查测试账户的计划情况
                test_accounts_query = text("""
                    SELECT 
                        aa.id,
                        aa.name,
                        aa.account_type,
                        aa.status,
                        COUNT(c.id) as campaign_count,
                        COUNT(DISTINCT lc.file_hash) as unique_videos
                    FROM ad_accounts aa
                    LEFT JOIN campaigns c ON aa.id = c.account_id
                    LEFT JOIN campaign_platform_creative_association cpca ON c.id = cpca.campaign_id
                    LEFT JOIN platform_creatives pc ON cpca.platform_creative_id = pc.id
                    LEFT JOIN local_creatives lc ON pc.local_creative_id = lc.id
                    WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                    GROUP BY aa.id, aa.name, aa.account_type, aa.status
                    ORDER BY campaign_count DESC
                """)
                
                test_accounts = db.execute(test_accounts_query).fetchall()
                
                logger.critical("📋 测试账户计划情况:")
                total_campaigns = 0
                total_videos = 0
                
                for account in test_accounts:
                    logger.critical(f"  📋 {account.name}:")
                    logger.critical(f"    计划数: {account.campaign_count}")
                    logger.critical(f"    唯一视频数: {account.unique_videos}")
                    total_campaigns += account.campaign_count
                    total_videos += account.unique_videos
                
                logger.critical(f"\n📊 测试账户总计:")
                logger.critical(f"  总计划数: {total_campaigns}")
                logger.critical(f"  总唯一视频数: {total_videos}")
                
                return {
                    'test_accounts': test_accounts,
                    'total_campaigns': total_campaigns,
                    'total_videos': total_videos
                }
                
        except Exception as e:
            logger.error(f"❌ 验证测试账户失败: {e}")
            return {}
    
    def generate_verification_report(self, check_result, hash_conflicts, test_accounts_info):
        """生成验证报告"""
        logger.critical("\n📋 new状态素材验证报告")
        logger.critical("=" * 60)
        
        logger.critical("🎯 验证目标:")
        logger.critical("  ✅ 确认506个new状态素材是否真的没有计划")
        logger.critical("  ✅ 防止重复创建已有计划的素材")
        logger.critical("  ✅ 验证测试账户的计划情况")
        
        logger.critical("\n📊 验证结果:")
        if check_result:
            logger.critical(f"  总new状态素材: {check_result['total_new']}")
            logger.critical(f"  有计划的new素材: {check_result['new_with_campaigns']}")
            logger.critical(f"  真正无计划的new素材: {check_result['truly_new']}")
        
        logger.critical(f"  file_hash冲突: {len(hash_conflicts)}")
        
        if test_accounts_info:
            logger.critical(f"  测试账户总计划: {test_accounts_info['total_campaigns']}")
            logger.critical(f"  测试账户总视频: {test_accounts_info['total_videos']}")
        
        logger.critical("\n🚨 关键发现:")
        if check_result and check_result['new_with_campaigns'] > 0:
            logger.critical(f"  ❌ 发现 {check_result['new_with_campaigns']} 个new状态素材已有计划")
            logger.critical(f"  ❌ 这些素材不应该重新创建计划")
        
        if len(hash_conflicts) > 0:
            logger.critical(f"  ❌ 发现 {len(hash_conflicts)} 个file_hash冲突")
            logger.critical(f"  ❌ 这些素材的视频文件已经有计划了")
        
        if check_result and check_result['new_with_campaigns'] == 0 and len(hash_conflicts) == 0:
            logger.critical("  ✅ 所有new状态素材都没有计划，可以安全创建")
        
        logger.critical("\n💡 建议:")
        if check_result and check_result['new_with_campaigns'] > 0:
            logger.critical("  1. 立即修复有计划的new状态素材")
            logger.critical("  2. 将它们的状态改为already_tested")
            logger.critical("  3. 防止重复创建计划")
        else:
            logger.critical("  1. new状态素材验证通过")
            logger.critical("  2. 可以安全重启Celery服务")
            logger.critical("  3. 按9个一组创建计划")

def main():
    """主验证函数"""
    try:
        verifier = NewMaterialVerifier()
        
        # 1. 检查new状态素材的计划情况
        check_result = verifier.check_new_materials_campaigns()
        
        # 2. 通过file_hash检查重复
        hash_conflicts = verifier.check_by_file_hash()
        
        # 3. 验证测试账户情况
        test_accounts_info = verifier.verify_test_accounts()
        
        # 4. 修复有问题的素材
        if check_result and check_result.get('problematic_materials'):
            fixed_count = verifier.fix_problematic_new_materials(check_result['problematic_materials'])
            logger.critical(f"\n🔧 修复了 {fixed_count} 个有问题的new状态素材")
        
        # 5. 生成验证报告
        verifier.generate_verification_report(check_result, hash_conflicts, test_accounts_info)
        
        # 判断是否安全
        is_safe = True
        if check_result and check_result['new_with_campaigns'] > 0:
            is_safe = False
        if len(hash_conflicts) > 0:
            is_safe = False
        
        logger.critical(f"\n🎉 验证完成!")
        logger.critical(f"安全状态: {'✅ 安全' if is_safe else '❌ 需要修复'}")
        
        return is_safe
        
    except Exception as e:
        logger.error(f"❌ 验证失败: {e}")
        return False

if __name__ == "__main__":
    main()
