#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 分析工具
生命周期: 永久保留
创建目的: 千川自动化项目核心工作流系统性诊断和修复方案
清理条件: 项目核心架构稳定后可归档
"""

import os
import sys
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.database import SessionLocal
from qianchuan_aw.database.models import LocalCreative, PlatformCreative, Campaign
from qianchuan_aw.utils.config_loader import load_settings
from sqlalchemy import text, func
from sqlalchemy.orm import joinedload


class WorkflowIssueType(Enum):
    """工作流问题类型"""
    MISSING_CAMPAIGNS = "missing_campaigns"           # 遗漏测试计划
    STATUS_INCONSISTENCY = "status_inconsistency"     # 状态不一致
    ORPHANED_RECORDS = "orphaned_records"             # 孤立记录
    TIMEOUT_STALE = "timeout_stale"                   # 超时卡住
    RETRY_EXHAUSTED = "retry_exhausted"               # 重试耗尽
    CELERY_TASK_FAILURE = "celery_task_failure"       # Celery任务失败
    API_RATE_LIMIT = "api_rate_limit"                 # API限流
    DATABASE_INTEGRITY = "database_integrity"         # 数据库完整性


@dataclass
class WorkflowIssue:
    """工作流问题"""
    issue_type: WorkflowIssueType
    severity: str  # CRITICAL, HIGH, MEDIUM, LOW
    count: int
    description: str
    affected_materials: List[int]
    root_cause: str
    fix_priority: int
    estimated_fix_time: str


class CoreWorkflowDiagnostic:
    """核心工作流诊断器"""
    
    def __init__(self):
        self.app_settings = load_settings()
        self.issues: List[WorkflowIssue] = []
        self.stats = {}
        
    def run_comprehensive_diagnosis(self) -> Dict[str, Any]:
        """运行全面诊断"""
        logger.critical("🔍 开始千川自动化项目核心工作流系统性诊断")
        logger.critical("=" * 80)
        
        with SessionLocal() as db:
            # 1. 数据库状态分析
            self._analyze_database_state(db)
            
            # 2. 核心业务规则违反检查
            self._check_business_rule_violations(db)
            
            # 3. 工作流状态一致性检查
            self._check_workflow_consistency(db)
            
            # 4. 超时和卡住状态检查
            self._check_timeout_and_stale_states(db)
            
            # 5. Celery任务健康检查
            self._check_celery_health()
            
            # 6. API调用稳定性分析
            self._analyze_api_stability(db)
            
            # 7. 数据库完整性检查
            self._check_database_integrity(db)
            
        # 8. 生成诊断报告
        return self._generate_diagnosis_report()
    
    def _analyze_database_state(self, db):
        """分析数据库状态分布"""
        logger.info("📊 分析数据库状态分布...")
        
        # 查询状态分布
        status_distribution = db.execute(text("""
            SELECT 
                lc.status,
                COUNT(*) as total_count,
                COUNT(pc.id) as uploaded_count,
                COUNT(cpc.campaign_id) as campaign_count,
                (COUNT(pc.id) - COUNT(cpc.campaign_id)) as missing_campaigns
            FROM local_creatives lc
            LEFT JOIN platform_creatives pc ON lc.id = pc.local_creative_id
            LEFT JOIN campaign_platform_creative_association cpc ON pc.id = cpc.platform_creative_id
            GROUP BY lc.status
            ORDER BY total_count DESC
        """)).fetchall()
        
        self.stats['status_distribution'] = []
        total_missing_campaigns = 0
        
        for row in status_distribution:
            status_info = {
                'status': row.status,
                'total_count': row.total_count,
                'uploaded_count': row.uploaded_count,
                'campaign_count': row.campaign_count,
                'missing_campaigns': row.missing_campaigns
            }
            self.stats['status_distribution'].append(status_info)
            
            if row.missing_campaigns > 0:
                total_missing_campaigns += row.missing_campaigns
                
                # 记录遗漏测试计划问题
                severity = "CRITICAL" if row.missing_campaigns > 1000 else "HIGH" if row.missing_campaigns > 100 else "MEDIUM"
                
                self.issues.append(WorkflowIssue(
                    issue_type=WorkflowIssueType.MISSING_CAMPAIGNS,
                    severity=severity,
                    count=row.missing_campaigns,
                    description=f"状态为'{row.status}'的素材中有{row.missing_campaigns}个已上传但未创建测试计划",
                    affected_materials=[],  # 稍后填充
                    root_cause="计划创建工作流中断或失败",
                    fix_priority=1 if severity == "CRITICAL" else 2,
                    estimated_fix_time="2-4小时"
                ))
        
        self.stats['total_missing_campaigns'] = total_missing_campaigns
        logger.warning(f"📊 发现总计 {total_missing_campaigns} 个遗漏的测试计划")
    
    def _check_business_rule_violations(self, db):
        """检查核心业务规则违反"""
        logger.info("⚖️ 检查核心业务规则违反...")
        
        # 规则1: 每个已上传素材都必须有测试计划
        uploaded_without_campaigns = db.execute(text("""
            SELECT lc.id, lc.file_path, lc.status, pc.material_id_qc
            FROM local_creatives lc
            JOIN platform_creatives pc ON lc.id = pc.local_creative_id
            LEFT JOIN campaign_platform_creative_association cpc ON pc.id = cpc.platform_creative_id
            WHERE cpc.campaign_id IS NULL
            LIMIT 100
        """)).fetchall()
        
        if uploaded_without_campaigns:
            material_ids = [row.id for row in uploaded_without_campaigns]
            self.issues.append(WorkflowIssue(
                issue_type=WorkflowIssueType.MISSING_CAMPAIGNS,
                severity="CRITICAL",
                count=len(uploaded_without_campaigns),
                description="核心业务规则违反：已上传素材未创建测试计划",
                affected_materials=material_ids,
                root_cause="handle_plan_creation工作流执行失败或中断",
                fix_priority=1,
                estimated_fix_time="1-2小时"
            ))
        
        # 规则2: 状态为testing_pending_review的素材必须有测试计划
        testing_without_campaigns = db.execute(text("""
            SELECT lc.id, lc.file_path
            FROM local_creatives lc
            JOIN platform_creatives pc ON lc.id = pc.local_creative_id
            LEFT JOIN campaign_platform_creative_association cpc ON pc.id = cpc.platform_creative_id
            WHERE lc.status = 'testing_pending_review' AND cpc.campaign_id IS NULL
        """)).fetchall()
        
        if testing_without_campaigns:
            material_ids = [row.id for row in testing_without_campaigns]
            self.issues.append(WorkflowIssue(
                issue_type=WorkflowIssueType.STATUS_INCONSISTENCY,
                severity="HIGH",
                count=len(testing_without_campaigns),
                description="状态不一致：testing_pending_review状态但无测试计划",
                affected_materials=material_ids,
                root_cause="状态更新与计划创建不同步",
                fix_priority=2,
                estimated_fix_time="30分钟"
            ))
    
    def _check_workflow_consistency(self, db):
        """检查工作流状态一致性"""
        logger.info("🔄 检查工作流状态一致性...")
        
        # 检查uploaded_pending_plan状态的素材数量
        pending_plan_count = db.execute(text("""
            SELECT COUNT(*) as count
            FROM local_creatives lc
            JOIN platform_creatives pc ON lc.id = pc.local_creative_id
            WHERE lc.status = 'uploaded_pending_plan'
        """)).scalar()
        
        if pending_plan_count == 0:
            # 这可能表明计划创建工作流运行正常，或者没有新素材
            logger.info("✅ 没有待创建计划的素材，工作流状态正常")
        else:
            # 有待创建计划的素材，需要检查是否卡住
            self.issues.append(WorkflowIssue(
                issue_type=WorkflowIssueType.TIMEOUT_STALE,
                severity="MEDIUM",
                count=pending_plan_count,
                description=f"有{pending_plan_count}个素材处于uploaded_pending_plan状态",
                affected_materials=[],
                root_cause="计划创建工作流可能暂停或延迟",
                fix_priority=3,
                estimated_fix_time="检查Celery任务状态"
            ))
    
    def _check_timeout_and_stale_states(self, db):
        """检查超时和卡住状态"""
        logger.info("⏰ 检查超时和卡住状态...")
        
        # 检查长时间处于processing状态的素材
        stale_processing = db.execute(text("""
            SELECT COUNT(*) as count
            FROM local_creatives
            WHERE status = 'processing'
            AND updated_at < NOW() - INTERVAL '1 hour'
        """)).scalar()
        
        if stale_processing > 0:
            self.issues.append(WorkflowIssue(
                issue_type=WorkflowIssueType.TIMEOUT_STALE,
                severity="HIGH",
                count=stale_processing,
                description=f"{stale_processing}个素材长时间处于processing状态",
                affected_materials=[],
                root_cause="上传任务超时或Celery worker异常",
                fix_priority=2,
                estimated_fix_time="重启Celery或状态重置"
            ))
    
    def _check_celery_health(self):
        """检查Celery任务健康状态"""
        logger.info("🔧 检查Celery任务健康状态...")
        
        # 检查关键文件是否存在
        celery_files = [
            'run_celery_worker.py',
            'run_celery_beat.py',
            'src/qianchuan_aw/celery_app.py',
            'src/qianchuan_aw/workflows/tasks.py'
        ]
        
        missing_files = []
        for file_path in celery_files:
            if not os.path.exists(os.path.join(project_root, file_path)):
                missing_files.append(file_path)
        
        if missing_files:
            self.issues.append(WorkflowIssue(
                issue_type=WorkflowIssueType.CELERY_TASK_FAILURE,
                severity="CRITICAL",
                count=len(missing_files),
                description=f"Celery关键文件缺失: {', '.join(missing_files)}",
                affected_materials=[],
                root_cause="项目文件结构不完整",
                fix_priority=1,
                estimated_fix_time="立即修复"
            ))
    
    def _analyze_api_stability(self, db):
        """分析API调用稳定性"""
        logger.info("🌐 分析API调用稳定性...")
        
        # 检查upload_failed状态的素材
        upload_failed_count = db.execute(text("""
            SELECT COUNT(*) as count
            FROM local_creatives
            WHERE status = 'upload_failed'
        """)).scalar()
        
        if upload_failed_count > 0:
            self.issues.append(WorkflowIssue(
                issue_type=WorkflowIssueType.RETRY_EXHAUSTED,
                severity="MEDIUM",
                count=upload_failed_count,
                description=f"{upload_failed_count}个素材上传失败",
                affected_materials=[],
                root_cause="API调用失败或网络问题",
                fix_priority=3,
                estimated_fix_time="检查网络和API状态"
            ))
    
    def _check_database_integrity(self, db):
        """检查数据库完整性"""
        logger.info("🗄️ 检查数据库完整性...")
        
        # 检查孤立的platform_creative记录
        orphaned_pc = db.execute(text("""
            SELECT COUNT(*) as count
            FROM platform_creatives pc
            LEFT JOIN local_creatives lc ON pc.local_creative_id = lc.id
            WHERE lc.id IS NULL
        """)).scalar()
        
        if orphaned_pc > 0:
            self.issues.append(WorkflowIssue(
                issue_type=WorkflowIssueType.ORPHANED_RECORDS,
                severity="LOW",
                count=orphaned_pc,
                description=f"{orphaned_pc}个孤立的platform_creative记录",
                affected_materials=[],
                root_cause="数据库清理不完整",
                fix_priority=4,
                estimated_fix_time="数据库清理脚本"
            ))
    
    def _generate_diagnosis_report(self) -> Dict[str, Any]:
        """生成诊断报告"""
        logger.critical("📋 生成诊断报告...")
        
        # 按严重程度和优先级排序问题
        self.issues.sort(key=lambda x: (x.fix_priority, x.severity == "CRITICAL"))
        
        report = {
            'diagnosis_time': datetime.now().isoformat(),
            'total_issues': len(self.issues),
            'critical_issues': len([i for i in self.issues if i.severity == "CRITICAL"]),
            'high_issues': len([i for i in self.issues if i.severity == "HIGH"]),
            'medium_issues': len([i for i in self.issues if i.severity == "MEDIUM"]),
            'low_issues': len([i for i in self.issues if i.severity == "LOW"]),
            'database_stats': self.stats,
            'issues': [
                {
                    'type': issue.issue_type.value,
                    'severity': issue.severity,
                    'count': issue.count,
                    'description': issue.description,
                    'root_cause': issue.root_cause,
                    'fix_priority': issue.fix_priority,
                    'estimated_fix_time': issue.estimated_fix_time,
                    'affected_count': len(issue.affected_materials)
                }
                for issue in self.issues
            ]
        }
        
        return report


def main():
    """主函数"""
    try:
        diagnostic = CoreWorkflowDiagnostic()
        report = diagnostic.run_comprehensive_diagnosis()
        
        # 输出诊断结果
        logger.critical("🎯 核心工作流诊断完成")
        logger.critical("=" * 80)
        logger.critical(f"📊 总计发现 {report['total_issues']} 个问题")
        logger.critical(f"🚨 严重问题: {report['critical_issues']} 个")
        logger.critical(f"⚠️ 高优先级问题: {report['high_issues']} 个")
        logger.critical(f"📋 中等问题: {report['medium_issues']} 个")
        logger.critical(f"ℹ️ 低优先级问题: {report['low_issues']} 个")
        
        # 保存详细报告
        report_path = f"ai_reports/ai_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}_core_workflow_diagnosis.json"
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.success(f"📄 详细诊断报告已保存: {report_path}")
        
        return report
        
    except Exception as e:
        logger.error(f"诊断过程中发生错误: {e}", exc_info=True)
        return None


if __name__ == "__main__":
    main()
