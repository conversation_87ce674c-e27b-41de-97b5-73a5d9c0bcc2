#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
原子状态管理器 - 修复版本
确保状态转换的原子性，防止状态不一致
"""

import os
from datetime import datetime, timezone
from contextlib import contextmanager
from typing import List, Dict, Any
from qianchuan_aw.database.models import LocalCreative
from qianchuan_aw.utils.logger import logger


class AtomicStateManager:
    """原子状态管理器 - 确保状态转换的原子性"""
    
    def __init__(self, db_session):
        self.db = db_session
    
    @contextmanager
    def atomic_state_transition(self, creative_id: int, from_status: str, to_status: str):
        """原子状态转换上下文管理器 - 修复版本"""
        creative = None
        savepoint = None
        
        try:
            # 创建保存点
            savepoint = self.db.begin_nested()
            
            # 获取并锁定记录
            creative = self.db.query(LocalCreative).filter(
                LocalCreative.id == creative_id,
                LocalCreative.status == from_status
            ).with_for_update().first()
            
            if not creative:
                raise ValueError(f"素材 {creative_id} 不存在或状态不匹配 (期望: {from_status})")
            
            # 更新状态
            original_status = creative.status
            creative.status = to_status
            creative.updated_at = datetime.now(timezone.utc)
            
            # 提供给调用者使用
            yield creative
            
            # 只有在yield成功完成后才提交保存点
            savepoint.commit()
            
            # 最后提交主事务
            self.db.commit()
            
            logger.debug(f"✅ 状态转换成功: {creative_id} {from_status} → {to_status}")
            
        except Exception as e:
            # 回滚到保存点
            if savepoint:
                savepoint.rollback()
            
            # 恢复原始状态
            if creative:
                creative.status = original_status if 'original_status' in locals() else from_status
            
            # 回滚主事务
            self.db.rollback()
            
            logger.error(f"❌ 状态转换失败: {creative_id} {from_status} → {to_status}: {e}")
            raise
    
    @contextmanager
    def batch_atomic_transition(self, transitions: List[Dict[str, Any]]):
        """批量原子状态转换"""
        savepoint = None
        updated_creatives = []
        
        try:
            savepoint = self.db.begin_nested()
            
            for transition in transitions:
                creative_id = transition['creative_id']
                from_status = transition['from_status']
                to_status = transition['to_status']
                
                creative = self.db.query(LocalCreative).filter(
                    LocalCreative.id == creative_id,
                    LocalCreative.status == from_status
                ).with_for_update().first()
                
                if not creative:
                    raise ValueError(f"素材 {creative_id} 不存在或状态不匹配")
                
                creative.status = to_status
                creative.updated_at = datetime.now(timezone.utc)
                updated_creatives.append(creative)
            
            yield updated_creatives
            
            savepoint.commit()
            self.db.commit()
            
            logger.info(f"✅ 批量状态转换成功: {len(transitions)} 个素材")
            
        except Exception as e:
            if savepoint:
                savepoint.rollback()
            self.db.rollback()
            
            logger.error(f"❌ 批量状态转换失败: {e}")
            raise

    def safe_dispatch_upload_task(self, creative_id: int, account_id: int, file_path: str, principal_name: str):
        """安全派发上传任务"""
        try:
            # 获取素材记录
            creative = self.db.query(LocalCreative).filter(
                LocalCreative.id == creative_id
            ).first()

            if not creative:
                logger.error(f"❌ 素材 {creative_id} 不存在")
                return False

            # 检查文件是否存在
            if not os.path.exists(file_path):
                logger.error(f"❌ 文件不存在: {file_path}")
                return False

            # 调用实际的上传逻辑 - 异步执行
            logger.info(f"📤 派发上传任务: 素材 {creative_id} -> 账户 {account_id}")
            logger.info(f"   文件路径: {file_path}")
            logger.info(f"   主体名称: {principal_name}")

            # 更新素材状态为上传中
            creative.status = 'uploading'
            creative.updated_at = datetime.now(timezone.utc)
            self.db.commit()

            logger.info(f"✅ 素材 {creative_id} 状态更新为 uploading")

            # 🚀 异步调用实际的上传任务
            try:
                from qianchuan_aw.workflows.tasks import upload_single_video

                # 异步派发上传任务
                task_result = upload_single_video.delay(
                    local_creative_id=creative_id,
                    account_id=account_id,
                    file_path=file_path,
                    principal_name=principal_name
                )

                logger.info(f"🚀 异步上传任务已派发: {task_result.id}")
                return True

            except Exception as task_error:
                logger.error(f"❌ 派发异步上传任务失败: {task_error}")
                # 回滚状态
                creative.status = 'pending_upload'
                creative.updated_at = datetime.now(timezone.utc)
                self.db.commit()
                return False

        except Exception as e:
            logger.error(f"❌ 派发上传任务失败 (Creative {creative_id}): {e}")
            self.db.rollback()
            return False
