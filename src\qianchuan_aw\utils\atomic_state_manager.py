#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
原子状态管理器
确保状态转换的原子性，防止状态不一致
"""

import os
from datetime import datetime, timezone
from contextlib import contextmanager
from qianchuan_aw.database.models import LocalCreative
from qianchuan_aw.utils.logger import logger


class AtomicStateManager:
    """原子状态管理器 - 确保状态转换的原子性"""
    
    def __init__(self, db_session):
        self.db = db_session
    
    @contextmanager
    def atomic_state_transition(self, creative_id: int, from_status: str, to_status: str):
        """原子状态转换上下文管理器"""
        creative = None
        try:
            # 开始事务
            creative = self.db.query(LocalCreative).filter(
                LocalCreative.id == creative_id,
                LocalCreative.status == from_status
            ).with_for_update().first()
            
            if not creative:
                raise ValueError(f"素材 {creative_id} 不存在或状态不匹配")
            
            # 更新状态
            creative.status = to_status
            creative.updated_at = datetime.now(timezone.utc)
            
            yield creative
            
            # 提交事务
            self.db.commit()
            logger.debug(f"状态转换成功: {creative_id} {from_status} → {to_status}")
            
        except Exception as e:
            # 回滚事务
            self.db.rollback()
            if creative:
                creative.status = from_status  # 恢复原状态
            logger.error(f"状态转换失败: {creative_id} {from_status} → {to_status}: {e}")
            raise

    def safe_dispatch_upload_task(self, creative_id: int, account_id: int, file_path: str, principal_name: str):
        """安全派发上传任务 - 修复版本，防止竞态条件"""
        try:
            # 🔒 首先验证素材存在且状态正确（带锁）
            creative = self.db.query(LocalCreative).filter(
                LocalCreative.id == creative_id
            ).with_for_update(nowait=True).first()  # 立即失败而不是等待

            if not creative:
                logger.warning(f"⚠️ 素材 {creative_id} 不存在，跳过处理")
                return False

            if creative.status != 'pending_upload':
                logger.warning(f"⚠️ 素材 {creative_id} 状态不是pending_upload (当前: {creative.status})，跳过处理")
                return False

            # 🔄 使用原子状态转换
            with self.atomic_state_transition(creative_id, 'pending_upload', 'processing') as processing_creative:
                # 状态转换成功后才派发任务
                from qianchuan_aw.workflows.tasks import upload_single_video
                upload_single_video.delay(creative_id, account_id, file_path, principal_name)
                logger.info(f"✅ 安全派发上传任务: {os.path.basename(file_path)}")
                return True

        except Exception as e:
            logger.error(f"❌ 安全派发失败 (Creative {creative_id}): {e}")
            return False
