#!/usr/bin/env python3
"""
千川项目实际工作流设计分析工具
基于正确的工作流理解重新诊断问题
"""

import sys
import os
from datetime import datetime
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text

class WorkflowDesignAnalyzer:
    """工作流设计分析器"""
    
    def __init__(self):
        logger.info("🔍 开始千川项目实际工作流设计分析")
        logger.info("=" * 60)
        logger.info("📋 实际工作流设计:")
        logger.info("  1. 扫描以'01'开头的目录中的素材文件")
        logger.info("  2. 将素材按9个一组进行分配")
        logger.info("  3. 每个账户分配9个素材后创建测试计划")
        logger.info("  4. creative_count: 9 是核心设计，不应修改")
    
    def restore_correct_config(self):
        """恢复正确的配置"""
        logger.info("\n🔧 恢复正确的配置设置")
        logger.info("=" * 60)
        
        import yaml
        
        config_file = "config/settings.yml"
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 检查当前配置
            current_test_count = config.get('plan_creation_defaults', {}).get('test_workflow', {}).get('creative_count', 1)
            current_interval = config.get('workflow', {}).get('plan_creation', {}).get('interval_seconds', 300)
            
            logger.info(f"📊 当前配置:")
            logger.info(f"  TEST账户creative_count: {current_test_count}")
            logger.info(f"  计划创建间隔: {current_interval}秒")
            
            # 恢复正确配置
            config_changed = False
            
            if current_test_count != 9:
                config['plan_creation_defaults']['test_workflow']['creative_count'] = 9
                logger.info(f"✅ 恢复TEST账户creative_count: {current_test_count} → 9")
                config_changed = True
            
            if current_interval != 60:
                config['workflow']['plan_creation']['interval_seconds'] = 60
                logger.info(f"✅ 恢复计划创建间隔: {current_interval} → 60秒")
                config_changed = True
            
            if config_changed:
                with open(config_file, 'w', encoding='utf-8') as f:
                    yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
                logger.info("✅ 配置已恢复为正确的设计值")
            else:
                logger.info("✅ 配置已经是正确的")
            
            return config_changed
            
        except Exception as e:
            logger.error(f"❌ 恢复配置失败: {e}")
            return False
    
    def analyze_material_scanning_logic(self):
        """分析素材扫描逻辑"""
        logger.info("\n🔍 分析素材扫描逻辑")
        logger.info("=" * 60)
        
        # 查找文件摄取相关代码
        try:
            # 检查是否有"01"开头目录的扫描逻辑
            ingest_files = [
                "src/qianchuan_aw/workflows/file_ingestion.py",
                "src/qianchuan_aw/workflows/scheduler.py",
                "src/qianchuan_aw/workflows/tasks.py"
            ]
            
            found_01_logic = False
            
            for file_path in ingest_files:
                if Path(file_path).exists():
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    if '01' in content or 'startswith' in content:
                        logger.info(f"📁 在 {file_path} 中发现可能的目录扫描逻辑")
                        
                        # 查找相关行
                        lines = content.split('\n')
                        for i, line in enumerate(lines):
                            if '01' in line or ('startswith' in line and 'dir' in line.lower()):
                                logger.info(f"  第{i+1}行: {line.strip()}")
                                found_01_logic = True
            
            if not found_01_logic:
                logger.warning("⚠️ 未找到明确的'01'开头目录扫描逻辑")
                logger.warning("  可能需要检查文件摄取的实际实现")
            
            return found_01_logic
            
        except Exception as e:
            logger.error(f"❌ 分析素材扫描逻辑失败: {e}")
            return False
    
    def analyze_material_grouping_logic(self):
        """分析素材分组逻辑"""
        logger.info("\n🔍 分析素材分组逻辑")
        logger.info("=" * 60)
        
        try:
            with database_session() as db:
                # 检查素材的实际分布情况
                material_distribution_query = text("""
                    SELECT 
                        aa.name as account_name,
                        aa.account_type,
                        COUNT(lc.id) as total_materials,
                        COUNT(CASE WHEN lc.status = 'approved' THEN 1 END) as approved_materials,
                        COUNT(CASE WHEN lc.status = 'uploaded_pending_plan' THEN 1 END) as pending_materials,
                        COUNT(CASE WHEN lc.status = 'creating_plan' THEN 1 END) as creating_materials
                    FROM ad_accounts aa
                    LEFT JOIN platform_creatives pc ON aa.id = pc.account_id
                    LEFT JOIN local_creatives lc ON pc.local_creative_id = lc.id
                    WHERE aa.status = 'active'
                    GROUP BY aa.name, aa.account_type
                    ORDER BY total_materials DESC
                """)
                
                results = db.execute(material_distribution_query).fetchall()
                
                logger.info("📊 账户素材分布分析:")
                total_approved = 0
                accounts_with_9plus = 0
                
                for row in results:
                    if row.total_materials > 0:
                        logger.info(f"  📋 {row.account_name} ({row.account_type}):")
                        logger.info(f"    总素材: {row.total_materials}")
                        logger.info(f"    已审核: {row.approved_materials}")
                        logger.info(f"    等待计划: {row.pending_materials}")
                        logger.info(f"    创建中: {row.creating_materials}")
                        
                        total_approved += row.approved_materials
                        
                        if row.approved_materials >= 9:
                            accounts_with_9plus += 1
                            logger.info(f"    ✅ 素材数量足够创建计划 (≥9)")
                        else:
                            logger.info(f"    ❌ 素材数量不足 (需要9个)")
                
                logger.info(f"\n📈 汇总统计:")
                logger.info(f"  总已审核素材: {total_approved}")
                logger.info(f"  可创建计划的账户: {accounts_with_9plus}")
                logger.info(f"  理论可创建计划数: {total_approved // 9}")
                
                # 分析为什么只有1个素材通过检查
                if total_approved >= 9 and accounts_with_9plus == 0:
                    logger.warning("🚨 发现问题: 有足够的素材但没有账户能创建计划")
                    logger.warning("  可能原因: 素材分配逻辑有问题")
                
                return {
                    'total_approved': total_approved,
                    'accounts_with_9plus': accounts_with_9plus,
                    'theoretical_plans': total_approved // 9
                }
                
        except Exception as e:
            logger.error(f"❌ 分析素材分组逻辑失败: {e}")
            return {}
    
    def analyze_plan_creation_workflow(self):
        """分析计划创建工作流"""
        logger.info("\n🔍 分析计划创建工作流")
        logger.info("=" * 60)
        
        # 查看scheduler.py中的实际逻辑
        scheduler_file = "src/qianchuan_aw/workflows/scheduler.py"
        
        try:
            with open(scheduler_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找关键的分组逻辑
            lines = content.split('\n')
            
            logger.info("📋 关键工作流逻辑分析:")
            
            # 查找素材筛选逻辑
            for i, line in enumerate(lines):
                if 'uploaded_pending_plan' in line and 'filter' in line:
                    logger.info(f"  素材筛选逻辑 (第{i+1}行):")
                    logger.info(f"    {line.strip()}")
                
                if 'final_grouped_by_account' in line:
                    logger.info(f"  账户分组逻辑 (第{i+1}行):")
                    logger.info(f"    {line.strip()}")
                
                if 'required_creative_count' in line:
                    logger.info(f"  素材数量要求 (第{i+1}行):")
                    logger.info(f"    {line.strip()}")
                    # 显示前后几行的上下文
                    for j in range(max(0, i-2), min(len(lines), i+3)):
                        if j != i:
                            logger.info(f"    {j+1}: {lines[j].strip()}")
            
            # 检查是否有按9个分组的逻辑
            if 'chunk_size' in content:
                logger.info("✅ 发现分组逻辑 (chunk_size)")
            else:
                logger.warning("⚠️ 未发现明确的分组逻辑")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 分析计划创建工作流失败: {e}")
            return False
    
    def check_material_status_flow(self):
        """检查素材状态流转"""
        logger.info("\n🔍 检查素材状态流转")
        logger.info("=" * 60)
        
        try:
            with database_session() as db:
                # 检查素材状态流转的详细情况
                status_flow_query = text("""
                    SELECT 
                        lc.status,
                        COUNT(*) as count,
                        MIN(lc.created_at) as earliest_created,
                        MAX(lc.updated_at) as latest_updated,
                        AVG(EXTRACT(EPOCH FROM (NOW() - lc.updated_at))/3600) as avg_hours_since_update
                    FROM local_creatives lc
                    GROUP BY lc.status
                    ORDER BY count DESC
                """)
                
                results = db.execute(status_flow_query).fetchall()
                
                logger.info("📊 素材状态流转详情:")
                
                problem_statuses = []
                
                for row in results:
                    logger.info(f"  {row.status}: {row.count}个")
                    logger.info(f"    最早创建: {row.earliest_created}")
                    logger.info(f"    最新更新: {row.latest_updated}")
                    logger.info(f"    平均停留: {row.avg_hours_since_update:.1f}小时")
                    
                    # 识别问题状态
                    if row.avg_hours_since_update > 24 and row.count > 100:
                        problem_statuses.append(row.status)
                        logger.warning(f"    ⚠️ 可能卡住的状态")
                
                # 特别检查approved状态的素材
                approved_detail_query = text("""
                    SELECT 
                        aa.name as account_name,
                        COUNT(lc.id) as approved_count
                    FROM local_creatives lc
                    JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    JOIN ad_accounts aa ON pc.account_id = aa.id
                    WHERE lc.status = 'approved'
                        AND aa.status = 'active'
                    GROUP BY aa.name
                    ORDER BY approved_count DESC
                    LIMIT 10
                """)
                
                approved_results = db.execute(approved_detail_query).fetchall()
                
                if approved_results:
                    logger.info("\n📋 approved状态素材的账户分布:")
                    for row in approved_results:
                        logger.info(f"  {row.account_name}: {row.approved_count}个")
                
                return {
                    'problem_statuses': problem_statuses,
                    'approved_distribution': [(r.account_name, r.approved_count) for r in approved_results]
                }
                
        except Exception as e:
            logger.error(f"❌ 检查素材状态流转失败: {e}")
            return {}
    
    def identify_root_cause(self):
        """识别根本原因"""
        logger.info("\n🎯 识别根本原因")
        logger.info("=" * 60)
        
        logger.info("基于分析结果，可能的根本原因:")
        
        # 基于之前的分析结果推断
        logger.info("1. 🔍 素材扫描问题:")
        logger.info("   - 可能没有正确扫描'01'开头的目录")
        logger.info("   - 或者扫描到的素材没有正确进入工作流")
        
        logger.info("2. 🔍 素材状态管理问题:")
        logger.info("   - 大量素材停留在approved状态")
        logger.info("   - 没有正确转换为uploaded_pending_plan状态")
        
        logger.info("3. 🔍 素材分配逻辑问题:")
        logger.info("   - 素材可能没有正确分配给账户")
        logger.info("   - 或者分配逻辑与9个一组的设计不符")
        
        logger.info("4. 🔍 工作流调度问题:")
        logger.info("   - 可能某个环节的任务没有正常执行")
        logger.info("   - 或者任务执行顺序有问题")
        
        logger.info("\n💡 建议的调查方向:")
        logger.info("1. 检查文件摄取任务是否正常运行")
        logger.info("2. 检查素材上传和审核流程")
        logger.info("3. 检查素材分配给账户的逻辑")
        logger.info("4. 检查计划创建的前置条件")
    
    def generate_action_plan(self):
        """生成行动计划"""
        logger.info("\n📋 生成行动计划")
        logger.info("=" * 60)
        
        logger.info("🎯 立即执行步骤:")
        logger.info("1. ✅ 恢复正确的配置 (creative_count: 9, interval: 60s)")
        logger.info("2. 🔍 深入检查文件摄取逻辑")
        logger.info("3. 🔍 检查素材状态流转机制")
        logger.info("4. 🔍 验证素材分配逻辑")
        logger.info("5. 🔧 修复发现的具体问题")
        
        logger.info("\n📊 需要验证的关键点:")
        logger.info("- 是否有'01'开头目录的素材被正确扫描？")
        logger.info("- approved状态的素材为什么不转为uploaded_pending_plan？")
        logger.info("- 素材是否按账户正确分组？")
        logger.info("- 每个账户是否能获得9个素材？")

def main():
    """主分析函数"""
    try:
        analyzer = WorkflowDesignAnalyzer()
        
        # 1. 恢复正确配置
        config_restored = analyzer.restore_correct_config()
        
        # 2. 分析素材扫描逻辑
        scanning_ok = analyzer.analyze_material_scanning_logic()
        
        # 3. 分析素材分组逻辑
        grouping_stats = analyzer.analyze_material_grouping_logic()
        
        # 4. 分析计划创建工作流
        workflow_ok = analyzer.analyze_plan_creation_workflow()
        
        # 5. 检查素材状态流转
        status_info = analyzer.check_material_status_flow()
        
        # 6. 识别根本原因
        analyzer.identify_root_cause()
        
        # 7. 生成行动计划
        analyzer.generate_action_plan()
        
        logger.info(f"\n🎉 工作流设计分析完成!")
        logger.info(f"配置已恢复: {'是' if config_restored else '否'}")
        logger.info(f"发现扫描逻辑: {'是' if scanning_ok else '否'}")
        logger.info(f"工作流分析: {'完成' if workflow_ok else '失败'}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 工作流设计分析失败: {e}")
        return False

if __name__ == "__main__":
    main()
