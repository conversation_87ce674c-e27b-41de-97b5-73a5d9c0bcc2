# -*- coding: utf-8 -*-
"""
[V62.0] 简单可靠的智投星服务

基于验证过的Playwright代码模式，不使用复杂的适配器。
直接使用原生Playwright API，确保稳定性。
"""
import os
import time
import json
from typing import Optional, Dict, Any, List, Tuple
from contextlib import contextmanager
from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.cookie_utils import clean_cookies_for_playwright

class SimpleCopilotSession:
    """简单可靠的智投星会话"""
    
    def __init__(self, principal_name: str, account_id_qc: int, app_settings: Dict[str, Any]):
        self.principal_name = principal_name
        self.account_id_qc = account_id_qc
        self.app_settings = app_settings
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None
    
    def __enter__(self):
        """进入上下文管理器"""
        logger.info(f"--- [简单智投星会话] 正在为账户 {self.account_id_qc} 创建会话... ---")

        try:
            # 🔧 集成浏览器实例管理器
            try:
                from qianchuan_aw.ai_tools.maintenance.ai_tool_20250805_browser_instance_manager import get_browser_manager
                browser_manager = get_browser_manager()
                browser_manager.cleanup_excess_instances()  # 创建前先清理
            except ImportError:
                logger.warning("浏览器实例管理器不可用，继续使用原有逻辑")

            # 加载cookies
            cookies = self._load_cookies()
            if not cookies:
                raise IOError(f"无法加载主体 '{self.principal_name}' 的cookies")

            # 启动浏览器
            from playwright.sync_api import sync_playwright

            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.launch(
                headless=self.app_settings.get('browser', {}).get('headless', True),
                args=['--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu', '--memory-pressure-off']
            )
            
            self.context = self.browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            )
            self.context.add_cookies(cookies)
            
            self.page = self.context.new_page()
            self.page.set_default_timeout(30000)
            
            # 导航到千川页面
            url = f"https://qianchuan.jinritemai.com/promotion-v2/standard?aavid={self.account_id_qc}"
            logger.info(f"导航到: {url}")
            self.page.goto(url, wait_until="domcontentloaded")
            time.sleep(5)  # 增加等待时间，确保页面完全加载

            logger.info(f"页面加载成功: {self.page.title()}")

            # 清理弹窗
            logger.info("--- [简单智投星会话] 步骤1: 清理引导弹窗...")
            try:
                popup = self.page.get_by_text("我知道了").nth(1)
                if popup.is_visible(timeout=5000):
                    popup.click()
                    logger.info("✅ 弹窗清理成功")
                    time.sleep(2)
            except:
                logger.info("未发现弹窗")

            # 打开智投星对话框
            logger.info("--- [简单智投星会话] 步骤2: 打开客服对话框...")

            # 尝试多种方式查找智投星图标
            copilot_selectors = [
                ".copilot-icon",
                "#copilot-sdk-ark",
                "#copilot-sdk-ark div"
            ]

            copilot_clicked = False
            for selector in copilot_selectors:
                try:
                    copilot_icon = self.page.locator(selector).first
                    # 等待元素出现并可见
                    copilot_icon.wait_for(state="visible", timeout=15000)
                    copilot_icon.click()
                    time.sleep(3)
                    logger.success(f"✅ 智投星对话框已打开 (使用选择器: {selector})")
                    copilot_clicked = True
                    break
                except Exception as e:
                    logger.debug(f"选择器 {selector} 失败: {e}")
                    continue

            if not copilot_clicked:
                raise IOError("智投星图标不可见或无法点击")
            
            logger.success(f"--- [简单智投星会话] 会话创建成功 ---")
            return self
            
        except Exception as e:
            logger.error(f"简单智投星会话初始化失败: {e}")
            self._cleanup()
            raise
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文管理器"""
        self._cleanup()
        logger.info("--- [简单智投星会话] 会话已关闭 ---")
    
    def _cleanup(self):
        """清理资源"""
        try:
            if self.browser:
                self.browser.close()
                logger.debug("浏览器实例已关闭")
            if self.playwright:
                self.playwright.stop()
        except:
            pass
    
    def _load_cookies(self) -> Optional[list]:
        """加载cookies"""
        try:
            # 查找cookies文件
            current_file_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.abspath(os.path.join(current_file_dir, '..', '..', '..'))
            
            cookies_paths = [
                os.path.join(project_root, 'config', 'browser_cookies.json'),
                os.path.join(project_root, '.auth', f'{self.principal_name}.auth.json')
            ]
            
            for cookies_path in cookies_paths:
                if os.path.exists(cookies_path):
                    with open(cookies_path, 'r', encoding='utf-8') as f:
                        cookies_data = json.load(f)
                    
                    if self.principal_name in str(cookies_path):
                        # .auth格式
                        cookies_array = cookies_data.get('cookies', [])
                    elif isinstance(cookies_data, dict) and self.principal_name in cookies_data:
                        # 分组格式
                        principal_data = cookies_data[self.principal_name]
                        cookies_array = principal_data.get('cookies', [])
                    else:
                        continue
                    
                    if cookies_array:
                        cleaned_cookies = clean_cookies_for_playwright(cookies_array)
                        logger.debug(f"成功加载 {len(cleaned_cookies)} 个cookies")
                        return cleaned_cookies
            
            return None
            
        except Exception as e:
            logger.error(f"加载cookies失败: {e}")
            return None
    
    def send_message(self, message: str) -> Optional[str]:
        """发送消息并获取回复 - 增强版智能等待"""
        try:
            logger.info(f"发送消息: {message}")

            # 智能等待对话框完全加载
            self._wait_for_chat_interface_ready()

            # 查找输入框
            chat_input = None
            input_selectors = [
                'textarea[placeholder*="请描述"]',
                'input[placeholder*="请描述"]',
                'textarea',
                '.copilot-input textarea',
                '[role="textbox"]'
            ]

            for selector in input_selectors:
                try:
                    input_element = self.page.locator(selector).first
                    if input_element.is_visible(timeout=3000):
                        chat_input = input_element
                        break
                except:
                    continue
            
            if not chat_input:
                logger.error("未找到聊天输入框")
                return None
            
            # 发送消息
            chat_input.fill(message)
            chat_input.press("Enter")
            
            # 等待回复
            time.sleep(10)
            
            # 获取最新回复
            reply_cards = self.page.locator(".role-robot").all()
            if reply_cards:
                latest_reply = reply_cards[-1]
                reply_text = latest_reply.inner_text()
                logger.info(f"收到回复: {reply_text[:200]}...")
                return reply_text
            else:
                logger.warning("未收到回复")
                return None
                
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            return None
    
    def query_plan(self, plan_id: int) -> Optional[str]:
        """查询计划状态并返回解析后的状态码"""
        try:
            query_message = f"查询申诉进度{plan_id}"
            reply_text = self.send_message(query_message)

            if not reply_text:
                return None

            # 解析回复文本，返回状态码
            return self._parse_appeal_status(reply_text, plan_id)

        except Exception as e:
            logger.error(f"查询计划状态失败: {e}")
            return None

    def _parse_appeal_status(self, reply_text: str, plan_id: int) -> str:
        """解析申诉状态回复"""
        try:
            # 导入状态枚举以确保返回值一致
            from qianchuan_aw.utils.appeal_status_definitions import AppealStatus

            logger.info(f"解析计划 {plan_id} 的申诉状态: {reply_text[:200]}...")

            # 1. 申诉失败（申诉流程结束）
            if "申诉失败" in reply_text:
                logger.info(f"计划 {plan_id} 申诉失败")
                return AppealStatus.APPEAL_FAILED.value

            # 2. 申诉成功（申诉流程结束）
            if "申诉成功" in reply_text or "申诉通过" in reply_text:
                logger.info(f"计划 {plan_id} 申诉成功")
                return AppealStatus.APPEAL_SUCCESS.value

            # 3. 申诉中（继续等待）
            if "申诉中" in reply_text:
                logger.info(f"计划 {plan_id} 申诉中")
                return AppealStatus.APPEAL_MONITORING.value

            # 4. 申诉已提交（刚提交成功）
            if "已为你成功提交申诉" in reply_text or "已为你提交" in reply_text:
                logger.info(f"计划 {plan_id} 申诉已提交")
                return AppealStatus.APPEAL_SUBMITTED.value

            # 5. 检查时间信息判断状态
            if "创建时间:" in reply_text:
                if "完成时间:" in reply_text and "完成时间：-" not in reply_text:
                    # 有完成时间，申诉已结束
                    if "申诉失败" in reply_text or "失败" in reply_text:
                        return AppealStatus.APPEAL_FAILED.value
                    elif "申诉成功" in reply_text or "通过" in reply_text:
                        return AppealStatus.APPEAL_SUCCESS.value
                    else:
                        # 有完成时间但状态不明确，可能是失败
                        logger.warning(f"计划 {plan_id} 申诉已完成但状态不明确")
                        return AppealStatus.APPEAL_FAILED.value
                else:
                    # 只有创建时间，没有完成时间，还在处理中
                    logger.info(f"计划 {plan_id} 只有创建时间，判断为申诉中")
                    return AppealStatus.APPEAL_MONITORING.value

            # 6. 无申诉记录
            if "暂无申诉记录" in reply_text:
                logger.info(f"计划 {plan_id} 无申诉记录")
                return "NO_RECORD"

            # 7. 已是非拒绝状态
            if "非拒绝状态" in reply_text:
                logger.info(f"计划 {plan_id} 已是非拒绝状态")
                return "ALREADY_APPROVED"

            # 8. 系统错误
            if "系统异常" in reply_text or "ID格式可能存在错误" in reply_text:
                logger.error(f"计划 {plan_id} 查询出现系统错误")
                return "SYSTEM_ERROR"

            # 9. 临时回复（需要重试）
            if any(temp in reply_text for temp in ["正在理解", "正在思考", "正在处理", "请稍等"]):
                logger.info(f"计划 {plan_id} 收到临时回复，需要重试")
                return "THINKING"

            # 10. 默认情况
            logger.warning(f"计划 {plan_id} 未知回复类型: {reply_text[:100]}...")
            return "UNKNOWN"

        except Exception as e:
            logger.error(f"解析申诉状态失败: {e}")
            return "UNKNOWN"
    
    def submit_appeal(self, plan_id: int) -> Optional[str]:
        """提交申诉"""
        try:
            appeal_message = f"自助申诉表单{plan_id}"
            return self.send_message(appeal_message)
        except Exception as e:
            logger.error(f"提交申诉失败: {e}")
            return None
    
    def batch_query_plans(self, plan_ids: List[int]) -> Dict[int, Optional[str]]:
        """批量查询计划状态"""
        results = {}

        for plan_id in plan_ids:
            try:
                logger.info(f"查询计划 {plan_id} 状态...")
                result = self.query_plan(plan_id)
                results[plan_id] = result

                # 避免请求过快
                time.sleep(2)

            except Exception as e:
                logger.error(f"查询计划 {plan_id} 失败: {e}")
                results[plan_id] = None

        return results

    def appeal_via_text_command(self, plan_id: int) -> Tuple[bool, str]:
        """通过文本指令提交申诉 - 兼容性方法"""
        try:
            logger.info(f"通过文本指令提交申诉，计划ID: {plan_id}")
            result = self.submit_appeal(plan_id)

            if result:
                # 分析回复内容判断是否成功
                success_keywords = [
                    "已为你成功提交申诉",
                    "已为你提交",
                    "申诉提交成功",
                    "申诉正在处理中",  # 修复：这也是成功状态
                    "你的申诉正在处理中",  # 修复：这也是成功状态
                    "申诉审出时效"
                ]

                if any(keyword in result for keyword in success_keywords):
                    return True, result
                elif "授权书" in result and "提交申诉" in result:
                    # 遇到授权书情况，直接点击提交申诉
                    logger.info(f"检测到授权书申诉界面，直接提交申诉，计划ID: {plan_id}")
                    return self._handle_authorization_appeal()
                elif any(keyword in result for keyword in [
                    "自助申诉表单功能",
                    "建议你输入关键词【自助申诉表单】",
                    "优先通过表单提交申诉"
                ]):
                    # 收到自助申诉表单提示，这是成功的第一步，继续完成表单流程
                    logger.info(f"✅ 计划 {plan_id} 收到自助申诉表单提示，开始表单申诉流程...")
                    return self._handle_self_service_appeal_form(plan_id, result)
                elif any(keyword in result for keyword in [
                    "暂无申诉记录（近半年数据），建议你换个ID查询",
                    "暂无广告审核建议",
                    "暂不支持提交非本账号下的ID申诉",
                    "请切换ID所在的账号重新进行查询",
                    "申诉提交失败",
                    "系统异常",
                    "网络错误",
                    "请稍后重试"
                ]):
                    # 其他类型的失败，启动浏览器自动化备用方案
                    logger.warning(f"⚠️ 计划 {plan_id} 文字命令失效，启动浏览器自动化备用方案...")
                    return self._fallback_to_browser_automation(plan_id, result)
                else:
                    # 其他未知回复，也尝试浏览器自动化备用方案
                    logger.warning(f"⚠️ 计划 {plan_id} 收到未知回复，启动浏览器自动化备用方案...")
                    return self._fallback_to_browser_automation(plan_id, result)
            else:
                return False, "未收到回复"

        except Exception as e:
            logger.error(f"文本指令提审失败: {e}")
            return False, str(e)

    def _fallback_to_browser_automation(self, plan_id: int, original_result: str) -> Tuple[bool, str]:
        """智能降级到自助申诉表单备用方案"""
        try:
            logger.info(f"🔄 启动自助申诉表单备用方案，计划ID: {plan_id}")

            # 备用方案：直接使用自助申诉表单流程
            # 1. 发送"自助申诉表单"指令
            logger.info("📋 步骤1: 发送自助申诉表单指令...")
            form_reply = self.send_message("自助申诉表单")

            if not form_reply:
                return False, f"文字版失败: {original_result[:50]}..., 备用方案: 无法发送自助申诉表单指令"

            logger.info(f"收到表单回复: {form_reply[:100]}...")

            # 2. 执行自助申诉表单流程
            logger.info("📋 步骤2: 执行自助申诉表单流程...")
            success, message = self._handle_self_service_appeal_form(plan_id, form_reply)

            if success:
                logger.success(f"✅ 自助申诉表单备用方案成功，计划ID: {plan_id}")
                return True, f"备用方案成功: {message}"
            else:
                logger.error(f"❌ 自助申诉表单备用方案也失败，计划ID: {plan_id}")
                return False, f"文字版失败: {original_result[:50]}..., 备用方案失败: {message}"

        except Exception as e:
            logger.error(f"自助申诉表单备用方案异常: {e}")
            return False, f"文字版失败: {original_result[:50]}..., 备用方案异常: {e}"

    def _wait_for_chat_interface_ready(self, max_wait_seconds: int = 15) -> bool:
        """智能等待聊天界面完全加载"""
        try:
            logger.info("🔄 智能等待聊天界面加载...")

            # 等待策略：递增等待时间
            wait_intervals = [2, 3, 5, 5]  # 总计15秒

            for i, wait_time in enumerate(wait_intervals, 1):
                logger.info(f"⏳ 第{i}次等待 {wait_time}秒...")
                time.sleep(wait_time)

                # 检查输入框是否可用
                input_selectors = [
                    'textarea[placeholder*="请描述"]',
                    'input[placeholder*="请描述"]',
                    'textarea',
                    '.copilot-input textarea',
                    '[role="textbox"]'
                ]

                for selector in input_selectors:
                    try:
                        elements = self.page.locator(selector).all()
                        for element in elements:
                            if element.is_visible(timeout=1000) and element.is_enabled():
                                logger.success(f"✅ 聊天界面已就绪 (使用选择器: {selector})")
                                return True
                    except Exception:
                        continue

                logger.info(f"第{i}次检查未就绪，继续等待...")

            logger.warning("⚠️ 聊天界面等待超时，但继续尝试发送消息")
            return False

        except Exception as e:
            logger.error(f"智能等待异常: {e}")
            return False

    def _handle_self_service_appeal_form(self, plan_id: int, initial_result: str) -> Tuple[bool, str]:
        """处理自助申诉表单流程 - 正确的备用方案"""
        try:
            logger.info(f"🔄 开始自助申诉表单流程，计划ID: {plan_id}")

            # 步骤1: 发送"自助申诉表单"关键词（这是关键步骤！）
            logger.info("📋 步骤1: 发送'自助申诉表单'关键词...")
            form_reply = self.send_message("自助申诉表单")

            if not form_reply:
                return False, "无法发送自助申诉表单关键词"

            logger.info(f"收到表单回复: {form_reply[:100]}...")

            # 步骤2: 等待表单界面加载
            logger.info("📋 步骤2: 等待表单界面加载...")
            time.sleep(5)  # 给表单更多时间加载

            # 步骤3: 点击"计划/商品申诉"（使用历史代码中验证过的方法）
            logger.info("📋 步骤3: 点击'计划/商品申诉'...")
            button_clicked = False
            try:
                # 使用历史代码中验证过的get_by_text方法
                plan_appeal_element = self.page.get_by_text("计划/商品申诉").first
                if plan_appeal_element.is_visible(timeout=10000):
                    plan_appeal_element.click(timeout=10000)
                    logger.success("✅ 成功点击'计划/商品申诉'")
                    button_clicked = True
                    time.sleep(3)
                else:
                    logger.warning("⚠️ '计划/商品申诉'按钮不可见")
            except Exception as e:
                logger.warning(f"⚠️ 点击'计划/商品申诉'失败: {e}")

            if not button_clicked:
                logger.error("❌ 无法点击'计划/商品申诉'按钮，备用方案失败")
                return False, "无法点击'计划/商品申诉'按钮"

            # 步骤4: 等待申诉表单加载
            logger.info("📋 步骤4: 等待申诉表单加载...")
            time.sleep(3)

            # 步骤5: 选择问题类型（使用历史代码中验证过的方法）
            logger.info("📋 步骤5: 选择问题类型...")
            try:
                # 使用历史代码中验证过的方法：先点击下拉框
                dropdown_element = self.page.get_by_placeholder("请选择", exact=True).last
                dropdown_element.click(timeout=5000)
                time.sleep(1)

                # 然后选择具体选项
                option_element = self.page.get_by_text("计划审核不通过/结果申诉").last
                option_element.click(timeout=10000)
                logger.success("✅ 成功选择问题类型: 计划审核不通过/结果申诉")
                time.sleep(2)
                type_selected = True
            except Exception as e:
                logger.error(f"❌ 选择问题类型失败: {e}")
                return False, f"选择问题类型失败: {e}"

            # 步骤6: 输入计划ID（使用历史代码中验证过的方法）
            logger.info(f"📋 步骤6: 输入计划ID {plan_id}...")
            try:
                # 使用历史代码中验证过的方法
                plan_id_element = self.page.get_by_placeholder("请输入计划ID，支持托管计划").last
                plan_id_element.click()
                plan_id_element.fill(str(plan_id))
                logger.success(f"✅ 成功输入计划ID: {plan_id}")
                time.sleep(1)
                id_entered = True
            except Exception as e:
                logger.error(f"❌ 输入计划ID失败: {e}")
                return False, f"输入计划ID失败: {e}"

            # 步骤7: 点击提交（使用历史代码中验证过的方法）
            logger.info("📋 步骤7: 点击提交...")
            try:
                # 使用历史代码中验证过的方法
                submit_element = self.page.get_by_role("button", name="提交").last
                submit_element.click(timeout=10000)
                logger.success("✅ 成功点击提交按钮")
                time.sleep(5)  # 等待提交处理
                submitted = True
            except Exception as e:
                logger.error(f"❌ 点击提交按钮失败: {e}")
                return False, f"点击提交按钮失败: {e}"

            # 步骤8: 等待提交结果
            logger.info("📋 步骤8: 等待提交结果...")
            time.sleep(5)

            # 检查提交结果
            if submitted and id_entered:
                logger.success(f"✅ 自助申诉表单提交成功，计划ID: {plan_id}")
                return True, "自助申诉表单提交成功"
            else:
                logger.warning(f"⚠️ 自助申诉表单提交可能不完整，计划ID: {plan_id}")
                return True, "自助申诉表单已尝试提交（部分步骤可能未完成）"

        except Exception as e:
            logger.error(f"自助申诉表单流程异常: {e}")
            return False, f"自助申诉表单流程异常: {e}"

    def _handle_authorization_appeal(self) -> Tuple[bool, str]:
        """处理授权书申诉：不管内容，直接点击提交申诉"""
        try:
            logger.info("开始处理授权书申诉，直接点击提交申诉按钮...")

            # 查找提交申诉按钮
            submit_selectors = [
                "button:has-text('提交申诉')",
                "button:has-text('提交')",
                ".submit-btn",
                ".appeal-submit",
                "[type='submit']"
            ]

            for selector in submit_selectors:
                try:
                    buttons = self.page.locator(selector).all()
                    for button in buttons:
                        if button.is_visible(timeout=3000):
                            button_text = button.inner_text()
                            if "申诉" in button_text or "提交" in button_text:
                                logger.info(f"找到提交按钮: {button_text}")
                                button.click()
                                time.sleep(3)

                                # 等待提交结果
                                time.sleep(5)

                                # 获取最新回复
                                reply_cards = self.page.locator(".role-robot").all()
                                if reply_cards:
                                    latest_reply = reply_cards[-1]
                                    reply_text = latest_reply.inner_text()

                                    if "已为你成功提交申诉" in reply_text or "申诉提交成功" in reply_text:
                                        logger.success("授权书申诉提交成功")
                                        return True, reply_text
                                    else:
                                        logger.info(f"授权书申诉提交后回复: {reply_text[:100]}...")
                                        return True, reply_text  # 认为提交成功
                                else:
                                    logger.info("授权书申诉提交后未收到回复，认为提交成功")
                                    return True, "授权书申诉已提交"

                except Exception as e:
                    logger.debug(f"尝试提交按钮 {selector} 失败: {e}")
                    continue

            logger.warning("未找到提交申诉按钮")
            return False, "未找到提交申诉按钮"

        except Exception as e:
            logger.error(f"处理授权书申诉失败: {e}")
            return False, str(e)

# 兼容性接口
@contextmanager
def simple_copilot_session(principal_name: str, account_id_qc: int, app_settings: Dict[str, Any]):
    """简单智投星会话上下文管理器"""
    session = SimpleCopilotSession(principal_name, account_id_qc, app_settings)
    try:
        yield session.__enter__()
    finally:
        session.__exit__(None, None, None)

# 为了兼容现有代码，提供CopilotSession别名
CopilotSession = SimpleCopilotSession
