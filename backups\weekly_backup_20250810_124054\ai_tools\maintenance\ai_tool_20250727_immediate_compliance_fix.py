#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 立即修复素材重复使用违规问题
清理条件: 系统完全合规后可删除

千川自动化立即合规性修复工具
========================

紧急修复：素材重复使用违反铁律问题
- 发现155个素材违规，445个重复计划
- 立即删除重复计划，保留最早计划
- 更新素材状态，确保合规性
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import LocalCreative, Campaign
from sqlalchemy import text


def fix_specific_material_violation():
    """修复特定素材违规（7.26-王梦珂-23.mp4）"""
    print("🚨 修复特定素材违规：7.26-王梦珂-23.mp4")
    
    with database_session() as db:
        # 查找该素材的所有计划
        query = text("""
            SELECT DISTINCT c.id, c.campaign_id_qc, c.created_at, c.status
            FROM campaigns c
            JOIN campaign_platform_creative_association cpca ON c.id = cpca.campaign_id
            JOIN platform_creatives pc ON cpca.platform_creative_id = pc.id
            JOIN local_creatives lc ON pc.local_creative_id = lc.id
            WHERE lc.filename = '7.26-王梦珂-23.mp4'
            ORDER BY c.created_at ASC
        """)
        
        result = db.execute(query)
        plans = result.fetchall()
        
        print(f"找到 {len(plans)} 个计划:")
        for i, plan in enumerate(plans, 1):
            print(f"  {i}. ID: {plan.id}, QC_ID: {plan.campaign_id_qc}, 状态: {plan.status}, 创建时间: {plan.created_at}")
        
        if len(plans) > 1:
            # 保留最早的计划，删除其他计划
            keep_plan = plans[0]
            delete_plans = plans[1:]
            
            print(f"\n保留最早计划: {keep_plan.campaign_id_qc}")
            print(f"删除重复计划: {len(delete_plans)} 个")
            
            for plan in delete_plans:
                print(f"  删除: {plan.campaign_id_qc}")
                # 实际删除
                db.execute(text("DELETE FROM campaigns WHERE id = :plan_id"), {"plan_id": plan.id})
            
            # 更新素材状态
            db.execute(text("""
                UPDATE local_creatives 
                SET status = 'already_tested' 
                WHERE filename = '7.26-王梦珂-23.mp4'
            """))
            
            db.commit()
            print("✅ 修复完成")
        else:
            print("✅ 该素材无违规")


def fix_all_violations():
    """修复所有违规"""
    print("\n🚨 修复所有素材违规")
    
    with database_session() as db:
        # 查找所有违规素材
        violation_query = text("""
            SELECT 
                lc.id as material_id,
                lc.filename,
                lc.file_hash,
                COUNT(DISTINCT c.id) as plan_count,
                MIN(c.id) as keep_plan_id,
                STRING_AGG(DISTINCT c.id::text, ', ') as all_plan_ids
            FROM local_creatives lc
            JOIN platform_creatives pc ON lc.id = pc.local_creative_id
            JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
            JOIN campaigns c ON cpca.campaign_id = c.id
            JOIN ad_accounts aa ON c.account_id = aa.id
            WHERE aa.account_type = 'TEST'
            GROUP BY lc.id, lc.filename, lc.file_hash
            HAVING COUNT(DISTINCT c.id) > 1
            ORDER BY plan_count DESC
        """)
        
        result = db.execute(violation_query)
        violations = result.fetchall()
        
        print(f"发现 {len(violations)} 个违规素材")
        
        total_deleted = 0
        total_updated = 0
        
        for violation in violations:
            material_id = violation.material_id
            filename = violation.filename
            plan_count = violation.plan_count
            keep_plan_id = violation.keep_plan_id
            all_plan_ids = [int(x) for x in violation.all_plan_ids.split(', ')]
            delete_plan_ids = [pid for pid in all_plan_ids if pid != keep_plan_id]
            
            print(f"\n处理素材: {filename}")
            print(f"  计划总数: {plan_count}")
            print(f"  保留计划ID: {keep_plan_id}")
            print(f"  删除计划数: {len(delete_plan_ids)}")
            
            # 删除重复计划
            for plan_id in delete_plan_ids:
                db.execute(text("DELETE FROM campaigns WHERE id = :plan_id"), {"plan_id": plan_id})
                total_deleted += 1
            
            # 更新素材状态
            db.execute(text("""
                UPDATE local_creatives 
                SET status = 'already_tested' 
                WHERE id = :material_id
            """), {"material_id": material_id})
            total_updated += 1
        
        db.commit()
        
        print(f"\n✅ 修复完成:")
        print(f"  删除重复计划: {total_deleted} 个")
        print(f"  更新素材状态: {total_updated} 个")


def verify_compliance():
    """验证合规性"""
    print("\n🔍 验证系统合规性")
    
    with database_session() as db:
        # 检查是否还有违规
        check_query = text("""
            SELECT COUNT(*) as violation_count
            FROM (
                SELECT lc.id
                FROM local_creatives lc
                JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                JOIN campaigns c ON cpca.campaign_id = c.id
                JOIN ad_accounts aa ON c.account_id = aa.id
                WHERE aa.account_type = 'TEST'
                GROUP BY lc.id
                HAVING COUNT(DISTINCT c.id) > 1
            ) violations
        """)
        
        result = db.execute(check_query)
        violation_count = result.fetchone().violation_count
        
        if violation_count == 0:
            print("✅ 系统已完全合规！")
            print("✅ 所有素材都遵守唯一性测试铁律")
        else:
            print(f"⚠️ 仍有 {violation_count} 个素材违规")
            print("⚠️ 需要进一步检查和修复")
        
        return violation_count == 0


def main():
    """主函数"""
    print("🚨 千川自动化立即合规性修复")
    print("=" * 50)
    print("修复素材重复使用违反铁律问题")
    print("=" * 50)
    
    try:
        # 1. 修复特定素材
        fix_specific_material_violation()
        
        # 2. 修复所有违规
        fix_all_violations()
        
        # 3. 验证合规性
        is_compliant = verify_compliance()
        
        if is_compliant:
            print("\n🎉 系统合规性修复成功！")
            print("🎉 所有素材现在都遵守唯一性测试铁律")
        else:
            print("\n⚠️ 修复未完全成功，需要进一步检查")
        
        # 4. 生成修复报告
        report_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        report = f"""
千川自动化合规性修复报告
=====================
修复时间: {report_time}
修复状态: {'完全合规' if is_compliant else '部分修复'}

修复措施:
1. 删除重复测试计划
2. 保留最早创建的计划
3. 更新素材状态为已测试
4. 验证系统合规性

重要提醒:
- 素材唯一性测试铁律已恢复
- 建议加强计划创建前检查
- 定期进行合规性审计
"""
        
        # 保存报告
        os.makedirs('ai_reports', exist_ok=True)
        report_file = f"ai_reports/compliance_fix_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\n📄 修复报告已保存: {report_file}")
        
        return 0 if is_compliant else 1
        
    except Exception as e:
        print(f"❌ 修复过程中发生错误: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
