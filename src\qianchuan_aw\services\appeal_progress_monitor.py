#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提审进度监控器

专注于监控提审进度，检测卡住状态，自动恢复异常情况。
与现有的CopilotSession集成，提供统一的进度查询和状态同步。

核心功能：
- 定期检查提审进度
- 检测卡住状态并自动恢复
- 同步提审结果
- 超时处理和重试机制
- 与现有查询服务集成
"""

import time
import json
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from threading import Thread, Event
from dataclasses import dataclass

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.services.appeal_state_tracker import AppealStateTracker
from qianchuan_aw.utils.monitoring_mcp import create_mcp_monitoring_collector


@dataclass
class AppealMonitorTask:
    """提审监控任务"""
    campaign_id_qc: str
    account_id_qc: str
    principal_name: str
    appeal_submitted_at: datetime
    last_check_at: Optional[datetime] = None
    check_count: int = 0
    status: str = 'monitoring'  # 'monitoring', 'completed', 'failed', 'timeout'
    
    def should_check(self, check_interval: int = 300) -> bool:
        """判断是否应该检查"""
        if not self.last_check_at:
            return True
        return (datetime.utcnow() - self.last_check_at).total_seconds() >= check_interval


class AppealProgressMonitor:
    """
    提审进度监控器
    
    定期检查提审进度，处理卡住状态，同步最终结果
    """
    
    def __init__(self, execute_sql_func: Callable[[str], str], 
                 check_interval: int = 300,  # 5分钟检查一次
                 max_monitor_hours: int = 24):  # 最多监控24小时
        """
        初始化提审进度监控器
        
        Args:
            execute_sql_func: MCP的execute_sql函数
            check_interval: 检查间隔（秒）
            max_monitor_hours: 最大监控时长（小时）
        """
        self.execute_sql = execute_sql_func
        self.check_interval = check_interval
        self.max_monitor_hours = max_monitor_hours
        
        # 组件
        self.state_tracker = AppealStateTracker(execute_sql_func)
        self.monitoring_collector = create_mcp_monitoring_collector(execute_sql_func)
        
        # 运行状态
        self.running = False
        self.stop_event = Event()
        self.monitor_thread = None
        
        # 监控任务队列
        self.monitor_tasks: List[AppealMonitorTask] = []
        
        # 统计信息
        self.stats = {
            'total_monitored': 0,
            'completed_appeals': 0,
            'failed_appeals': 0,
            'timeout_appeals': 0,
            'last_check_time': None
        }
    
    def start(self):
        """启动进度监控器"""
        if self.running:
            logger.warning("提审进度监控器已在运行")
            return
        
        logger.info("🚀 启动提审进度监控器...")
        self.running = True
        self.stop_event.clear()
        
        # 启动监控线程
        self.monitor_thread = Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        logger.info("✅ 提审进度监控器启动成功")
    
    def stop(self):
        """停止进度监控器"""
        if not self.running:
            return
        
        logger.info("🛑 停止提审进度监控器...")
        self.running = False
        self.stop_event.set()
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=10)
        
        logger.info("✅ 提审进度监控器已停止")
    
    def add_monitoring_task(self, campaign_id_qc: str, account_id_qc: str, 
                           principal_name: str, appeal_submitted_at: datetime):
        """
        添加监控任务
        
        Args:
            campaign_id_qc: 计划ID
            account_id_qc: 账户ID
            principal_name: 主体名称
            appeal_submitted_at: 提审提交时间
        """
        # 检查是否已存在
        for task in self.monitor_tasks:
            if task.campaign_id_qc == campaign_id_qc:
                logger.debug(f"计划 {campaign_id_qc} 已在监控队列中")
                return
        
        task = AppealMonitorTask(
            campaign_id_qc=campaign_id_qc,
            account_id_qc=account_id_qc,
            principal_name=principal_name,
            appeal_submitted_at=appeal_submitted_at
        )
        
        self.monitor_tasks.append(task)
        self.stats['total_monitored'] += 1
        
        logger.info(f"添加提审监控任务: {campaign_id_qc}")
    
    def _monitor_loop(self):
        """监控循环"""
        logger.info("📊 提审进度监控线程启动")
        
        while self.running and not self.stop_event.is_set():
            try:
                # 加载待监控的任务
                self._load_monitoring_tasks()
                
                # 检查监控任务
                self._check_monitoring_tasks()
                
                # 清理完成的任务
                self._cleanup_completed_tasks()
                
                # 处理超时任务
                self._handle_timeout_tasks()
                
                self.stats['last_check_time'] = datetime.utcnow()
                
                # 等待下次检查
                self.stop_event.wait(self.check_interval)
                
            except Exception as e:
                logger.error(f"提审进度监控循环异常: {e}", exc_info=True)
                self.stop_event.wait(30)  # 异常后等待30秒
        
        logger.info("📊 提审进度监控线程结束")
    
    def _load_monitoring_tasks(self):
        """加载待监控的任务"""
        try:
            # 查询需要监控的计划
            result = self.execute_sql("""
                SELECT campaign_id_qc, account_id_qc, appeal_submitted_at,
                       p.name as principal_name
                FROM campaigns c
                JOIN ad_accounts a ON c.account_id = a.id
                JOIN principals p ON a.principal_id = p.id
                WHERE c.appeal_status IN ('appeal_submitted', 'appeal_monitoring')
                  AND c.appeal_submitted_at IS NOT NULL
                  AND c.appeal_submitted_at >= NOW() - INTERVAL '24 hours'
            """)
            
            if not result or not result.strip():
                return
            
            lines = result.strip().split('\n')
            for line in lines:
                if line.strip():
                    data = json.loads(line)
                    
                    # 检查是否已在监控队列中
                    campaign_id_qc = data['campaign_id_qc']
                    if not any(task.campaign_id_qc == campaign_id_qc for task in self.monitor_tasks):
                        self.add_monitoring_task(
                            campaign_id_qc=campaign_id_qc,
                            account_id_qc=data['account_id_qc'],
                            principal_name=data['principal_name'],
                            appeal_submitted_at=datetime.fromisoformat(data['appeal_submitted_at'])
                        )
            
        except Exception as e:
            logger.error(f"加载监控任务失败: {e}")
    
    def _check_monitoring_tasks(self):
        """检查监控任务"""
        for task in self.monitor_tasks:
            if task.status != 'monitoring':
                continue
            
            if not task.should_check(self.check_interval):
                continue
            
            try:
                self._check_single_task(task)
                task.last_check_at = datetime.utcnow()
                task.check_count += 1
                
            except Exception as e:
                logger.error(f"检查监控任务失败: {task.campaign_id_qc}, 错误: {e}")
    
    def _check_single_task(self, task: AppealMonitorTask):
        """检查单个监控任务"""
        try:
            logger.debug(f"检查提审进度: {task.campaign_id_qc}")
            
            # 使用现有的CopilotSession查询进度
            from qianchuan_aw.services.copilot_service import CopilotSession
            from qianchuan_aw.utils.config_loader import load_settings
            
            app_settings = load_settings()
            
            with CopilotSession(task.principal_name, task.account_id_qc, app_settings) as session:
                appeal_status = session.query_plan(task.campaign_id_qc)
                
                self._process_appeal_status(task, appeal_status)
                
        except Exception as e:
            logger.error(f"检查提审进度异常: {task.campaign_id_qc}, 错误: {e}")
    
    def _process_appeal_status(self, task: AppealMonitorTask, appeal_status: str):
        """处理提审状态"""
        try:
            # 导入状态枚举
            from qianchuan_aw.utils.appeal_status_definitions import AppealStatus, is_terminal_state

            # 检查是否为终态状态
            if is_terminal_state(appeal_status):
                if appeal_status == AppealStatus.APPEAL_SUCCESS.value:
                    # 申诉成功 - 申诉流程结束
                    logger.success(f"🎉 计划 {task.campaign_id_qc} 申诉成功！申诉流程结束")
                    self._complete_monitoring_task(task, True, "申诉成功，可以开始收割通过素材")

                elif appeal_status == AppealStatus.APPEAL_FAILED.value:
                    # 申诉失败 - 申诉流程结束
                    logger.info(f"❌ 计划 {task.campaign_id_qc} 申诉失败，申诉流程结束")
                    self._complete_monitoring_task(task, False, "申诉失败，结束该计划的申诉流程")

                elif appeal_status == AppealStatus.APPEAL_TIMEOUT.value:
                    # 申诉超时 - 申诉流程结束
                    logger.warning(f"⏰ 计划 {task.campaign_id_qc} 申诉超时，申诉流程结束")
                    self._complete_monitoring_task(task, False, "申诉超时，结束监控")

            elif appeal_status == AppealStatus.APPEAL_MONITORING.value:
                # 仍在申诉中，继续监控
                logger.info(f"⏳ 计划 {task.campaign_id_qc} 申诉中，继续监控")
                self._update_monitoring_status(task, 'appeal_monitoring', "申诉进行中，等待结果")

            elif appeal_status == 'NO_RECORD':
                # 无申诉记录，可能需要重新提审
                logger.warning(f"📋 计划 {task.campaign_id_qc} 无申诉记录")
                self._handle_no_record_status(task)

            elif appeal_status in ['UNKNOWN', 'SYSTEM_ERROR', 'THINKING']:
                # 系统错误或临时状态，稍后重试
                logger.warning(f"⚠️ 计划 {task.campaign_id_qc} 查询状态异常: {appeal_status}")

            else:
                logger.warning(f"❓ 计划 {task.campaign_id_qc} 未知提审状态: {appeal_status}")

        except Exception as e:
            logger.error(f"处理提审状态失败: {e}")
    
    def _complete_monitoring_task(self, task: AppealMonitorTask, success: bool, message: str):
        """完成监控任务"""
        try:
            task.status = 'completed' if success else 'failed'
            
            # 更新数据库状态
            final_status = 'appeal_completed' if success else 'appeal_failed'
            
            self.execute_sql(f"""
                UPDATE campaigns 
                SET appeal_status = '{final_status}',
                    appeal_completed_at = NOW(),
                    appeal_result = '{message}',
                    last_appeal_check = NOW(),
                    updated_at = NOW()
                WHERE campaign_id_qc = '{task.campaign_id_qc}'
            """)
            
            # 更新统计
            if success:
                self.stats['completed_appeals'] += 1
                logger.success(f"提审监控完成: {task.campaign_id_qc} - 成功")
            else:
                self.stats['failed_appeals'] += 1
                logger.error(f"提审监控完成: {task.campaign_id_qc} - 失败: {message}")
            
            # 记录监控指标
            duration = (datetime.utcnow() - task.appeal_submitted_at).total_seconds()
            self.monitoring_collector.track_harvest_metrics(
                material_id=task.campaign_id_qc,
                harvest_type='appeal_monitor',
                delay_seconds=duration,
                success=success,
                message=message
            )
            
        except Exception as e:
            logger.error(f"完成监控任务失败: {e}")
    
    def _update_monitoring_status(self, task: AppealMonitorTask, status: str, message: str):
        """更新监控状态"""
        try:
            self.execute_sql(f"""
                UPDATE campaigns 
                SET appeal_status = '{status}',
                    last_appeal_check = NOW(),
                    updated_at = NOW()
                WHERE campaign_id_qc = '{task.campaign_id_qc}'
            """)
            
            logger.debug(f"更新监控状态: {task.campaign_id_qc} -> {status}")
            
        except Exception as e:
            logger.error(f"更新监控状态失败: {e}")
    
    def _handle_no_record_status(self, task: AppealMonitorTask):
        """处理无申诉记录状态"""
        try:
            logger.warning(f"计划 {task.campaign_id_qc} 无申诉记录，可能需要重新提审")
            
            # 检查是否应该重新提审
            if task.check_count <= 2:  # 前几次检查可能是延迟
                logger.info(f"计划 {task.campaign_id_qc} 等待申诉记录生成")
                return
            
            # 标记为需要重新提审
            self.execute_sql(f"""
                UPDATE campaigns 
                SET appeal_status = 'appeal_retry',
                    appeal_error_message = 'No appeal record found, retry needed',
                    next_appeal_retry = NOW() + INTERVAL '300 seconds',
                    last_appeal_check = NOW(),
                    updated_at = NOW()
                WHERE campaign_id_qc = '{task.campaign_id_qc}'
            """)
            
            task.status = 'failed'
            self.stats['failed_appeals'] += 1
            
            logger.warning(f"计划 {task.campaign_id_qc} 标记为需要重新提审")
            
        except Exception as e:
            logger.error(f"处理无申诉记录状态失败: {e}")
    
    def _cleanup_completed_tasks(self):
        """清理完成的任务"""
        self.monitor_tasks = [task for task in self.monitor_tasks if task.status == 'monitoring']
    
    def _handle_timeout_tasks(self):
        """处理超时任务"""
        try:
            timeout_threshold = datetime.utcnow() - timedelta(hours=self.max_monitor_hours)
            
            for task in self.monitor_tasks:
                if task.status == 'monitoring' and task.appeal_submitted_at < timeout_threshold:
                    logger.warning(f"提审监控超时: {task.campaign_id_qc}")
                    
                    # 标记为超时
                    task.status = 'timeout'
                    self.stats['timeout_appeals'] += 1
                    
                    # 更新数据库状态
                    self.execute_sql(f"""
                        UPDATE campaigns 
                        SET appeal_status = 'appeal_failed',
                            appeal_error_message = 'Monitoring timeout after {self.max_monitor_hours} hours',
                            last_appeal_check = NOW(),
                            updated_at = NOW()
                        WHERE campaign_id_qc = '{task.campaign_id_qc}'
                    """)
                    
                    logger.error(f"计划 {task.campaign_id_qc} 监控超时，标记为失败")
            
        except Exception as e:
            logger.error(f"处理超时任务失败: {e}")
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        return {
            'running': self.running,
            'check_interval': self.check_interval,
            'max_monitor_hours': self.max_monitor_hours,
            'active_tasks': len([task for task in self.monitor_tasks if task.status == 'monitoring']),
            'total_tasks': len(self.monitor_tasks),
            'stats': self.stats.copy()
        }
    
    def force_check_campaign(self, campaign_id_qc: str) -> Dict[str, Any]:
        """
        强制检查指定计划的提审状态
        
        Args:
            campaign_id_qc: 计划ID
            
        Returns:
            Dict[str, Any]: 检查结果
        """
        try:
            # 查找任务
            task = None
            for t in self.monitor_tasks:
                if t.campaign_id_qc == campaign_id_qc:
                    task = t
                    break
            
            if not task:
                return {'success': False, 'error': f'计划 {campaign_id_qc} 不在监控队列中'}
            
            # 强制检查
            self._check_single_task(task)
            
            return {
                'success': True,
                'campaign_id_qc': campaign_id_qc,
                'status': task.status,
                'check_count': task.check_count,
                'last_check_at': task.last_check_at.isoformat() if task.last_check_at else None
            }
            
        except Exception as e:
            logger.error(f"强制检查计划失败: {e}")
            return {'success': False, 'error': str(e)}


# 便捷函数
def create_appeal_progress_monitor(execute_sql_func: Callable[[str], str], 
                                  check_interval: int = 300) -> AppealProgressMonitor:
    """创建提审进度监控器的便捷函数"""
    return AppealProgressMonitor(execute_sql_func, check_interval)
