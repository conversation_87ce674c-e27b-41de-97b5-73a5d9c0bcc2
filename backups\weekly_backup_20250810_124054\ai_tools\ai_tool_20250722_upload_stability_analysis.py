#!/usr/bin/env python3
"""
千川上传接口稳定性和性能评估工具
分析长期运行的稳定性问题和性能瓶颈
"""

import sys
import os
import time
import psutil
import gc
from datetime import datetime, timedelta
from pathlib import Path
import threading
import queue
import json

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.db_utils import database_session
from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.config_loader import load_settings
from sqlalchemy import text

class UploadStabilityAnalyzer:
    """上传接口稳定性分析器"""
    
    def __init__(self):
        self.config = load_settings()
        self.analysis_results = {
            'memory_usage': [],
            'connection_pool_status': [],
            'file_handle_count': [],
            'api_call_frequency': [],
            'error_patterns': [],
            'performance_metrics': []
        }
        self.start_time = datetime.now()
    
    def analyze_system_resources(self):
        """分析系统资源使用情况"""
        logger.info("🔍 分析系统资源使用情况")
        logger.info("=" * 50)
        
        # 获取当前进程信息
        process = psutil.Process()
        
        # 内存使用分析
        memory_info = process.memory_info()
        memory_percent = process.memory_percent()
        
        logger.info(f"📊 内存使用情况:")
        logger.info(f"  RSS内存: {memory_info.rss / (1024*1024):.1f} MB")
        logger.info(f"  VMS内存: {memory_info.vms / (1024*1024):.1f} MB")
        logger.info(f"  内存占用率: {memory_percent:.1f}%")
        
        # 文件句柄分析
        try:
            open_files = process.open_files()
            logger.info(f"  打开文件数: {len(open_files)}")
            
            # 分析文件类型
            file_types = {}
            for file_info in open_files:
                ext = Path(file_info.path).suffix.lower()
                file_types[ext] = file_types.get(ext, 0) + 1
            
            if file_types:
                logger.info(f"  文件类型分布:")
                for ext, count in sorted(file_types.items(), key=lambda x: x[1], reverse=True)[:5]:
                    logger.info(f"    {ext or '无扩展名'}: {count}个")
        
        except (psutil.AccessDenied, psutil.NoSuchProcess):
            logger.warning("无法获取文件句柄信息")
        
        # 网络连接分析
        try:
            connections = process.connections()
            active_connections = [conn for conn in connections if conn.status == 'ESTABLISHED']
            logger.info(f"  活跃网络连接: {len(active_connections)}")
            
            # 分析连接目标
            connection_targets = {}
            for conn in active_connections:
                if conn.raddr:
                    target = f"{conn.raddr.ip}:{conn.raddr.port}"
                    connection_targets[target] = connection_targets.get(target, 0) + 1
            
            if connection_targets:
                logger.info(f"  主要连接目标:")
                for target, count in sorted(connection_targets.items(), key=lambda x: x[1], reverse=True)[:3]:
                    logger.info(f"    {target}: {count}个连接")
        
        except (psutil.AccessDenied, psutil.NoSuchProcess):
            logger.warning("无法获取网络连接信息")
        
        # 线程分析
        thread_count = process.num_threads()
        logger.info(f"  线程数: {thread_count}")
        
        return {
            'memory_rss_mb': memory_info.rss / (1024*1024),
            'memory_percent': memory_percent,
            'open_files_count': len(open_files) if 'open_files' in locals() else 0,
            'active_connections': len(active_connections) if 'active_connections' in locals() else 0,
            'thread_count': thread_count
        }
    
    def analyze_upload_frequency(self):
        """分析上传接口调用频率"""
        logger.info(f"\n📈 分析上传接口调用频率")
        logger.info("=" * 50)
        
        with database_session() as db:
            # 分析最近24小时的上传活动
            upload_activity_query = text("""
                SELECT 
                    DATE_TRUNC('hour', lc.created_at) as hour,
                    COUNT(*) as upload_count,
                    COUNT(CASE WHEN lc.status = 'upload_failed' THEN 1 END) as failed_count
                FROM local_creatives lc
                JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
                WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                    AND lc.created_at >= NOW() - INTERVAL '24 hours'
                GROUP BY DATE_TRUNC('hour', lc.created_at)
                ORDER BY hour DESC
                LIMIT 24
            """)
            
            activity_results = db.execute(upload_activity_query).fetchall()
            
            if activity_results:
                logger.info(f"  最近24小时上传活动:")
                total_uploads = 0
                total_failures = 0
                
                for row in activity_results:
                    hour_str = row.hour.strftime('%H:00')
                    failure_rate = (row.failed_count / row.upload_count * 100) if row.upload_count > 0 else 0
                    logger.info(f"    {hour_str}: {row.upload_count}次上传, {row.failed_count}次失败 ({failure_rate:.1f}%)")
                    
                    total_uploads += row.upload_count
                    total_failures += row.failed_count
                
                avg_hourly_uploads = total_uploads / len(activity_results)
                overall_failure_rate = (total_failures / total_uploads * 100) if total_uploads > 0 else 0
                
                logger.info(f"\n  总体统计:")
                logger.info(f"    总上传次数: {total_uploads}")
                logger.info(f"    平均每小时: {avg_hourly_uploads:.1f}次")
                logger.info(f"    总失败率: {overall_failure_rate:.1f}%")
                
                # 评估API调用频率
                max_workers = self.config.get('workflow', {}).get('max_upload_workers', 15)
                theoretical_max_per_hour = max_workers * 60  # 假设每分钟1次上传
                
                logger.info(f"\n  频率分析:")
                logger.info(f"    当前并发数: {max_workers}")
                logger.info(f"    理论最大频率: {theoretical_max_per_hour}次/小时")
                logger.info(f"    实际使用率: {(avg_hourly_uploads/theoretical_max_per_hour*100):.1f}%")
                
                if avg_hourly_uploads > theoretical_max_per_hour * 0.8:
                    logger.warning("⚠️ 接近频率限制，可能需要优化")
                else:
                    logger.info("✅ 频率使用合理")
            
            else:
                logger.info("  最近24小时无上传活动")
    
    def analyze_long_running_issues(self):
        """分析长期运行问题"""
        logger.info(f"\n🔧 分析长期运行问题")
        logger.info("=" * 50)
        
        issues_found = []
        
        # 1. 检查内存泄漏迹象
        current_memory = psutil.Process().memory_info().rss / (1024*1024)
        if current_memory > 500:  # 超过500MB
            issues_found.append({
                'type': 'memory_leak',
                'severity': 'high',
                'description': f'内存使用过高: {current_memory:.1f}MB',
                'recommendation': '检查是否存在内存泄漏，考虑定期重启'
            })
        
        # 2. 检查文件句柄泄漏
        try:
            open_files_count = len(psutil.Process().open_files())
            if open_files_count > 100:
                issues_found.append({
                    'type': 'file_handle_leak',
                    'severity': 'medium',
                    'description': f'打开文件数过多: {open_files_count}个',
                    'recommendation': '检查文件句柄是否正确关闭'
                })
        except:
            pass
        
        # 3. 检查连接池状态
        try:
            connections = psutil.Process().connections()
            established_count = len([c for c in connections if c.status == 'ESTABLISHED'])
            if established_count > 50:
                issues_found.append({
                    'type': 'connection_pool_issue',
                    'severity': 'medium',
                    'description': f'活跃连接数过多: {established_count}个',
                    'recommendation': '检查连接池配置和连接复用'
                })
        except:
            pass
        
        # 4. 检查数据库连接
        with database_session() as db:
            # 检查长时间运行的查询
            long_queries_query = text("""
                SELECT COUNT(*) as long_query_count
                FROM pg_stat_activity 
                WHERE state = 'active' 
                    AND query_start < NOW() - INTERVAL '5 minutes'
                    AND query NOT LIKE '%pg_stat_activity%'
            """)
            
            try:
                result = db.execute(long_queries_query).fetchone()
                if result and result.long_query_count > 0:
                    issues_found.append({
                        'type': 'long_running_queries',
                        'severity': 'high',
                        'description': f'发现{result.long_query_count}个长时间运行的查询',
                        'recommendation': '优化查询性能，添加索引或分页'
                    })
            except Exception as e:
                logger.debug(f"无法检查长时间运行的查询: {e}")
        
        # 报告问题
        if issues_found:
            logger.warning(f"🔴 发现 {len(issues_found)} 个潜在问题:")
            for issue in issues_found:
                severity_icon = "🔴" if issue['severity'] == 'high' else "🟡"
                logger.warning(f"  {severity_icon} {issue['type']}: {issue['description']}")
                logger.info(f"     建议: {issue['recommendation']}")
        else:
            logger.info("✅ 未发现明显的长期运行问题")
        
        return issues_found
    
    def generate_performance_recommendations(self):
        """生成性能优化建议"""
        logger.info(f"\n🚀 性能优化建议")
        logger.info("=" * 50)
        
        current_config = self.config.get('workflow', {})
        
        recommendations = []
        
        # 1. 并发配置优化
        max_workers = current_config.get('max_upload_workers', 15)
        if max_workers > 20:
            recommendations.append({
                'category': 'concurrency',
                'priority': 'medium',
                'suggestion': f'当前并发数({max_workers})较高，建议降至15以减少资源竞争',
                'config_change': {'max_upload_workers': 15}
            })
        elif max_workers < 10:
            recommendations.append({
                'category': 'concurrency',
                'priority': 'low',
                'suggestion': f'当前并发数({max_workers})较低，可适当提升至12-15',
                'config_change': {'max_upload_workers': 12}
            })
        
        # 2. 重试机制优化
        retry_delay = self.config.get('robustness', {}).get('upload_retry_delay', 30)
        if retry_delay > 60:
            recommendations.append({
                'category': 'retry',
                'priority': 'medium',
                'suggestion': f'重试延迟({retry_delay}s)过长，建议降至30-45秒',
                'config_change': {'upload_retry_delay': 30}
            })
        
        # 3. 监控机制建议
        recommendations.append({
            'category': 'monitoring',
            'priority': 'high',
            'suggestion': '建立实时监控机制，跟踪内存使用、连接数、错误率',
            'implementation': '创建监控脚本，定期收集系统指标'
        })
        
        # 4. 资源管理建议
        recommendations.append({
            'category': 'resource_management',
            'priority': 'high',
            'suggestion': '实施资源清理机制，定期释放未使用的连接和文件句柄',
            'implementation': '添加资源清理定时任务'
        })
        
        # 输出建议
        for rec in recommendations:
            priority_icon = "🔴" if rec['priority'] == 'high' else "🟡" if rec['priority'] == 'medium' else "🟢"
            logger.info(f"  {priority_icon} [{rec['category']}] {rec['suggestion']}")
            
            if 'config_change' in rec:
                logger.info(f"     配置修改: {rec['config_change']}")
            if 'implementation' in rec:
                logger.info(f"     实施方案: {rec['implementation']}")
        
        return recommendations
    
    def create_monitoring_script(self):
        """创建监控脚本"""
        monitoring_script = """#!/usr/bin/env python3
'''
千川上传接口实时监控脚本
定期收集系统指标并报告异常
'''

import psutil
import time
import json
from datetime import datetime
from pathlib import Path

class UploadMonitor:
    def __init__(self):
        self.log_file = Path("logs/upload_monitor.log")
        self.log_file.parent.mkdir(exist_ok=True)
    
    def collect_metrics(self):
        process = psutil.Process()
        
        metrics = {
            'timestamp': datetime.now().isoformat(),
            'memory_mb': process.memory_info().rss / (1024*1024),
            'memory_percent': process.memory_percent(),
            'open_files': len(process.open_files()) if hasattr(process, 'open_files') else 0,
            'connections': len([c for c in process.connections() if c.status == 'ESTABLISHED']),
            'threads': process.num_threads(),
            'cpu_percent': process.cpu_percent()
        }
        
        return metrics
    
    def check_alerts(self, metrics):
        alerts = []
        
        if metrics['memory_mb'] > 500:
            alerts.append(f"高内存使用: {metrics['memory_mb']:.1f}MB")
        
        if metrics['open_files'] > 100:
            alerts.append(f"文件句柄过多: {metrics['open_files']}")
        
        if metrics['connections'] > 50:
            alerts.append(f"连接数过多: {metrics['connections']}")
        
        return alerts
    
    def run_monitoring(self, interval=300):  # 5分钟间隔
        while True:
            try:
                metrics = self.collect_metrics()
                alerts = self.check_alerts(metrics)
                
                # 记录日志
                with open(self.log_file, 'a', encoding='utf-8') as f:
                    f.write(json.dumps(metrics) + '\\n')
                
                # 输出警报
                if alerts:
                    print(f"[{metrics['timestamp']}] 警报:")
                    for alert in alerts:
                        print(f"  - {alert}")
                
                time.sleep(interval)
                
            except Exception as e:
                print(f"监控异常: {e}")
                time.sleep(60)

if __name__ == "__main__":
    monitor = UploadMonitor()
    monitor.run_monitoring()
"""
        
        script_path = "ai_tools/ai_tool_20250722_upload_monitor.py"
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(monitoring_script)
        
        logger.info(f"✅ 创建监控脚本: {script_path}")
        return script_path

def main():
    """主函数"""
    try:
        analyzer = UploadStabilityAnalyzer()
        
        # 分析系统资源
        resource_metrics = analyzer.analyze_system_resources()
        
        # 分析上传频率
        analyzer.analyze_upload_frequency()
        
        # 分析长期运行问题
        issues = analyzer.analyze_long_running_issues()
        
        # 生成优化建议
        recommendations = analyzer.generate_performance_recommendations()
        
        # 创建监控脚本
        monitor_script = analyzer.create_monitoring_script()
        
        logger.info(f"\n✅ 上传接口稳定性分析完成")
        logger.info(f"建议定期运行监控脚本: python {monitor_script}")
        
        return {
            'resource_metrics': resource_metrics,
            'issues': issues,
            'recommendations': recommendations,
            'monitor_script': monitor_script
        }
        
    except Exception as e:
        logger.error(f"❌ 分析过程中出现错误: {e}")
        raise

if __name__ == "__main__":
    main()
