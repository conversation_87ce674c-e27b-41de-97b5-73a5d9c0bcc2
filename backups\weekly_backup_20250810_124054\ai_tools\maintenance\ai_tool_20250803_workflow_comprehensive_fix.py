#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 综合修复工作流系统问题，解决入库文件处理问题
清理条件: 成为项目核心修复工具，长期保留
"""

import os
import sys
import json
from datetime import datetime
from pathlib import Path
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def setup_logging():
    """设置日志"""
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | {message}",
        level="INFO"
    )

def fix_database_path_issues():
    """修复数据库中的路径问题"""
    logger.info("🔧 修复数据库路径问题...")
    
    try:
        from src.qianchuan_aw.database.connection import database_session
        from src.qianchuan_aw.database.models import LocalCreative
        
        with database_session() as db:
            # 查找所有路径问题的记录
            records_with_path_issues = db.query(LocalCreative).filter(
                LocalCreative.file_path.like('%01_to_process%')
            ).all()
            
            logger.info(f"📊 发现 {len(records_with_path_issues)} 条路径问题记录")
            
            fixed_count = 0
            for record in records_with_path_issues:
                old_path = record.file_path
                new_path = old_path.replace('01_to_process', '01_materials_to_process')
                
                # 检查新路径文件是否存在
                if os.path.exists(new_path):
                    record.file_path = new_path
                    record.updated_at = datetime.now()
                    fixed_count += 1
                    logger.success(f"✅ 修复路径: {record.filename}")
                else:
                    logger.warning(f"⚠️ 文件不存在，跳过: {new_path}")
            
            db.commit()
            logger.success(f"🎯 成功修复 {fixed_count} 条路径问题")
            return fixed_count
            
    except Exception as e:
        logger.error(f"❌ 修复路径问题失败: {e}")
        return 0

def process_pending_grouping_files():
    """处理pending_grouping状态的文件"""
    logger.info("🔧 处理pending_grouping状态文件...")
    
    try:
        from src.qianchuan_aw.database.connection import database_session
        from src.qianchuan_aw.database.models import LocalCreative
        
        with database_session() as db:
            # 查找pending_grouping状态的文件
            pending_files = db.query(LocalCreative).filter(
                LocalCreative.status == 'pending_grouping'
            ).all()
            
            logger.info(f"📊 发现 {len(pending_files)} 个pending_grouping状态文件")
            
            processed_count = 0
            for record in pending_files:
                # 检查文件是否存在
                if os.path.exists(record.file_path):
                    # 将状态改为pending_upload，让系统重新处理
                    record.status = 'pending_upload'
                    record.updated_at = datetime.now()
                    processed_count += 1
                    logger.success(f"✅ 重置状态: {record.filename} -> pending_upload")
                else:
                    logger.warning(f"⚠️ 文件不存在: {record.file_path}")
            
            db.commit()
            logger.success(f"🎯 成功处理 {processed_count} 个pending_grouping文件")
            return processed_count
            
    except Exception as e:
        logger.error(f"❌ 处理pending_grouping文件失败: {e}")
        return 0

def check_unprocessed_files():
    """检查未处理的文件"""
    logger.info("🔍 检查未处理的文件...")
    
    directory = "D:/workflow_assets/01_materials_to_process/缇萃百货"
    if not os.path.exists(directory):
        logger.error(f"❌ 目录不存在: {directory}")
        return []
    
    # 获取目录中的所有视频文件
    directory_files = set()
    for filename in os.listdir(directory):
        if filename.endswith('.mp4'):
            directory_files.add(filename)
    
    logger.info(f"📁 目录中发现 {len(directory_files)} 个视频文件")
    
    try:
        from src.qianchuan_aw.database.connection import database_session
        from src.qianchuan_aw.database.models import LocalCreative
        
        with database_session() as db:
            # 查询数据库中的文件
            db_records = db.query(LocalCreative).all()
            db_files = set(record.filename for record in db_records)
            
            # 找出未入库的文件
            unprocessed_files = directory_files - db_files
            
            logger.info(f"💾 数据库中有 {len(db_files)} 条记录")
            logger.info(f"⏳ 未处理文件: {len(unprocessed_files)} 个")
            
            if unprocessed_files:
                logger.warning("⚠️ 未处理的文件:")
                for filename in sorted(unprocessed_files):
                    logger.warning(f"   📄 {filename}")
            
            return list(unprocessed_files)
            
    except Exception as e:
        logger.error(f"❌ 检查未处理文件失败: {e}")
        return []

def trigger_workflow_tasks():
    """触发工作流任务"""
    logger.info("🚀 触发工作流任务...")
    
    try:
        from src.qianchuan_aw.workflows.tasks import (
            task_ingest_and_upload,
            task_group_and_dispatch,
            task_upload_materials
        )
        
        # 1. 触发文件摄取任务
        task_ingest_and_upload.delay()
        logger.success("✅ 已触发文件摄取任务")
        
        # 2. 触发分组派发任务
        task_group_and_dispatch.delay()
        logger.success("✅ 已触发分组派发任务")
        
        # 3. 触发素材上传任务
        task_upload_materials.delay()
        logger.success("✅ 已触发素材上传任务")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 触发工作流任务失败: {e}")
        return False

def check_celery_status():
    """检查Celery服务状态"""
    logger.info("🔍 检查Celery服务状态...")
    
    # 检查Celery Beat调度文件
    beat_file = "logs/celerybeat-schedule.db"
    beat_running = False
    
    if os.path.exists(beat_file):
        stat = os.stat(beat_file)
        last_modified = datetime.fromtimestamp(stat.st_mtime)
        beat_running = (datetime.now() - last_modified).seconds < 300  # 5分钟内
        logger.info(f"📅 Celery Beat最后更新: {last_modified.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"🔄 Celery Beat状态: {'✅ 运行中' if beat_running else '❌ 未运行'}")
    else:
        logger.warning("⚠️ Celery Beat调度文件不存在")
    
    # 检查最近的任务执行
    log_file = "logs/app_2025-08-03.log"
    recent_tasks = False
    
    if os.path.exists(log_file):
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否有最近的任务执行
            if 'Task Start' in content[-10000:]:  # 检查最后10KB内容
                recent_tasks = True
                logger.success("✅ 检测到最近的任务执行")
            else:
                logger.warning("⚠️ 未检测到最近的任务执行")
                
        except Exception as e:
            logger.error(f"❌ 检查日志失败: {e}")
    
    return {
        'beat_running': beat_running,
        'recent_tasks': recent_tasks
    }

def generate_fix_report(results):
    """生成修复报告"""
    logger.info("📋 生成修复报告...")
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'fixes_applied': {
            'path_issues_fixed': results.get('path_fixes', 0),
            'pending_grouping_processed': results.get('grouping_fixes', 0),
            'tasks_triggered': results.get('tasks_triggered', False)
        },
        'system_status': results.get('celery_status', {}),
        'unprocessed_files': results.get('unprocessed_files', []),
        'recommendations': []
    }
    
    # 生成建议
    if not results.get('celery_status', {}).get('beat_running', False):
        report['recommendations'].append({
            'priority': 'CRITICAL',
            'issue': 'Celery Beat调度器未运行',
            'solution': '启动Celery Beat服务: celery -A src.qianchuan_aw.celery_app beat --loglevel=info'
        })
    
    if results.get('unprocessed_files'):
        report['recommendations'].append({
            'priority': 'HIGH',
            'issue': f"{len(results['unprocessed_files'])} 个文件未入库",
            'solution': '等待文件摄取任务处理或手动触发任务'
        })
    
    # 保存报告
    report_file = f"ai_reports/maintenance/ai_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}_workflow_fix.json"
    os.makedirs(os.path.dirname(report_file), exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2, default=str)
    
    logger.success(f"📋 修复报告已保存: {report_file}")
    return report

def main():
    """主函数"""
    setup_logging()
    
    logger.info("=" * 80)
    logger.info("🔧 工作流系统综合修复工具")
    logger.info("=" * 80)
    
    results = {}
    
    # 1. 修复数据库路径问题
    results['path_fixes'] = fix_database_path_issues()
    
    # 2. 处理pending_grouping状态文件
    results['grouping_fixes'] = process_pending_grouping_files()
    
    # 3. 检查未处理文件
    results['unprocessed_files'] = check_unprocessed_files()
    
    # 4. 检查Celery状态
    results['celery_status'] = check_celery_status()
    
    # 5. 触发工作流任务
    results['tasks_triggered'] = trigger_workflow_tasks()
    
    # 6. 生成修复报告
    report = generate_fix_report(results)
    
    # 输出总结
    logger.info("=" * 80)
    logger.info("🎯 修复总结:")
    logger.info(f"   🔧 路径问题修复: {results['path_fixes']} 条")
    logger.info(f"   📋 状态重置: {results['grouping_fixes']} 个")
    logger.info(f"   ⏳ 未处理文件: {len(results['unprocessed_files'])} 个")
    logger.info(f"   🚀 任务触发: {'✅ 成功' if results['tasks_triggered'] else '❌ 失败'}")
    logger.info(f"   🔄 Celery Beat: {'✅ 运行' if results['celery_status']['beat_running'] else '❌ 停止'}")
    logger.info(f"   💡 建议数量: {len(report['recommendations'])} 条")
    
    if report['recommendations']:
        logger.info("\n🎯 关键建议:")
        for rec in report['recommendations']:
            priority_color = {'CRITICAL': '🔴', 'HIGH': '🟠', 'MEDIUM': '🟡', 'LOW': '🟢'}
            logger.info(f"   {priority_color.get(rec['priority'], '⚪')} {rec['issue']}")
            logger.info(f"      {rec['solution']}")
    
    logger.info("=" * 80)

if __name__ == "__main__":
    main()
