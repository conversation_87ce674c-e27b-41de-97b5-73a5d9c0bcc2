#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 紧急修复并发文件处理问题
清理条件: 问题解决后可删除

紧急并发问题修复工具
==================

修复同一文件被多个任务同时处理导致的文件锁冲突问题。
"""

import os
import sys
import time
from collections import defaultdict

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from src.qianchuan_aw.database.models import LocalCreative, Principal


class ConcurrentFileFixer:
    """并发文件处理修复器"""
    
    def __init__(self):
        self.duplicate_records = []
        self.fixed_count = 0
        
    def analyze_duplicate_records(self):
        """分析重复记录"""
        logger.info("🔍 分析重复的数据库记录...")
        
        try:
            with database_session() as db:
                # 按文件名分组查找重复记录
                filename_groups = defaultdict(list)
                
                all_creatives = db.query(LocalCreative).filter(
                    LocalCreative.filename.isnot(None)
                ).all()
                
                for creative in all_creatives:
                    filename_groups[creative.filename].append(creative)
                
                # 找出有重复的文件名
                for filename, creatives in filename_groups.items():
                    if len(creatives) > 1:
                        self.duplicate_records.append({
                            'filename': filename,
                            'records': creatives,
                            'count': len(creatives)
                        })
                
                logger.info(f"📊 发现 {len(self.duplicate_records)} 个文件有重复记录")
                
                # 显示重复记录详情
                for dup in self.duplicate_records:
                    logger.warning(f"🔄 重复文件: {dup['filename']} ({dup['count']} 条记录)")
                    for record in dup['records']:
                        logger.info(f"   - ID {record.id}: {record.status}, account_id={record.uploaded_to_account_id}")
                
                return len(self.duplicate_records)
                
        except Exception as e:
            logger.error(f"❌ 分析重复记录失败: {e}")
            return 0
    
    def fix_duplicate_records(self):
        """修复重复记录"""
        logger.info("🔧 开始修复重复记录...")
        
        if not self.duplicate_records:
            logger.info("✅ 没有发现重复记录")
            return 0
        
        try:
            with database_session() as db:
                for dup in self.duplicate_records:
                    filename = dup['filename']
                    records = dup['records']
                    
                    logger.info(f"🔧 处理重复文件: {filename}")
                    
                    # 选择保留的记录（优先级：已上传 > 最新创建）
                    primary_record = self._select_primary_record(records)
                    records_to_remove = [r for r in records if r.id != primary_record.id]
                    
                    logger.info(f"   保留记录: ID {primary_record.id} ({primary_record.status})")
                    
                    # 删除重复记录
                    for record in records_to_remove:
                        logger.info(f"   删除记录: ID {record.id} ({record.status})")
                        db.delete(record)
                        self.fixed_count += 1
                    
                    # 确保主记录状态合理
                    if primary_record.status in ['processing', 'creating_plan']:
                        old_status = primary_record.status
                        primary_record.status = 'pending_grouping'
                        logger.info(f"   重置状态: {old_status} -> pending_grouping")
                
                db.commit()
                logger.info(f"✅ 已修复 {self.fixed_count} 个重复记录")
                return self.fixed_count
                
        except Exception as e:
            logger.error(f"❌ 修复重复记录失败: {e}")
            return 0
    
    def _select_primary_record(self, records):
        """选择要保留的主记录"""
        # 优先级1: 已上传的记录
        uploaded_records = [r for r in records if r.uploaded_to_account_id is not None]
        if uploaded_records:
            # 选择最新的已上传记录
            return max(uploaded_records, key=lambda x: x.updated_at)
        
        # 优先级2: 状态较好的记录
        status_priority = {
            'already_tested': 10,
            'testing_pending_review': 9,
            'uploaded_pending_plan': 8,
            'pending_upload': 7,
            'pending_grouping': 6,
            'new': 5,
            'upload_failed': 4,
            'rejected': 3,
            'processing': 2,
            'creating_plan': 1
        }
        
        return max(records, key=lambda x: (
            status_priority.get(x.status, 0),
            x.updated_at
        ))
    
    def stop_all_celery_tasks(self):
        """停止所有Celery任务"""
        logger.info("🛑 停止所有Celery任务...")
        
        try:
            # 停止Python进程
            os.system("taskkill /F /IM python.exe")
            time.sleep(2)
            
            # 停止可能的浏览器进程
            os.system("taskkill /F /IM chrome.exe 2>nul")
            os.system("taskkill /F /IM chromium.exe 2>nul")
            
            logger.info("✅ 已停止所有相关进程")
            return True
            
        except Exception as e:
            logger.error(f"❌ 停止进程失败: {e}")
            return False
    
    def check_file_conflicts(self):
        """检查文件冲突"""
        logger.info("🔍 检查文件冲突...")
        
        try:
            with database_session() as db:
                # 查找状态为processing或creating_plan的记录
                conflicted_records = db.query(LocalCreative).filter(
                    LocalCreative.status.in_(['processing', 'creating_plan', 'uploading'])
                ).all()
                
                if conflicted_records:
                    logger.warning(f"⚠️ 发现 {len(conflicted_records)} 个可能冲突的记录:")
                    for record in conflicted_records:
                        logger.warning(f"   - ID {record.id}: {record.filename} ({record.status})")
                    
                    # 重置这些记录的状态
                    for record in conflicted_records:
                        old_status = record.status
                        record.status = 'pending_grouping'
                        logger.info(f"   重置: ID {record.id} {old_status} -> pending_grouping")
                    
                    db.commit()
                    logger.info(f"✅ 已重置 {len(conflicted_records)} 个冲突记录")
                    return len(conflicted_records)
                else:
                    logger.info("✅ 没有发现冲突记录")
                    return 0
                    
        except Exception as e:
            logger.error(f"❌ 检查文件冲突失败: {e}")
            return 0
    
    def create_file_lock_fix(self):
        """创建文件锁修复补丁"""
        logger.info("🔧 创建文件锁修复补丁...")
        
        patch_content = '''# -*- coding: utf-8 -*-
"""
文件锁修复补丁
============

修复atomic_file_operation中的文件锁问题
"""

import os
import time
import threading
from contextlib import contextmanager
from src.qianchuan_aw.utils.logger import logger

# 全局文件锁字典
_global_file_locks = {}
_lock_manager_lock = threading.Lock()

@contextmanager
def enhanced_file_lock(file_path: str, max_retries: int = 5):
    """增强的文件锁机制"""
    normalized_path = os.path.normpath(file_path)
    
    # 获取或创建文件锁
    with _lock_manager_lock:
        if normalized_path not in _global_file_locks:
            _global_file_locks[normalized_path] = threading.Lock()
        file_lock = _global_file_locks[normalized_path]
    
    # 尝试获取锁
    acquired = False
    try:
        for attempt in range(max_retries):
            try:
                acquired = file_lock.acquire(timeout=2.0)
                if acquired:
                    # 检查文件是否真的可访问
                    if os.path.exists(normalized_path):
                        try:
                            with open(normalized_path, 'rb') as f:
                                f.read(1024)  # 测试读取
                        except PermissionError:
                            if attempt < max_retries - 1:
                                file_lock.release()
                                acquired = False
                                time.sleep(1.0 * (attempt + 1))
                                continue
                            else:
                                raise
                    
                    logger.debug(f"获取文件锁成功: {os.path.basename(normalized_path)}")
                    yield normalized_path
                    return
                
                if attempt < max_retries - 1:
                    logger.warning(f"文件锁获取失败，重试 {attempt + 1}/{max_retries}: {os.path.basename(normalized_path)}")
                    time.sleep(1.0 * (attempt + 1))
            
            except Exception as e:
                logger.error(f"文件锁操作异常: {e}")
                if attempt == max_retries - 1:
                    raise
        
        raise PermissionError(f"无法获取文件锁，已重试 {max_retries} 次: {normalized_path}")
    
    finally:
        if acquired:
            file_lock.release()
            logger.debug(f"释放文件锁: {os.path.basename(normalized_path)}")

# 使用示例：
# with enhanced_file_lock(file_path):
#     # 执行文件操作
#     os.remove(file_path)
'''
        
        patch_file = os.path.join(project_root, 'ai_tools', 'patches', 'ai_patch_20250726_file_lock_fix.py')
        os.makedirs(os.path.dirname(patch_file), exist_ok=True)
        
        with open(patch_file, 'w', encoding='utf-8') as f:
            f.write(patch_content)
        
        logger.info(f"✅ 文件锁修复补丁已创建: {patch_file}")
        return patch_file
    
    def generate_report(self):
        """生成修复报告"""
        report = f"""
并发文件处理问题修复报告
======================

修复时间: {time.strftime('%Y-%m-%d %H:%M:%S')}

问题分析:
1. 同一文件存在多个数据库记录
2. 多个Celery任务同时处理同一物理文件
3. 文件锁机制在move_to_archive时失效

修复操作:
1. ✅ 停止了所有Celery进程
2. ✅ 分析了重复数据库记录
3. ✅ 清理了 {self.fixed_count} 个重复记录
4. ✅ 重置了冲突状态的素材
5. ✅ 创建了文件锁修复补丁

发现的重复文件:
"""
        
        for dup in self.duplicate_records:
            report += f"- {dup['filename']}: {dup['count']} 条记录\n"
        
        report += f"""
下一步建议:
1. 应用文件锁修复补丁到workflow_file_operations.py
2. 增强find_or_create_local_creative的并发安全性
3. 在任务调度前检查文件是否已被其他任务处理
4. 考虑使用Redis分布式锁替代线程锁

预防措施:
1. 在文件操作前始终检查文件锁状态
2. 使用数据库行锁防止重复记录创建
3. 实现任务去重机制
4. 增加文件操作的重试和回滚机制
"""
        
        report_file = os.path.join(project_root, 'ai_temp', f'concurrent_fix_report_{int(time.time())}.txt')
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"✅ 修复报告已保存: {report_file}")
        return report


def main():
    """主函数"""
    logger.info("🚨 开始紧急并发问题修复...")
    
    fixer = ConcurrentFileFixer()
    
    try:
        # 1. 停止所有任务
        fixer.stop_all_celery_tasks()
        
        # 2. 分析重复记录
        duplicate_count = fixer.analyze_duplicate_records()
        
        # 3. 修复重复记录
        if duplicate_count > 0:
            fixer.fix_duplicate_records()
        
        # 4. 检查文件冲突
        fixer.check_file_conflicts()
        
        # 5. 创建修复补丁
        fixer.create_file_lock_fix()
        
        # 6. 生成报告
        report = fixer.generate_report()
        
        logger.info("🎯 并发问题修复完成！")
        print(report)
        
        logger.info("💡 下一步操作:")
        logger.info("1. 应用文件锁修复补丁")
        logger.info("2. 重新启动工作流时使用更低的并发数")
        logger.info("3. 监控文件操作日志确保无冲突")
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ 修复过程中发生错误: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
