#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 千川业务规则实际数据库审计
清理条件: 系统不再需要业务规则审计时删除

千川业务规则实际数据库审计工具
============================

通过实际数据库查询验证三大核心业务规则
"""

import os
import sys
import subprocess
from datetime import datetime
from typing import Dict, List, Any, Tuple

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from src.qianchuan_aw.utils.logger import logger


def execute_sql_query(query: str) -> List[Dict[str, Any]]:
    """执行SQL查询并返回结果"""
    try:
        cmd = [
            'psql', 
            '-h', 'localhost',
            '-p', '5432', 
            '-U', 'postgres',
            '-d', 'qianchuan_aw',
            '-t',  # 只输出数据
            '-A',  # 不对齐输出
            '-F', '|',  # 使用|作为分隔符
            '-c', query
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            if not lines or lines == ['']:
                return []
            
            # 解析结果
            results = []
            for line in lines:
                if line.strip():
                    parts = line.split('|')
                    # 这里简化处理，实际应该根据查询结构解析
                    results.append({'raw': line, 'parts': parts})
            
            return results
        else:
            logger.error(f"SQL查询失败: {result.stderr}")
            return []
            
    except Exception as e:
        logger.error(f"执行SQL查询异常: {e}")
        return []


class RealBusinessRulesAuditor:
    """真实业务规则审计器"""
    
    def __init__(self):
        self.audit_start_time = datetime.now()
        self.violations = []
    
    def audit_rule_1_material_uniqueness(self) -> Dict[str, Any]:
        """审计规则1：素材唯一性测试铁律"""
        logger.info("🔍 审计规则1：素材唯一性测试铁律")
        
        # 查找相同file_hash创建了多个测试计划的情况
        duplicate_plans_query = """
        SELECT 
            lc.file_hash,
            lc.filename,
            COUNT(DISTINCT c.id) as plan_count,
            STRING_AGG(DISTINCT c.campaign_id_qc, ', ') as plan_ids
        FROM local_creatives lc
        JOIN platform_creatives pc ON lc.id = pc.local_creative_id
        JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
        JOIN campaigns c ON cpca.campaign_id = c.id
        JOIN ad_accounts aa ON c.account_id = aa.id
        WHERE aa.account_type = 'TEST'
        GROUP BY lc.file_hash, lc.filename
        HAVING COUNT(DISTINCT c.id) > 1
        ORDER BY plan_count DESC;
        """
        
        duplicate_results = execute_sql_query(duplicate_plans_query)
        
        # 统计查询
        total_materials_query = """
        SELECT COUNT(DISTINCT lc.file_hash) as total_unique_materials
        FROM local_creatives lc
        JOIN platform_creatives pc ON lc.id = pc.local_creative_id
        JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
        JOIN campaigns c ON cpca.campaign_id = c.id
        JOIN ad_accounts aa ON c.account_id = aa.id
        WHERE aa.account_type = 'TEST';
        """
        
        total_results = execute_sql_query(total_materials_query)
        total_materials = int(total_results[0]['parts'][0]) if total_results else 0
        
        violations_count = len(duplicate_results)
        
        audit_result = {
            'rule_name': '素材唯一性测试铁律',
            'status': 'PASS' if violations_count == 0 else 'FAIL',
            'violations_count': violations_count,
            'total_materials_checked': total_materials,
            'violation_details': duplicate_results,
            'severity': 'HIGH' if violations_count > 0 else 'NONE'
        }
        
        if violations_count == 0:
            logger.success(f"✅ 规则1审计通过：{total_materials}个素材严格遵循唯一性原则")
        else:
            logger.error(f"❌ 规则1审计失败：发现{violations_count}个素材违反唯一性原则")
            for violation in duplicate_results:
                parts = violation['parts']
                if len(parts) >= 3:
                    logger.error(f"   违规素材: {parts[1]} (hash: {parts[0][:8]}...) 创建了{parts[2]}个计划")
        
        return audit_result
    
    def audit_rule_2_appeal_once_only(self) -> Dict[str, Any]:
        """审计规则2：申诉提审一次性原则"""
        logger.info("🔍 审计规则2：申诉提审一次性原则")
        
        # 查找多次申诉的计划
        multiple_appeals_query = """
        SELECT 
            campaign_id_qc,
            appeal_attempt_count,
            appeal_started_at,
            appeal_completed_at,
            appeal_status
        FROM campaigns 
        WHERE appeal_attempt_count > 1
        ORDER BY appeal_attempt_count DESC;
        """
        
        multiple_appeals_results = execute_sql_query(multiple_appeals_query)
        
        # 查找状态异常的计划（已提审但没有开始时间）
        status_anomaly_query = """
        SELECT 
            campaign_id_qc,
            appeal_status,
            appeal_started_at,
            appeal_completed_at
        FROM campaigns 
        WHERE appeal_status IN ('appeal_submitted', 'appeal_approved', 'appeal_rejected')
        AND appeal_started_at IS NULL;
        """
        
        anomaly_results = execute_sql_query(status_anomaly_query)
        
        # 统计总计划数
        total_campaigns_query = """
        SELECT COUNT(*) as total_campaigns
        FROM campaigns;
        """
        
        total_results = execute_sql_query(total_campaigns_query)
        total_campaigns = int(total_results[0]['parts'][0]) if total_results else 0
        
        violations_count = len(multiple_appeals_results) + len(anomaly_results)
        
        audit_result = {
            'rule_name': '申诉提审一次性原则',
            'status': 'PASS' if violations_count == 0 else 'FAIL',
            'violations_count': violations_count,
            'total_campaigns_checked': total_campaigns,
            'multiple_appeals': len(multiple_appeals_results),
            'status_anomalies': len(anomaly_results),
            'violation_details': {
                'multiple_appeals': multiple_appeals_results,
                'status_anomalies': anomaly_results
            },
            'severity': 'HIGH' if violations_count > 0 else 'NONE'
        }
        
        if violations_count == 0:
            logger.success(f"✅ 规则2审计通过：{total_campaigns}个计划严格遵循一次性申诉原则")
        else:
            logger.error(f"❌ 规则2审计失败：发现{violations_count}个违规")
            if multiple_appeals_results:
                logger.error(f"   多次申诉: {len(multiple_appeals_results)}个计划")
            if anomaly_results:
                logger.error(f"   状态异常: {len(anomaly_results)}个计划")
        
        return audit_result
    
    def audit_rule_3_harvest_precision(self) -> Dict[str, Any]:
        """审计规则3：收割动作精确控制"""
        logger.info("🔍 审计规则3：收割动作精确控制")
        
        # 查找可能被重复收割的素材（通过状态变更历史）
        # 注意：这个查询可能需要根据实际的状态变更记录表调整
        premature_harvest_query = """
        SELECT 
            lc.filename,
            lc.file_hash,
            lc.status as material_status,
            c.campaign_id_qc,
            c.appeal_status,
            c.appeal_completed_at,
            c.created_at
        FROM local_creatives lc
        JOIN platform_creatives pc ON lc.id = pc.local_creative_id
        JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
        JOIN campaigns c ON cpca.campaign_id = c.id
        WHERE lc.status = 'rejected'
        AND c.appeal_status IN ('appeal_submitted', 'appeal_pending')
        AND c.appeal_completed_at IS NULL;
        """
        
        premature_results = execute_sql_query(premature_harvest_query)
        
        # 统计已收割素材总数
        total_harvested_query = """
        SELECT COUNT(*) as total_harvested
        FROM local_creatives 
        WHERE status = 'rejected';
        """
        
        total_results = execute_sql_query(total_harvested_query)
        total_harvested = int(total_results[0]['parts'][0]) if total_results else 0
        
        violations_count = len(premature_results)
        
        audit_result = {
            'rule_name': '收割动作精确控制',
            'status': 'PASS' if violations_count == 0 else 'FAIL',
            'violations_count': violations_count,
            'total_harvested_materials': total_harvested,
            'premature_harvests': len(premature_results),
            'violation_details': premature_results,
            'severity': 'MEDIUM' if violations_count > 0 else 'NONE'
        }
        
        if violations_count == 0:
            logger.success(f"✅ 规则3审计通过：{total_harvested}个收割素材时机精确控制")
        else:
            logger.warning(f"⚠️ 规则3审计发现问题：{violations_count}个可能的过早收割")
            for violation in premature_results:
                parts = violation['parts']
                if len(parts) >= 4:
                    logger.warning(f"   素材: {parts[0]} 已收割但计划{parts[3]}仍在申诉中")
        
        return audit_result
    
    def run_comprehensive_audit(self) -> Dict[str, Any]:
        """运行全面的业务规则审计"""
        logger.info("🛡️ 开始全面业务规则数据库审计...")
        
        audit_start = datetime.now()
        
        # 审计所有规则
        rule1_result = self.audit_rule_1_material_uniqueness()
        rule2_result = self.audit_rule_2_appeal_once_only()
        rule3_result = self.audit_rule_3_harvest_precision()
        
        audit_duration = (datetime.now() - audit_start).total_seconds()
        
        # 汇总结果
        all_results = [rule1_result, rule2_result, rule3_result]
        total_violations = sum(result['violations_count'] for result in all_results)
        
        comprehensive_result = {
            'audit_time': audit_start,
            'duration_seconds': audit_duration,
            'overall_status': 'PASS' if total_violations == 0 else 'FAIL',
            'total_violations': total_violations,
            'rules_results': {
                'rule_1_material_uniqueness': rule1_result,
                'rule_2_appeal_once_only': rule2_result,
                'rule_3_harvest_precision': rule3_result
            },
            'summary': {
                'rules_passed': len([r for r in all_results if r['status'] == 'PASS']),
                'rules_failed': len([r for r in all_results if r['status'] == 'FAIL']),
                'total_rules': len(all_results),
                'high_severity_violations': len([r for r in all_results if r.get('severity') == 'HIGH']),
                'medium_severity_violations': len([r for r in all_results if r.get('severity') == 'MEDIUM'])
            }
        }
        
        # 输出审计摘要
        self.print_audit_summary(comprehensive_result)
        
        return comprehensive_result
    
    def print_audit_summary(self, result: Dict[str, Any]):
        """打印审计摘要"""
        logger.info("=" * 60)
        logger.info("🛡️ 千川业务规则数据库审计报告")
        logger.info("=" * 60)
        logger.info(f"审计时间: {result['audit_time'].strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"审计耗时: {result['duration_seconds']:.2f} 秒")
        logger.info(f"总体状态: {result['overall_status']}")
        logger.info(f"总违规数: {result['total_violations']}")
        
        if result['overall_status'] == 'PASS':
            logger.success("🎉 所有业务规则审计通过！数据库状态完全合规！")
        else:
            logger.error(f"❌ 发现 {result['total_violations']} 个规则违规")
            
            summary = result['summary']
            if summary['high_severity_violations'] > 0:
                logger.error(f"   🚨 高严重性违规: {summary['high_severity_violations']} 个")
            if summary['medium_severity_violations'] > 0:
                logger.warning(f"   ⚠️ 中等严重性违规: {summary['medium_severity_violations']} 个")
        
        # 规则审计详情
        logger.info("📋 规则审计详情:")
        for rule_key, rule_result in result['rules_results'].items():
            status_icon = "✅" if rule_result['status'] == 'PASS' else "❌"
            logger.info(f"   {status_icon} {rule_result['rule_name']}: {rule_result['status']}")
            
            if rule_result['violations_count'] > 0:
                severity_icon = "🚨" if rule_result.get('severity') == 'HIGH' else "⚠️"
                logger.info(f"      {severity_icon} 违规数量: {rule_result['violations_count']}")
        
        # 统计摘要
        summary = result['summary']
        logger.info(f"📊 审计统计: {summary['rules_passed']}/{summary['total_rules']} 规则通过")
        
        logger.info("=" * 60)


def main():
    """主函数"""
    auditor = RealBusinessRulesAuditor()
    
    print("🛡️ 千川业务规则数据库审计器")
    print("=" * 50)
    print("审计规则:")
    print("1. 素材唯一性测试铁律")
    print("2. 申诉提审一次性原则")
    print("3. 收割动作精确控制")
    print("=" * 50)
    
    # 运行全面审计
    audit_result = auditor.run_comprehensive_audit()
    
    # 生成详细报告
    report_content = f"""
千川业务规则数据库审计报告
========================

审计时间: {audit_result['audit_time'].strftime('%Y-%m-%d %H:%M:%S')}
审计耗时: {audit_result['duration_seconds']:.2f} 秒
总体状态: {audit_result['overall_status']}
总违规数: {audit_result['total_violations']}

规则审计结果:
"""
    
    for rule_key, rule_result in audit_result['rules_results'].items():
        report_content += f"""
{rule_result['rule_name']}:
- 状态: {rule_result['status']}
- 违规数: {rule_result['violations_count']}
- 严重程度: {rule_result.get('severity', 'NONE')}
"""
        
        # 添加具体违规详情
        if rule_result['violations_count'] > 0 and 'violation_details' in rule_result:
            report_content += "- 违规详情:\n"
            for detail in rule_result['violation_details'][:5]:  # 只显示前5个
                report_content += f"  * {detail['raw']}\n"
    
    # 保存报告
    report_file = os.path.join(
        project_root,
        'ai_reports',
        'business_rules',
        f'database_audit_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt'
    )
    os.makedirs(os.path.dirname(report_file), exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"\n📊 审计报告已保存: {report_file}")
    
    return 0 if audit_result['overall_status'] == 'PASS' else 1


if __name__ == '__main__':
    exit(main())
