#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 测试计划创建修复效果
清理条件: 成为计划创建验证工具，长期保留
"""

import os
import sys
from datetime import datetime, timezone

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.database import SessionLocal
from qianchuan_aw.database.models import LocalCreative, PlatformCreative, Principal


class PlanCreationFixTester:
    """计划创建修复测试器"""
    
    def __init__(self):
        self.test_results = {}
    
    def run_comprehensive_test(self):
        """运行全面测试"""
        logger.info("🧪 计划创建修复测试")
        logger.info("="*100)
        
        # 1. 检查修复前状态
        before_status = self._check_current_status()
        
        # 2. 验证修复代码
        code_fix_verified = self._verify_code_fix()
        
        # 3. 手动触发计划创建任务
        task_result = self._trigger_plan_creation_task()
        
        # 4. 检查修复后状态
        after_status = self._check_current_status()
        
        # 5. 生成测试报告
        self._generate_test_report({
            'before_status': before_status,
            'code_fix_verified': code_fix_verified,
            'task_result': task_result,
            'after_status': after_status
        })
    
    def _check_current_status(self):
        """检查当前状态"""
        logger.info("📊 检查当前状态...")
        
        with SessionLocal() as db:
            # 检查缇萃百货的状态分布
            ticui_stats = db.execute("""
                SELECT 
                    lc.status,
                    COUNT(*) as count,
                    COUNT(pc.id) as has_platform_creative
                FROM local_creatives lc
                LEFT JOIN platform_creatives pc ON pc.local_creative_id = lc.id
                JOIN principals p ON lc.principal_id = p.id
                WHERE p.name = '缇萃百货'
                GROUP BY lc.status
                ORDER BY count DESC
            """).fetchall()
            
            status_summary = {}
            uploaded_pending_plan_count = 0
            uploaded_with_pc_count = 0
            uploaded_without_pc_count = 0
            
            for row in ticui_stats:
                status = row[0]
                count = row[1]
                has_pc = row[2]
                
                status_summary[status] = {
                    'count': count,
                    'has_platform_creative': has_pc
                }
                
                if status == 'uploaded_pending_plan':
                    uploaded_pending_plan_count = count
                    uploaded_with_pc_count = has_pc
                    uploaded_without_pc_count = count - has_pc
            
            logger.info(f"📋 缇萃百货状态分布:")
            for status, data in status_summary.items():
                logger.info(f"   📄 {status}: {data['count']} 个 (有PlatformCreative: {data['has_platform_creative']})")
            
            logger.info(f"\n🎯 关键指标:")
            logger.info(f"   uploaded_pending_plan总数: {uploaded_pending_plan_count}")
            logger.info(f"   有PlatformCreative记录: {uploaded_with_pc_count}")
            logger.info(f"   缺少PlatformCreative记录: {uploaded_without_pc_count}")
            
            return {
                'uploaded_pending_plan_count': uploaded_pending_plan_count,
                'uploaded_with_pc_count': uploaded_with_pc_count,
                'uploaded_without_pc_count': uploaded_without_pc_count,
                'status_summary': status_summary
            }
    
    def _verify_code_fix(self):
        """验证代码修复"""
        logger.info("🔧 验证handle_plan_creation修复...")
        
        try:
            scheduler_file = os.path.join(project_root, 'src/qianchuan_aw/workflows/scheduler.py')
            
            with open(scheduler_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查修复的关键代码
            fix_checks = {
                'direct_local_creative_query': 'db.query(LocalCreative).filter(' in content and 'uploaded_pending_plan' in content,
                'platform_creative_lookup': 'existing_pc = db.query(PlatformCreative).filter(' in content,
                'missing_pc_warning': '缺少PlatformCreative记录' in content,
                'emergency_fix_comment': '紧急修复' in content,
                'actual_available_logging': '实际可用于计划创建的素材' in content
            }
            
            fixes_applied = sum(fix_checks.values())
            total_checks = len(fix_checks)
            
            logger.info(f"📊 代码修复检查: {fixes_applied}/{total_checks}")
            
            for check_name, passed in fix_checks.items():
                status_icon = "✅" if passed else "❌"
                logger.info(f"   {status_icon} {check_name}: {'通过' if passed else '失败'}")
            
            if fixes_applied >= 4:
                logger.success("✅ handle_plan_creation修复验证通过")
                return True
            else:
                logger.error("❌ handle_plan_creation修复不完整")
                return False
                
        except Exception as e:
            logger.error(f"❌ 验证代码修复失败: {e}")
            return False
    
    def _trigger_plan_creation_task(self):
        """手动触发计划创建任务"""
        logger.info("🚀 手动触发batch_create_plans任务...")
        
        try:
            from qianchuan_aw.workflows.tasks import batch_create_plans
            
            # 提交任务
            result = batch_create_plans.delay(batch_size=5)
            logger.info(f"✅ 任务已提交，任务ID: {result.id}")
            
            # 等待任务完成
            import time
            time.sleep(15)  # 等待15秒
            
            # 检查任务结果
            if result.ready():
                task_result = result.get()
                logger.info(f"📋 任务结果: {task_result}")
                
                if task_result and task_result.get('success'):
                    processed = task_result.get('processed', 0)
                    remaining = task_result.get('remaining', 0)
                    
                    logger.success(f"✅ 任务执行成功，处理了 {processed} 个素材")
                    logger.info(f"📊 剩余待处理: {remaining} 个素材")
                    
                    return {
                        'task_success': True,
                        'processed': processed,
                        'remaining': remaining
                    }
                else:
                    logger.warning("⚠️ 任务执行但结果不明确")
                    return {'task_success': False, 'result': task_result}
            else:
                logger.warning("⏳ 任务仍在执行中...")
                return {'task_success': None, 'status': 'running'}
                
        except Exception as e:
            logger.error(f"❌ 触发任务失败: {e}")
            return {'task_success': False, 'error': str(e)}
    
    def _generate_test_report(self, results):
        """生成测试报告"""
        logger.info("\n📋 计划创建修复测试报告")
        logger.info("="*100)
        
        before_status = results['before_status']
        code_fix_verified = results['code_fix_verified']
        task_result = results['task_result']
        after_status = results['after_status']
        
        # 修复前状态
        logger.info("📊 修复前状态:")
        logger.info(f"   uploaded_pending_plan: {before_status['uploaded_pending_plan_count']} 个")
        logger.info(f"   有PlatformCreative: {before_status['uploaded_with_pc_count']} 个")
        logger.info(f"   缺少PlatformCreative: {before_status['uploaded_without_pc_count']} 个")
        
        # 代码修复验证
        if code_fix_verified:
            logger.success("✅ 代码修复: handle_plan_creation修复验证通过")
        else:
            logger.error("❌ 代码修复: handle_plan_creation修复验证失败")
        
        # 任务执行结果
        if task_result['task_success']:
            processed = task_result.get('processed', 0)
            logger.success(f"✅ 任务执行: 成功处理 {processed} 个素材")
        elif task_result['task_success'] is None:
            logger.warning("⏳ 任务执行: 仍在运行中")
        else:
            logger.error("❌ 任务执行: 失败")
        
        # 修复后状态变化
        uploaded_change = before_status['uploaded_pending_plan_count'] - after_status['uploaded_pending_plan_count']
        
        logger.info(f"\n📈 状态变化:")
        logger.info(f"   uploaded_pending_plan 减少: {uploaded_change} 个")
        
        if uploaded_change > 0:
            logger.success(f"✅ 计划创建正在工作，{uploaded_change} 个素材状态已改变")
        else:
            logger.warning("⚠️ 没有检测到状态变化，可能需要更多时间")
        
        # 总体评估
        success_indicators = [
            code_fix_verified,
            task_result.get('task_success', False),
            uploaded_change > 0 or before_status['uploaded_without_pc_count'] == 0
        ]
        
        success_count = sum(success_indicators)
        
        logger.info(f"\n🎯 修复成功指标: {success_count}/3")
        
        if success_count >= 2:
            logger.success("🎊 计划创建修复测试成功")
            logger.success("💡 建议：继续观察后续批次的处理效果")
            
            if before_status['uploaded_without_pc_count'] > 0:
                logger.info(f"\n💡 注意事项:")
                logger.info(f"   仍有 {before_status['uploaded_without_pc_count']} 个素材缺少PlatformCreative记录")
                logger.info(f"   这些素材将被跳过，不会创建计划")
                logger.info(f"   如需处理，可考虑补创建PlatformCreative记录")
            
            return True
        else:
            logger.error("❌ 计划创建修复测试失败")
            logger.error("🔧 需要进一步检查和修复")
            return False


def main():
    """主函数"""
    tester = PlanCreationFixTester()
    
    logger.info("🚀 启动计划创建修复测试")
    logger.info("🎯 目标：验证381个uploaded_pending_plan素材能否创建计划")
    
    success = tester.run_comprehensive_test()
    
    if success:
        logger.success("🎊 计划创建修复测试完成")
        logger.success("💡 建议：继续观察计划创建进度")
        return 0
    else:
        logger.error("❌ 计划创建修复测试失败")
        logger.error("🔧 建议：检查修复代码并重新测试")
        return 1


if __name__ == "__main__":
    exit(main())
