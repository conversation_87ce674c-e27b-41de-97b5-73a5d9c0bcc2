
                # [增强重复检查] 基于file_hash的严格唯一性检查
                # 这是最可靠的重复检测方法，因为file_hash是唯一的
                existing_campaigns_by_hash = db.query(Campaign).join(
                    campaign_platform_creative_association
                ).join(PlatformCreative).join(LocalCreative).filter(
                    LocalCreative.file_hash == locked_creative.file_hash
                ).count()
                
                if existing_campaigns_by_hash > 0:
                    # 基于file_hash发现重复计划，这是最严格的检查
                    skipped_creatives.append(pc)
                    logger.warning(f"素材 {os.path.basename(locked_creative.file_path)} (hash: {locked_creative.file_hash[:8]}...) 已有测试计划，跳过重复创建")
                    
                    # 将状态更新为已测试
                    locked_creative.status = 'already_tested'
                    db.commit()
                    continue
                
                # [原有检查] 通过多种方式检查是否已有测试计划
                # 方法1: 通过关联表检查
                existing_campaigns_via_association = db.query(Campaign).join(
                    campaign_platform_creative_association
                ).join(PlatformCreative).filter(
                    PlatformCreative.local_creative_id == locked_creative.id
                ).count()

                # 方法2: 直接检查该素材的所有平台素材是否有关联的计划
                platform_creatives_for_this_local = db.query(PlatformCreative).filter(
                    PlatformCreative.local_creative_id == locked_creative.id
                ).all()

                has_existing_campaigns = False
                for pc_check in platform_creatives_for_this_local:
                    if pc_check.campaigns:  # 如果有关联的计划
                        has_existing_campaigns = True
                        break

                if existing_campaigns_via_association > 0 or has_existing_campaigns:
                    # 该素材已经创建过测试计划，跳过
                    skipped_creatives.append(pc)
                    logger.warning(f"素材 {os.path.basename(locked_creative.file_path)} 已创建过测试计划，跳过重复测试")

                    # 将状态更新为特殊状态，表示已测试过
                    locked_creative.status = 'already_tested'
                    db.commit()  # 立即提交状态更新

                else:
                    # [最终检查] 在创建前再次检查，防止竞态条件
                    final_check = db.query(Campaign).join(
                        campaign_platform_creative_association
                    ).join(PlatformCreative).join(LocalCreative).filter(
                        LocalCreative.file_hash == locked_creative.file_hash
                    ).count()
                    
                    if final_check > 0:
                        logger.warning(f"素材 {os.path.basename(locked_creative.file_path)} 在最终检查时发现已有计划，跳过")
                        locked_creative.status = 'already_tested'
                        db.commit()
                        continue
                    
                    # 该素材从未创建过测试计划，可以进行测试
                    # 先将状态标记为 'creating_plan' 防止其他进程重复处理
                    locked_creative.status = 'creating_plan'
                    db.commit()  # 立即提交状态更新
                    final_creatives_to_build.append(pc)
                    logger.info(f"素材 {os.path.basename(locked_creative.file_path)} 通过所有重复检查，标记为创建中")
