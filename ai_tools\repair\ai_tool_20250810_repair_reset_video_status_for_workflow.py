"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 重置8月9日视频状态，让它们重新进入完整工作流（上传→创建计划→提审→收割）
清理条件: 成为项目核心工具后永久保留
"""

import sys
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.qianchuan_aw.database.database import SessionLocal
from src.qianchuan_aw.database.models import LocalCreative, PlatformCreative, AdAccount, Principal
from src.qianchuan_aw.utils.config_loader import load_settings
from src.qianchuan_aw.utils.logger import logger


class VideoWorkflowResetTool:
    """重置视频状态，让它们进入完整工作流"""
    
    def __init__(self):
        self.app_settings = load_settings()
        self.reset_stats = {
            'found_videos': 0,
            'reset_to_pending': 0,
            'moved_to_process_dir': 0,
            'deleted_invalid_records': 0,
            'errors': 0
        }
        
        logger.info("🔧 初始化视频工作流重置工具")
    
    def find_target_videos(self, db) -> List[Dict[str, Any]]:
        """查找需要重置的8月9日视频"""
        
        logger.info("🔍 查找需要重置的8月9日视频...")
        
        # 查找8月9日的视频，特别是状态异常的
        target_videos = db.query(LocalCreative).filter(
            LocalCreative.filename.like('%8.9-%') | LocalCreative.filename.like('%7.24%'),
            LocalCreative.status.in_(['uploaded_pending_plan', 'pending_upload'])
        ).all()
        
        result = []
        for creative in target_videos:
            result.append({
                'id': creative.id,
                'filename': creative.filename,
                'file_path': creative.file_path,
                'status': creative.status,
                'principal_id': creative.principal_id,
                'principal_name': creative.principal.name if creative.principal else 'Unknown'
            })
        
        self.reset_stats['found_videos'] = len(result)
        logger.info(f"📊 找到 {len(result)} 个需要重置的视频")
        
        return result
    
    def reset_video_status(self, db, video_info: Dict[str, Any]) -> bool:
        """重置视频状态为pending_upload"""
        
        try:
            creative_id = video_info['id']
            filename = video_info['filename']
            current_status = video_info['status']
            
            # 获取素材记录
            creative = db.get(LocalCreative, creative_id)
            if not creative:
                logger.warning(f"❌ 素材 {filename} 不存在")
                return False
            
            # 删除可能存在的无效PlatformCreative记录
            invalid_platform_creatives = db.query(PlatformCreative).filter(
                PlatformCreative.local_creative_id == creative_id
            ).all()
            
            for pc in invalid_platform_creatives:
                db.delete(pc)
                self.reset_stats['deleted_invalid_records'] += 1
                logger.info(f"🗑️ 删除素材 {filename} 的无效PlatformCreative记录")
            
            # 重置状态
            if current_status != 'pending_upload':
                creative.status = 'pending_upload'
                self.reset_stats['reset_to_pending'] += 1
                logger.info(f"🔄 重置素材 {filename} 状态: {current_status} → pending_upload")
            
            # 如果文件在存档目录，移动到待处理目录
            if '00_uploaded_archive' in creative.file_path:
                new_path = self.move_to_process_directory(creative.file_path, video_info['principal_name'])
                if new_path:
                    creative.file_path = new_path
                    self.reset_stats['moved_to_process_dir'] += 1
                    logger.info(f"📁 移动素材 {filename} 到待处理目录")
            
            db.commit()
            return True
            
        except Exception as e:
            logger.error(f"❌ 重置素材 {filename} 失败: {e}")
            self.reset_stats['errors'] += 1
            db.rollback()
            return False
    
    def move_to_process_directory(self, current_path: str, principal_name: str) -> str:
        """将文件从存档目录移动到待处理目录"""
        
        try:
            current_path_obj = Path(current_path)
            if not current_path_obj.exists():
                logger.warning(f"⚠️ 源文件不存在: {current_path}")
                return None
            
            # 构建目标路径
            filename = current_path_obj.name
            target_dir = Path("D:/workflow_assets/01_materials_to_process") / principal_name
            target_dir.mkdir(parents=True, exist_ok=True)
            
            target_path = target_dir / filename
            
            # 如果目标文件已存在，跳过
            if target_path.exists():
                logger.info(f"📁 文件已存在于待处理目录: {filename}")
                return str(target_path)
            
            # 复制文件（保留原文件）
            import shutil
            shutil.copy2(current_path, target_path)
            
            logger.info(f"📁 复制文件: {filename} → 01_materials_to_process")
            return str(target_path)
            
        except Exception as e:
            logger.error(f"❌ 移动文件失败: {current_path} - {e}")
            return None
    
    def trigger_workflow_tasks(self):
        """触发工作流任务处理这些视频"""
        
        logger.info("🚀 触发工作流任务处理重置的视频...")
        
        try:
            # 触发文件摄取任务
            from src.qianchuan_aw.workflows.tasks import task_ingest_and_upload
            task_ingest_and_upload.delay()
            logger.success("✅ 已触发文件摄取任务 (ingest_and_upload)")
            
            # 触发分组派发任务
            from src.qianchuan_aw.workflows.tasks import task_group_and_dispatch
            task_group_and_dispatch.delay()
            logger.success("✅ 已触发分组派发任务 (group_and_dispatch)")
            
        except Exception as e:
            logger.error(f"❌ 触发工作流任务失败: {e}")
    
    def execute_reset(self):
        """执行重置操作"""
        
        logger.info("🚀 开始重置8月9日视频状态，让它们进入完整工作流")
        logger.info("=" * 80)
        
        with SessionLocal() as db:
            # 1. 查找目标视频
            target_videos = self.find_target_videos(db)
            
            if not target_videos:
                logger.info("✅ 没有发现需要重置的视频")
                return
            
            # 2. 按状态分类
            uploaded_pending_videos = [v for v in target_videos if v['status'] == 'uploaded_pending_plan']
            pending_upload_videos = [v for v in target_videos if v['status'] == 'pending_upload']
            
            logger.info(f"📊 视频分类统计:")
            logger.info(f"   uploaded_pending_plan (需要重置): {len(uploaded_pending_videos)} 个")
            logger.info(f"   pending_upload (状态正确): {len(pending_upload_videos)} 个")
            
            # 3. 重置uploaded_pending_plan状态的视频
            logger.info(f"\n🔄 重置状态异常的视频...")
            for video_info in uploaded_pending_videos:
                self.reset_video_status(db, video_info)
            
            # 4. 确保pending_upload视频在正确目录
            logger.info(f"\n📁 检查pending_upload视频的文件位置...")
            for video_info in pending_upload_videos:
                if '00_uploaded_archive' in video_info['file_path']:
                    self.reset_video_status(db, video_info)
        
        # 5. 触发工作流任务
        self.trigger_workflow_tasks()
        
        # 6. 输出重置统计
        self.print_reset_summary()
    
    def print_reset_summary(self):
        """输出重置统计"""
        
        logger.info(f"\n📊 视频工作流重置统计")
        logger.info("=" * 80)
        logger.info(f"⏰ 重置时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"🎯 重置范围: 8月9日和7月24日视频素材")
        
        logger.info(f"\n📋 重置结果:")
        logger.info(f"   发现视频: {self.reset_stats['found_videos']} 个")
        logger.info(f"   重置为pending_upload: {self.reset_stats['reset_to_pending']} 个")
        logger.info(f"   移动到待处理目录: {self.reset_stats['moved_to_process_dir']} 个")
        logger.info(f"   删除无效记录: {self.reset_stats['deleted_invalid_records']} 个")
        logger.info(f"   处理错误: {self.reset_stats['errors']} 个")
        
        success_rate = ((self.reset_stats['reset_to_pending'] + self.reset_stats['moved_to_process_dir']) / 
                       max(self.reset_stats['found_videos'], 1)) * 100
        
        logger.info(f"\n✅ 重置成功率: {success_rate:.1f}%")
        
        logger.info(f"\n💡 后续工作流程:")
        logger.info(f"   1. ✅ 文件摄取任务已触发 (ingest_and_upload)")
        logger.info(f"   2. ✅ 分组派发任务已触发 (group_and_dispatch)")
        logger.info(f"   3. 🔄 工作流将自动执行: 上传 → 创建计划 → 提审 → 收割")
        logger.info(f"   4. 📊 监控工作流日志查看进度")


def main():
    """主函数"""
    
    logger.info("🚨 重置8月9日视频状态，让它们进入完整工作流")
    logger.info("=" * 80)
    
    logger.info("🎯 重置目标:")
    logger.info("   1. 重置错误状态的视频为pending_upload")
    logger.info("   2. 移动文件到正确的待处理目录")
    logger.info("   3. 删除无效的PlatformCreative记录")
    logger.info("   4. 触发工作流任务开始完整流程")
    
    try:
        reset_tool = VideoWorkflowResetTool()
        reset_tool.execute_reset()
        
        logger.success("🎉 视频状态重置完成！")
        logger.info("💡 现在这些视频将进入完整的工作流程：上传→创建计划→提审→收割")
        
    except Exception as e:
        logger.error(f"❌ 重置过程发生严重错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
