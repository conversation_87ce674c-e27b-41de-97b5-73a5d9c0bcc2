#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 修复收割逻辑的账户类型过滤BUG
清理条件: BUG修复并验证后可考虑归档
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session, joinedload
from sqlalchemy import or_, and_, func, desc, text
from src.qianchuan_aw.database.database import SessionLocal
from src.qianchuan_aw.database.models import LocalCreative, PlatformCreative, Campaign, AdAccount, Principal
from src.qianchuan_aw.utils.logger import logger
from datetime import datetime, timezone, timedelta
import yaml
from pathlib import Path
import shutil

def load_app_settings():
    """加载应用配置"""
    try:
        with open('config/settings.yml', 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except Exception as e:
        logger.error(f"加载配置失败: {e}")
        return {}

def analyze_harvest_bug():
    """分析收割BUG的具体情况"""
    logger.info("🔍 分析收割BUG的具体情况")
    logger.info("="*80)
    
    with SessionLocal() as db:
        # 查找所有approved状态的素材及其账户类型
        approved_materials = db.query(LocalCreative).options(
            joinedload(LocalCreative.platform_creatives).joinedload(PlatformCreative.account)
        ).filter(
            LocalCreative.status == 'approved'
        ).all()
        
        # 按账户类型分组
        account_type_stats = {}
        test_materials = []
        delivery_materials = []
        
        for material in approved_materials:
            for pc in material.platform_creatives:
                if pc.account:
                    account_type = pc.account.account_type
                    if account_type not in account_type_stats:
                        account_type_stats[account_type] = []
                    account_type_stats[account_type].append(material)
                    
                    if account_type == 'TEST':
                        test_materials.append(material)
                    elif account_type == 'DELIVERY':
                        delivery_materials.append(material)
        
        logger.info(f"📊 approved状态素材按账户类型分析:")
        for account_type, materials in account_type_stats.items():
            logger.info(f"   {account_type}: {len(materials)} 个素材")
        
        logger.info(f"\n🎯 关键发现:")
        logger.info(f"   TEST账户素材: {len(test_materials)} 个 (应该收割)")
        logger.info(f"   DELIVERY账户素材: {len(delivery_materials)} 个 (不应该收割)")
        
        return {
            'test_materials': test_materials,
            'delivery_materials': delivery_materials,
            'account_type_stats': account_type_stats
        }

def fix_scheduler_harvest_logic():
    """修复scheduler.py中的收割逻辑，添加账户类型过滤"""
    logger.info("🔧 修复scheduler.py中的收割逻辑")
    logger.info("="*80)
    
    scheduler_file = 'src/qianchuan_aw/workflows/scheduler.py'
    
    try:
        # 备份原文件
        backup_file = f"{scheduler_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(scheduler_file, backup_file)
        logger.info(f"✅ 已备份原文件到: {backup_file}")
        
        # 读取文件内容
        with open(scheduler_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找需要修改的位置
        target_line = "if audit_status == 'PASS':"
        replacement_block = '''if audit_status == 'PASS':
                    # 🚨 [BUG修复] 添加账户类型过滤 - 只收割测试账户素材
                    if pc.account.account_type != 'TEST':
                        logger.debug(f"跳过非测试账户素材收割: {material_id} (账户类型: {pc.account.account_type})")
                        continue
                    
                    if pc.local_creative.status != 'approved':
                        logger.info(f"收割测试账户素材: {material_id} (账户: {pc.account.name})")
                        _add_to_approved_library(db, pc, app_settings)'''
        
        # 查找并替换
        old_block = '''if audit_status == 'PASS':
                    if pc.local_creative.status != 'approved':
                        _add_to_approved_library(db, pc, app_settings)'''
        
        if old_block in content:
            content = content.replace(old_block, replacement_block)
            
            # 写回文件
            with open(scheduler_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.success("✅ 成功修复scheduler.py中的收割逻辑")
            logger.info("   添加了账户类型过滤：只收割TEST账户素材")
            return True
        else:
            logger.error("❌ 未找到预期的代码块，可能需要手动修改")
            return False
            
    except Exception as e:
        logger.error(f"❌ 修复scheduler.py失败: {e}")
        return False

def sync_database_harvest_status():
    """同步数据库收割状态 - 只将TEST账户的approved素材标记为harvested"""
    logger.info("🔄 同步数据库收割状态")
    logger.info("="*80)
    
    app_settings = load_app_settings()
    harvest_dir = app_settings.get('custom_workflow_assets_dir', 'D:/workflow_assets')
    harvest_path = os.path.join(harvest_dir, '03_materials_approved')
    
    if not os.path.exists(harvest_path):
        logger.error(f"❌ 收割目录不存在: {harvest_path}")
        return False
    
    # 收集收割目录中的文件
    harvest_files = {}
    for root, dirs, files in os.walk(harvest_path):
        for file in files:
            if file.endswith('.mp4'):
                harvest_files[file] = os.path.join(root, file)
    
    logger.info(f"📊 收割目录中有 {len(harvest_files)} 个文件")
    
    with SessionLocal() as db:
        # 查找所有approved状态的TEST账户素材
        test_approved_materials = db.query(LocalCreative).options(
            joinedload(LocalCreative.platform_creatives).joinedload(PlatformCreative.account)
        ).filter(
            LocalCreative.status == 'approved'
        ).all()
        
        # 过滤出TEST账户的素材
        test_materials_to_update = []
        delivery_materials_found = []
        
        for material in test_approved_materials:
            is_test_material = False
            is_delivery_material = False
            
            for pc in material.platform_creatives:
                if pc.account:
                    if pc.account.account_type == 'TEST':
                        is_test_material = True
                    elif pc.account.account_type == 'DELIVERY':
                        is_delivery_material = True
            
            if is_test_material:
                # 检查文件是否在收割目录中
                if material.filename and material.filename in harvest_files:
                    test_materials_to_update.append(material)
            
            if is_delivery_material:
                delivery_materials_found.append(material)
        
        logger.info(f"📊 分析结果:")
        logger.info(f"   TEST账户已收割素材: {len(test_materials_to_update)} 个")
        logger.info(f"   DELIVERY账户错误收割: {len(delivery_materials_found)} 个")
        
        # 更新TEST账户素材状态为harvested
        updated_count = 0
        for material in test_materials_to_update:
            try:
                material.status = 'harvested'
                # 更新文件路径为收割目录路径
                if material.filename in harvest_files:
                    material.file_path = harvest_files[material.filename]
                updated_count += 1
                logger.debug(f"更新素材状态: {material.filename} -> harvested")
            except Exception as e:
                logger.error(f"更新素材 {material.id} 状态失败: {e}")
        
        # 提交更改
        try:
            db.commit()
            logger.success(f"✅ 成功更新 {updated_count} 个TEST账户素材状态为harvested")
        except Exception as e:
            db.rollback()
            logger.error(f"❌ 数据库提交失败: {e}")
            return False
        
        return True

def clean_delivery_account_harvest_files():
    """清理DELIVERY账户错误收割的文件"""
    logger.info("🧹 清理DELIVERY账户错误收割的文件")
    logger.info("="*80)
    
    app_settings = load_app_settings()
    harvest_dir = app_settings.get('custom_workflow_assets_dir', 'D:/workflow_assets')
    harvest_path = os.path.join(harvest_dir, '03_materials_approved')
    
    with SessionLocal() as db:
        # 查找DELIVERY账户的approved素材
        delivery_materials = db.query(LocalCreative).options(
            joinedload(LocalCreative.platform_creatives).joinedload(PlatformCreative.account)
        ).filter(
            LocalCreative.status == 'approved'
        ).all()
        
        delivery_files_to_remove = []
        
        for material in delivery_materials:
            for pc in material.platform_creatives:
                if pc.account and pc.account.account_type == 'DELIVERY':
                    if material.filename:
                        # 查找收割目录中的对应文件
                        for root, dirs, files in os.walk(harvest_path):
                            if material.filename in files:
                                file_path = os.path.join(root, material.filename)
                                delivery_files_to_remove.append({
                                    'file_path': file_path,
                                    'material': material,
                                    'account_name': pc.account.name
                                })
        
        logger.info(f"📊 发现 {len(delivery_files_to_remove)} 个DELIVERY账户错误收割文件")
        
        if delivery_files_to_remove:
            # 创建清理目录
            cleanup_dir = os.path.join(harvest_dir, 'cleanup_delivery_files')
            os.makedirs(cleanup_dir, exist_ok=True)
            
            removed_count = 0
            for item in delivery_files_to_remove:
                try:
                    file_path = item['file_path']
                    material = item['material']
                    account_name = item['account_name']
                    
                    # 移动文件到清理目录而不是删除
                    cleanup_file_path = os.path.join(cleanup_dir, os.path.basename(file_path))
                    shutil.move(file_path, cleanup_file_path)
                    
                    logger.info(f"移动DELIVERY账户文件: {material.filename} (账户: {account_name})")
                    removed_count += 1
                    
                except Exception as e:
                    logger.error(f"移动文件失败 {item['file_path']}: {e}")
            
            logger.success(f"✅ 成功移动 {removed_count} 个DELIVERY账户错误收割文件到清理目录")
        
        return len(delivery_files_to_remove)

def verify_fix():
    """验证修复效果"""
    logger.info("✅ 验证修复效果")
    logger.info("="*80)
    
    with SessionLocal() as db:
        # 统计各状态素材数量
        status_stats = db.query(
            LocalCreative.status,
            func.count(LocalCreative.id).label('count')
        ).group_by(LocalCreative.status).all()
        
        logger.info(f"📊 修复后素材状态统计:")
        for status, count in status_stats:
            logger.info(f"   {status}: {count} 个")
        
        # 检查harvested状态的素材账户类型
        harvested_materials = db.query(LocalCreative).options(
            joinedload(LocalCreative.platform_creatives).joinedload(PlatformCreative.account)
        ).filter(
            LocalCreative.status == 'harvested'
        ).all()
        
        harvested_account_types = {}
        for material in harvested_materials:
            for pc in material.platform_creatives:
                if pc.account:
                    account_type = pc.account.account_type
                    if account_type not in harvested_account_types:
                        harvested_account_types[account_type] = 0
                    harvested_account_types[account_type] += 1
        
        logger.info(f"📊 harvested状态素材按账户类型:")
        for account_type, count in harvested_account_types.items():
            logger.info(f"   {account_type}: {count} 个")
        
        # 检查收割目录文件数量
        app_settings = load_app_settings()
        harvest_dir = app_settings.get('custom_workflow_assets_dir', 'D:/workflow_assets')
        harvest_path = os.path.join(harvest_dir, '03_materials_approved')
        
        harvest_file_count = 0
        if os.path.exists(harvest_path):
            for root, dirs, files in os.walk(harvest_path):
                harvest_file_count += len([f for f in files if f.endswith('.mp4')])
        
        logger.info(f"📊 收割目录文件数量: {harvest_file_count} 个")
        
        # 验证一致性
        harvested_db_count = sum(harvested_account_types.values())
        if harvested_db_count == harvest_file_count:
            logger.success("✅ 数据库状态与收割目录文件完全一致！")
            return True
        else:
            logger.warning(f"⚠️ 数据库harvested({harvested_db_count})与目录文件({harvest_file_count})仍不一致")
            return False

def main():
    """主函数"""
    logger.info("🚀 启动收割BUG修复工具")
    logger.info("="*80)
    logger.info("🎯 目标: 修复收割逻辑的账户类型过滤BUG")
    
    # 1. 分析BUG情况
    bug_analysis = analyze_harvest_bug()
    
    # 2. 修复scheduler.py中的收割逻辑
    if fix_scheduler_harvest_logic():
        logger.success("✅ 收割逻辑修复成功")
    else:
        logger.error("❌ 收割逻辑修复失败")
        return
    
    # 3. 同步数据库状态
    if sync_database_harvest_status():
        logger.success("✅ 数据库状态同步成功")
    else:
        logger.error("❌ 数据库状态同步失败")
        return
    
    # 4. 清理错误收割的文件
    cleaned_files = clean_delivery_account_harvest_files()
    logger.info(f"✅ 清理了 {cleaned_files} 个错误收割文件")
    
    # 5. 验证修复效果
    if verify_fix():
        logger.success("🎉 收割BUG修复完成！铁律5现在应该100%达标")
    else:
        logger.warning("⚠️ 修复可能不完整，需要进一步检查")
    
    logger.info("\n💡 修复总结:")
    logger.info("   1. ✅ 修复了scheduler.py中缺少账户类型过滤的BUG")
    logger.info("   2. ✅ 同步了TEST账户素材的数据库状态")
    logger.info("   3. ✅ 清理了DELIVERY账户错误收割的文件")
    logger.info("   4. ✅ 确保了收割逻辑只处理测试账户")

if __name__ == "__main__":
    main()
